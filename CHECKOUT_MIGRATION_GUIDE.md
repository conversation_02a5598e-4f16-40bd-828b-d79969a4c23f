# Custom Checkout Migration Guide

This guide explains how to replace your existing Stripe hosted checkout with the new custom embedded checkout system.

## What's Been Created

### New Files:
1. **`src/components/CustomCheckout.jsx`** - Main custom checkout component
2. **`src/app/checkout/page.jsx`** - Checkout page that uses the custom component
3. **`src/app/checkout/return/page.jsx`** - Success/return page for completed payments
4. **`src/app/api/checkout/embedded/route.js`** - API endpoint for embedded checkout
5. **`src/app/api/checkout/session/route.js`** - API endpoint to retrieve session status
6. **`src/utils/checkout.js`** - Utility functions for checkout redirects

### Updated Files:
1. **`src/app/actions/stripe.js`** - Enhanced with configurable fetchClientSecret
2. **`src/components/checkout.jsx`** - Updated with better styling and error handling
3. **`src/app/payment/buy-from-seller.tsx`** - Example of migration to custom checkout

## How to Migrate Your Existing Checkout Flows

### 1. Replace Hosted Checkout API Calls

**Before (Hosted Checkout):**
```javascript
// Old way - redirects to Stripe hosted page
const res = await fetch('/api/checkout', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ userId, userEmail, amount }),
});
const data = await res.json();
window.location.href = data.url; // Redirects to Stripe
```

**After (Custom Checkout):**
```javascript
// New way - redirects to your custom checkout page
import { redirectToCustomCheckout } from '../utils/checkout';

const checkoutData = {
  userId: 'user_123',
  userEmail: '<EMAIL>',
  amount: 2000, // $20.00 in cents
  currency: 'usd',
  productName: 'Your Product',
  productDescription: 'Product description',
  isEscrow: false, // or true for escrow payments
};

redirectToCustomCheckout(checkoutData);
```

### 2. Update Your Existing API Endpoints

You can keep your existing API endpoints but modify them to redirect to custom checkout instead of returning Stripe URLs:

**Example - Update `/api/checkout/route.ts`:**
```javascript
// Instead of returning session.url, return redirect info
return NextResponse.json({
  redirectToCustomCheckout: true,
  checkoutData: {
    userId,
    userEmail,
    amount,
    currency,
    productName,
    productDescription,
    isEscrow,
    sellerId,
    orderId
  }
});
```

### 3. Update Frontend Components

**For Regular Payments:**
```javascript
import { redirectToCustomCheckout } from '../utils/checkout';

const handlePayment = () => {
  redirectToCustomCheckout({
    userId: currentUser.id,
    userEmail: currentUser.email,
    amount: 2000, // $20.00
    productName: 'Your Product',
    productDescription: 'Description',
  });
};
```

**For Escrow Payments:**
```javascript
import { formatEscrowPayment } from '../utils/checkout';

const handleEscrowPayment = () => {
  const escrowData = formatEscrowPayment({
    userId: buyer.id,
    userEmail: buyer.email,
    amount: 5000, // $50.00
    productName: 'Service',
    productDescription: 'Protected by escrow',
    sellerId: seller.id,
    sellerEmail: seller.email,
    sellerStripeAccountId: seller.stripeAccountId,
    orderId: order.id,
  });
  
  redirectToCustomCheckout(escrowData);
};
```

**For Basket/Cart Checkout:**
```javascript
import { formatBasketForCheckout } from '../utils/checkout';

const handleBasketCheckout = () => {
  const checkoutData = formatBasketForCheckout(basketItems, userInfo);
  redirectToCustomCheckout(checkoutData);
};
```

### 4. Update Success/Return Handling

The new system redirects to `/checkout/return` with these parameters:
- `session_id` - Stripe session ID
- `transaction_id` - Your internal transaction ID
- `order_id` - Order ID (for escrow payments)

You can customize the return page in `src/app/checkout/return/page.jsx` to handle your specific success logic.

### 5. Environment Variables

Make sure you have these environment variables set:
```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_BASE_URL=http://localhost:3000 (or your domain)
```

## Benefits of the New System

1. **Better User Experience**: No redirect to external Stripe pages
2. **Consistent Branding**: Checkout stays within your application
3. **More Control**: Full control over the checkout flow and styling
4. **Mobile Optimized**: Better mobile experience with embedded checkout
5. **Escrow Integration**: Built-in support for your escrow payment system

## Testing the Migration

1. **Test Regular Payments**: Use `/checkout` with basic payment data
2. **Test Escrow Payments**: Include escrow parameters in checkout data
3. **Test Error Handling**: Try invalid data to see error states
4. **Test Success Flow**: Complete a payment and verify return page
5. **Test Mobile**: Ensure checkout works well on mobile devices

## Rollback Plan

If you need to rollback:
1. Keep your existing API endpoints unchanged
2. Simply change the frontend calls back to the old pattern
3. The new files won't interfere with existing functionality

## Next Steps

1. Update your existing payment components one by one
2. Test each migration thoroughly
3. Update any webhook handling if needed
4. Consider adding additional customization to the checkout UI
5. Update your documentation and user guides

The new system maintains compatibility with your existing transaction and escrow systems while providing a much better user experience.
