"use client";
import { useLoading } from "@/context/LoadingContext";

/**
 * Hook to control the application-wide loading state
 * 
 * @returns {Object} Loading control methods
 * @returns {boolean} isLoading - Current loading state
 * @returns {Function} showLoader - Show the loader for a specific duration
 * @returns {Function} hideLoader - Hide the loader immediately
 */
export const useAppLoading = () => {
  const { isLoading, showLoader, hideLoader } = useLoading();

  return {
    isLoading,
    showLoader,
    hideLoader
  };
};

export default useAppLoading;
