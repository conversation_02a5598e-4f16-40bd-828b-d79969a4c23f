import { useState, useEffect } from "react";
import {
  SideSheetContainer,
  SideSheetHeader,
  SideSheetDescription,
} from "@/components/ui/sidebarSheet";
import { closeEvent } from "@/lib/eventEmmiter";
import { Settings } from "react-feather";
// import OrderCard from "./orderCard";

export function Notifications({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [isSheetOpen, setSheetOpen] = useState(false);

  useEffect(() => {
    const closeChat = () => {
      setSheetOpen(false);
      onOpenChange(false);
    };

    closeEvent.on("close", closeChat);

    return () => {
      closeEvent.off("close", closeChat);
    };
  }, [onOpenChange]);

  return (
    <SideSheetContainer
      className="left-[22rem] max-lg:left-0"
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Header */}
      <SideSheetHeader className="px-8">
        <div
          className="cursor-pointer text-base font-normal text-black row gap-2"
          onClick={() => onOpenChange(false)} // Close only when Back button is clicked
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">Notifications</p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>

      {/* Tabs */}
      <SideSheetDescription className="overflow-y-auto chat-scroll-custom">
        {" "}
        {isSheetOpen}
        <div className="flex flex-col mr-4">
          <div>
            <p className="font-bold text-primary mb-4 mt-1">This Month</p>
            {Array.from({ length: 10 }).map((_, indexs) => (
              <div key={indexs}>
                <div className="row justify-between mb-5 gap-3">
                  <div className="row gap-2">
                    <div>
                      <img
                        src="https://github.com/shadcn.png"
                        alt=""
                        className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] rounded-full object-cover"
                        style={{
                          border: "3px solid",
                        }}
                      />
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-primary">
                        Daniel Kaluuya{" "}
                        <span className="font-normal">starred your post</span>{" "}
                        <span className="text-subtitle font-normal">1d</span>
                      </p>
                    </div>
                  </div>
                  <img
                    src="https://github.com/shadcn.png"
                    alt=""
                    className="w-[40px] h-[40px] object-cover"
                  />
                </div>
              </div>
            ))}
          </div>
          <div>
            <p className="font-bold text-primary mb-4 mt-1">Earlier</p>
            {Array.from({ length: 2 }).map((_, indexs) => (
              <div key={indexs}>
                <div className="row justify-between mb-5 gap-3">
                  <div className="row gap-2">
                    <div>
                      <img
                        src="https://github.com/shadcn.png"
                        alt=""
                        className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] rounded-full object-cover"
                        style={{
                          border: "3px solid",
                        }}
                      />
                    </div>
                    <div>
                      <p className="font-bold text-primary">
                        Daniel Kaluuya{" "}
                        <span className="font-normal">starred your post</span>{" "}
                        <span className="text-subtitle font-normal">1d</span>
                      </p>
                    </div>
                  </div>
                  <img
                    src="https://github.com/shadcn.png"
                    alt=""
                    className="w-[40px] h-[40px] object-cover"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Setting Msg */}
          <div>
            {/* <p className="font-bold text-primary">Earlier</p> */}
            {Array.from({ length: 5 }).map((_, indexs) => (
              <div key={indexs}>
                <div className="row justify-between mb-5">
                  <div className="row gap-2">
                    <div className="border-2 border-borderColor rounded-full w-[40px] h-[40px] min-w-[40px] min-h-[40px] flex items-center justify-center">
                      <Settings className="text-borderColor" />
                    </div>
                    <div>
                      <span className="text-titleLabel text-base">
                        Your categories request change was approved.{" "}
                      </span>
                      <span className="text-subtitle text-base">4w</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
