"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { themes } from "../../../../theme";
import { getLensProfileDetails } from "@/services/lensService";
import { useRouter } from "next/navigation";
import { Play } from "lucide-react";
import LazyMedia from "@/components/LazyMedia";

// Function to generate a deterministic "random" index based on a seed
const getStableRandomIndex = (seed: string): number => {
  // Simple hash function to generate a number from a string
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    hash = (hash << 5) - hash + seed.charCodeAt(i);
    hash = hash & hash; // Convert to 32bit integer
  }
  // Use the hash to generate a number between 0 and 4
  return Math.abs(hash % 5);
};

const ImageCard = (props: any) => {
  const [category, setCategory] = useState("");
  // State to track which box will display the Lens content (0-4)
  // Initialize with -1, will be set properly in useEffect
  const [lensBoxIndex, setLensBoxIndex] = useState<number>(-1);
  // Use a stable seed for the random index - using props that won't change
  const stableSeed = props.lensId || props.lenscategory || "default-seed";

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) {
      return undefined;
    }

    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const getBackgroundColor = (title: string): string => {
    const normalizedTitle = title === "Storytelling" ? "Literature" : title;
    const theme = Object.values(themes).find((t) => t.title === normalizedTitle);
    return theme ? theme.backgroundColor : "#ffffff";
  };

  useEffect(() => {
    // Use the stable random index instead of Math.random()
    if (typeof window !== "undefined") {
      // Only set the index on the client side
      setLensBoxIndex(getStableRandomIndex(stableSeed));
    }

    const fetchProfileDetails = async () => {
      const response = await getLensProfileDetails(props.lenscategory);
      if (response.success) {
        if (response.lens_details) {
          for (let index = 0; index < response.lens_details.length; index++) {
            if (response?.lens_details[index]?.category != "my feed") {
              const category = response?.lens_details?.[index]?.category;

              const capitalizeWords = (str: string) => {
                return str
                  .split(" ")
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(" ");
              };
              setCategory(capitalizeWords(category));
            }
          }
        }
      } else {
        console.log("Error");
      }
    };

    fetchProfileDetails();
  }, [props.lenscategory, stableSeed]);

  // Render a regular content box
  const renderRegularBox = (chunkItem: any, boxHeight: number, boxWidth?: string) => {
    if (!chunkItem) return null;

    const mediaStyle = {
      borderColor: getBackgroundColor(chunkItem.category),
      minHeight: `${boxHeight}px`,
      maxHeight: `${boxHeight}px`,
    };

    return (
      <div className={`${boxWidth || "w-full"} relative`}>
        {chunkItem.mediaType === "image" ? (
          <Link
            className="block"
            href={`/browse/${
              chunkItem.category === "Storytelling" ? "Literature" : chunkItem.category
            }/${chunkItem.id}%20${chunkItem.user_id}`}
            prefetch={true}
          >
            <LazyMedia
              src={generateFileUrl(chunkItem.postFile) || "/assets/noimg.png"}
              alt={`Post ${chunkItem.id}`}
              type="image"
              className="h-full w-full object-cover border-2"
              style={mediaStyle}
              placeholderClassName="bg-gray-100"
            />
          </Link>
        ) : chunkItem.mediaType === "video" ? (
          <Link
            href={`/browse/${
              chunkItem.category === "Storytelling" ? "Literature" : chunkItem.category
            }/${chunkItem.id}%20${chunkItem.user_id}`}
            prefetch={true}
            className="h-full w-full object-cover overflow-hidden"
            style={{
              maxHeight: `${boxHeight}px`,
              minHeight: `${boxHeight}px`,
            }}
          >
            <LazyMedia
              src={generateFileUrl(chunkItem.postFile)}
              type="video"
              className="h-full w-full object-cover border-2"
              style={mediaStyle}
              placeholderClassName="bg-gray-100"
              showPlayIcon={true}
              playIconClassName="top-1 right-1"
              controls={false}
              autoPlay={false}
              muted={true}
            />
          </Link>
        ) : null}
      </div>
    );
  };

  // Render a Lens content box
  const renderLensBox = (boxHeight: number, boxWidth?: string) => {
    const hexToRgb = (hex: string) => {
      hex = hex.replace(/^#/, "");
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);
      return `${r}, ${g}, ${b}`;
    };

    const mediaStyle = {
      borderColor: props.borderColor,
      minHeight: `${boxHeight}px`,
      maxHeight: `${boxHeight}px`,
    };

    const imageStyle = {
      ...mediaStyle,
      backgroundColor: `rgba(${hexToRgb(props.borderColor)}, 0.3)`,
    };

    return (
      <div
        className={`${boxWidth || "w-full"} relative h-full`}
        style={{ maxHeight: `${boxHeight}px` }}
      >
        <Link
          href={`/browse/${category}/${props?.postId?.id}%20${props.lensId}`}
          className="block h-full"
        >
          {props.lensItem1?.image?.item ? (
            <LazyMedia
              src={props.lensItem1?.image?.item || ""}
              alt={"Lens content"}
              type="image"
              className="h-full w-full object-cover border-2"
              style={imageStyle}
              placeholderClassName="bg-gray-50"
            />
          ) : props.lensItem1?.video?.item ? (
            <LazyMedia
              src={props.lensItem1?.video?.item}
              type="video"
              className="h-full w-full object-cover border-2"
              style={mediaStyle}
              placeholderClassName="bg-gray-50"
              controls={false}
              autoPlay={false}
              muted={true}
            />
          ) : null}
        </Link>
        <div className="absolute top-1 right-1">
          <img src="/assets/lens.png" alt="" className="h-3 w-5" />
        </div>
      </div>
    );
  };

  // Sequential content management - distributes available items sequentially
  const getContentForPosition = (position: number) => {
    // If this position is the lens box, return null (lens will be rendered instead)
    if (position === lensBoxIndex) {
      return null;
    }

    // Count how many non-lens positions we've seen before this one
    let contentIndex = 0;
    for (let i = 0; i < position; i++) {
      if (i !== lensBoxIndex) {
        contentIndex++;
      }
    }

    // If we have content at this index, return it
    if (contentIndex < props.chunk?.length) {
      return props.chunk[contentIndex];
    }

    return null;
  };

  // Box configurations
  const boxConfigs = [
    { height: 116, width: "col-span-4" }, // Box 0: Top-left large
    { height: 116, width: "col-span-2" }, // Box 1: Top-right small
    { height: 124, width: "col-span-2" }, // Box 2: Bottom-left top
    { height: 124, width: "col-span-2" }, // Box 3: Bottom-left bottom
    { height: 250, width: "col-span-4" }, // Box 4: Bottom-right large
  ];

  // Map each position to its content
  const boxContent = boxConfigs.map((config, index) => ({
    content: lensBoxIndex === -1 ? props.chunk?.[index] : getContentForPosition(index),
    isLens: lensBoxIndex !== -1 && index === lensBoxIndex,
    height: config.height,
    width: config.width,
  }));

  return (
    <>
      <div>
        <div className="w-full mt-0">
          <div className="w-full mb-[1px] gap-[1px]">
            <div className="w-full mt-0 mr-[1px]">
              {/* Top row with boxes 0 and 1 */}
              <div className="grid grid-cols-6 gap-[1px] mb-[1px] mr-[1px]">
                <div className="col-span-4 max-h-[116px] min-h-[116px]">
                  {boxContent[0].isLens
                    ? renderLensBox(116, "col-span-4")
                    : renderRegularBox(boxContent[0].content, 116, "col-span-4")}
                </div>
                <div className="col-span-2 max-h-[116px] min-h-[116px]">
                  {boxContent[1].isLens
                    ? renderLensBox(116, "col-span-2")
                    : renderRegularBox(boxContent[1].content, 116, "col-span-2")}
                </div>
              </div>

              {/* Bottom row with boxes 2, 3, and 4 */}
              <div className="grid grid-cols-6 gap-[1px] mr-[1px] min-h-[250px] max-h-[250px]">
                <div className="col-span-2">
                  {/* Top half of left column - box 2 */}
                  <div className="w-full object-cover h-1/2 min-h-1/2 max-h-[124px] mb-[1px]">
                    {boxContent[2].isLens
                      ? renderLensBox(124)
                      : renderRegularBox(boxContent[2].content, 124)}
                  </div>

                  {/* Bottom half of left column - box 3 */}
                  <div className="w-full object-cover h-1/2 min-h-1/2 max-h-[124px]">
                    {boxContent[3].isLens
                      ? renderLensBox(124)
                      : renderRegularBox(boxContent[3].content, 124)}
                  </div>
                </div>

                {/* Right bottom large - box 4 */}
                <div className="col-span-4 w-full h-full min-h-full object-cover">
                  {boxContent[4].isLens
                    ? renderLensBox(250)
                    : renderRegularBox(boxContent[4].content, 250)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageCard;
