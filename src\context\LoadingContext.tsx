"use client";
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import FullScreenLoader from "@/components/loaders/FullScreenLoader";
import { useSignInStore } from "@/components/GlobalSignInButton";

interface LoadingContextType {
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  showLoader: (duration?: number) => void;
  hideLoader: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }
  return context;
};

interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isWalletConnecting, setIsWalletConnecting] = useState(false);
  const globalWalletConnecting = useSignInStore((state) => state.isWalletConnecting);

  // Check if wallet connection is in progress
  useEffect(() => {
    const checkWalletConnection = () => {
      // Check for wallet connection indicators
      const lensUser = localStorage.getItem("lens-user");
      const isWalletConnectInUrl =
        window.location.href.includes("wc:") || window.location.href.includes("walletconnect");

      // Set wallet connecting state
      const walletConnecting = !!lensUser || isWalletConnectInUrl;
      setIsWalletConnecting(walletConnecting);

      // If wallet connection is detected, hide the loader immediately
      if (walletConnecting) {
        setIsLoading(false);
      }
    };

    // Check initially
    checkWalletConnection();

    // Set up event listener for visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        checkWalletConnection();

        // If returning from wallet app, hide the loader
        if (isWalletConnecting) {
          setIsLoading(false);
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isWalletConnecting]);

  // Show loader for a specific duration
  const showLoader = (duration = 2500) => {
    // Don't show loader if wallet connection is in progress (check both local and global state)
    if (isWalletConnecting || globalWalletConnecting) return;

    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, duration);
  };

  // Hide loader immediately
  const hideLoader = () => {
    setIsLoading(false);
  };

  // Initial loading when the app first loads
  useEffect(() => {
    if (isInitialLoad) {
      const timer = setTimeout(() => {
        setIsLoading(false);
        setIsInitialLoad(false);
      }, 2500); // Show loader for 2.5 seconds on initial load

      return () => clearTimeout(timer);
    }
  }, [isInitialLoad]);

  // Listen for page navigation events
  useEffect(() => {
    const handleStart = () => {
      // Don't show loader if wallet connection is in progress (check both local and global state)
      if (!isWalletConnecting && !globalWalletConnecting) {
        setIsLoading(true);
      }
    };

    const handleComplete = () => {
      setTimeout(() => {
        setIsLoading(false);
      }, 500); // Short delay to ensure components are rendered
    };

    // Add event listeners for page changes
    window.addEventListener("beforeunload", handleStart);
    window.addEventListener("load", handleComplete);

    return () => {
      window.removeEventListener("beforeunload", handleStart);
      window.removeEventListener("load", handleComplete);
    };
  }, [isWalletConnecting, globalWalletConnecting]);

  return (
    <LoadingContext.Provider value={{ isLoading, setIsLoading, showLoader, hideLoader }}>
      {isLoading && !isWalletConnecting && !globalWalletConnecting && (
        <FullScreenLoader
          message={isInitialLoad ? "Welcome to Amuzn" : "Loading..."}
          subMessage={
            isInitialLoad
              ? "Connecting you to creative communities..."
              : "Please wait while we prepare your experience..."
          }
        />
      )}
      {children}
    </LoadingContext.Provider>
  );
};

export default LoadingProvider;
