import { Badge } from "@/components/ui/badge";
import { CheckSquare } from "react-feather";
import { SideSheetContainer, SideSheetHeader, SideSheetDescription } from "@/components/ui/sidebarSheet";

export function ConfirmPayments({
  open,
  onOpenChange,
  cardId,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cardId: any;
}) {
  return (
    <SideSheetContainer className="max-md:left-0 left-[48.25rem] max-lg:left-[26.25rem] max-w-[26.25rem]"
      open={open} onOpenChange={(isOpen) => {
        // Prevent closing the sheet unless explicitly triggered
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}>
      {/* Header */}
      <SideSheetHeader className="px-8 xs-px-2">
        {/* Header */}
        <div
          className="cursor-pointer text-base text-black font-normal row gap-2"
          // Close only when Back button is clicked
          onClick={() => onOpenChange(false)}
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">
          Confirm payment{cardId}
        </p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>
      {/* Tabs */}
      <SideSheetDescription>
        <div className="flex flex-col mr-4">
          <div className="row justify-between">
            <p className="text-[#404040]">Subtotal</p>
            <p className="font-bold text-primary">$100.00</p>
          </div>
          <div className="row justify-between my-1">
            <p className="text-[#404040]">Transaction fee (4%)</p>
            <p className="font-bold text-primary">$4.00</p>
          </div>
          <div className="row justify-between">
            <p className="text-[#404040]">Order total</p>
            <p className="font-bold text-primary">$104.00</p>
          </div>
          <div className="flex flex-row  gap-2 mt-2 border-b-2 pb-3">
            <div>
              <CheckSquare />
            </div>
            <div>
              <p className="text-primary">Request an invoice </p>
              <p className="text-[#898887]">
                Note: to obtain an Invoice you’ll need to provide your tax
                details (legal name, address and VAT registration number).
              </p>
            </div>
          </div>
          <div className="mt-3">
            <p className="text-subtitle">
              Terms: By placing your order, you confirm that you agree to
              the User Terms and Conditions.
            </p>
            <Badge className=" btn-xs text-white btn py-4 ">Confirm</Badge>
          </div>
        </div>
      </SideSheetDescription>
    </SideSheetContainer >
  );
}
