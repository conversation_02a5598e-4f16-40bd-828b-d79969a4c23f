import useLogin from "./auth/useLogin";
import { v4 } from "uuid";
import { readAccessToken } from "./auth/auth-helpers";
import { useCreatePostMutation } from "@/graphql/test/generated";
import { immutable, StorageClient } from "@lens-chain/storage-client";
import { useAccount } from "wagmi";

export function useComment() {
  const { mutateAsync: requestTypedData_OnChain } = useCreatePostMutation({});

  const { address,isConnected } = useAccount();
  const { mutateAsync: loginUser }: any = useLogin();

  const storageClient = StorageClient.create();

  async function comment(
    commentOn: string,
    comment: string,
    ismomoke: boolean
  ) {
    try {
      if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      const payload = {
        $schema: "https://json-schemas.lens.dev/posts/text-only/3.0.0.json",
        name: "Comment by",
        description: comment,
        external_url: "https://mytext.com",
        image: "https://text.com/image.png",
        lens: {
          id: v4(),
          locale: "en",
          mainContentFocus: "TEXT_ONLY",
          content: comment,
        },
      };
      const { uri } = await storageClient.uploadAsJson(payload, {
        acl: immutable(232),
      });

      const typedData = await requestTypedData_OnChain({
        request: {
          commentOn: {
            post: commentOn,
          },
          contentUri: uri,
        },
      });
      return typedData;
    } catch (error) {
      // console.error("Follow error:", error);
      throw error;
    }
  }

  return comment;
}
