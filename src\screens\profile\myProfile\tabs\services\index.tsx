"use client";
import ServicesCard from "./servicesCard";
import ServiceDetails from "./serviceDetails";
import { useState } from "react";
const ServicesMyProfile = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState(null);

  const handleSelectService = (id: any) => {
    // console.log(id);

    setSelectedServiceId(id);
    setIsOpen(true);
  };

  const handleSelectServiceDetail = (id: any) => {
    setSelectedServiceId(null);
    setIsOpen(false);
  };
  return (
    <>
      <div>
        <div className="">
          <div></div>
          {isOpen ? (
            <ServiceDetails
              id={selectedServiceId}
              onSelectServiceDetails={handleSelectServiceDetail}
              categories={props.categories}
            />
          ) : (
            <ServicesCard
              onSelectService={handleSelectService}
              activeColor={props.activeColor}
              setParentIsOpen={props.setIsOpen}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default ServicesMyProfile;
