'use client'

import { useState } from 'react'
import { redirectToCustomPaymentForm } from '@/utils/checkout'

export default function DemoCustomPayment() {
  const [loading, setLoading] = useState(false)

  const handlePayment = (productData) => {
    setLoading(true)
    
    try {
      redirectToCustomPaymentForm(productData)
    } catch (error) {
      console.error('Error redirecting to payment:', error)
      setLoading(false)
    }
  }

  const products = [
    {
      name: 'Pure set',
      price: 6500, // $65.00
      currency: 'usd',
      image: '/api/placeholder/300/300',
      description: 'Premium skincare set with natural ingredients'
    },
    {
      name: 'Luxury Watch',
      price: 25000, // $250.00
      currency: 'usd',
      image: '/api/placeholder/300/300',
      description: 'Elegant timepiece with Swiss movement'
    },
    {
      name: 'Artisan Coffee',
      price: 2400, // $24.00
      currency: 'usd',
      image: '/api/placeholder/300/300',
      description: 'Single origin beans from Ethiopia'
    }
  ]

  const formatPrice = (amount, currency) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Custom Payment Form Demo
          </h1>
          <p className="text-gray-600">
            Try the new custom payment form layout (like the image you showed)
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
              <img 
                src={product.image} 
                alt={product.name}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {product.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  {product.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900">
                    {formatPrice(product.price, product.currency)}
                  </span>
                  <button
                    onClick={() => handlePayment({
                      amount: product.price,
                      currency: product.currency,
                      productName: product.name,
                      productImage: product.image,
                    })}
                    disabled={loading}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-md transition-colors"
                  >
                    {loading ? 'Loading...' : 'Buy Now'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            What's Different About This Payment Form?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">✅ Your New Custom Form:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Product image and details on the left</li>
                <li>• Payment form fields on the right</li>
                <li>• Individual card input fields (number, expiry, CVC)</li>
                <li>• Custom styling and layout</li>
                <li>• Matches the design you showed me</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">🔄 Your Previous Form:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Stripe's embedded checkout component</li>
                <li>• Full-width layout</li>
                <li>• Stripe's default styling</li>
                <li>• Less customization options</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Click "Buy Now" on any product to see the new custom payment form in action!
          </p>
        </div>
      </div>
    </div>
  )
}
