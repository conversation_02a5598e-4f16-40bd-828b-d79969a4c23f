import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';

export default function SellerOnboarded() {
  const searchParams = useSearchParams();
  const account = searchParams.get('account');
  const userIdFromUrl = searchParams.get('userId');
  const [saved, setSaved] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, loading: userLoading } = useCurrentUser();

  useEffect(() => {
    // Get user ID from URL params or current user
    const finalUserId = userIdFromUrl || user?.uid;

    if (account && finalUserId) {
      fetch('/api/save-seller-account', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: finalUserId, account }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          setError(data.error);
        } else {
          setSaved(true);
        }
      })
      .catch(err => {
        console.error('Error saving seller account:', err);
        setError('Failed to save seller account');
      });
    } else if (!finalUserId && !userLoading) {
      setError('User ID not found. Please log in and try again.');
    }
  }, [account, userIdFromUrl, user?.uid, userLoading]);

  if (userLoading) {
    return (
      <div>
        <h1>Onboarding Complete!</h1>
        <p>Loading user information...</p>
      </div>
    );
  }

  const finalUserId = userIdFromUrl || user?.uid;

  return (
    <div>
      <h1>Onboarding Complete!</h1>
      <p>Your Stripe account ID: <b>{account}</b></p>
      <p>User ID: <b>{finalUserId}</b></p>
      <p>Email: <b>{user?.email}</b></p>

      {error ? (
        <p style={{ color: 'red' }}>Error: {error}</p>
      ) : (
        <p>
          {saved
            ? "✅ Your seller account was saved and you can now receive payments!"
            : "⏳ Saving your seller account..."}
        </p>
      )}
    </div>
  );
}
