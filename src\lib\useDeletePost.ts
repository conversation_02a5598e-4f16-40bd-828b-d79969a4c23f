import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import { useDeletePostMutation } from "@/graphql/test/generated";
import { useAccount } from "wagmi";

export function useDeletePost() {
  const { mutateAsync: requestTypedData } = useDeletePostMutation();

  const { address,isConnected } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();

  async function post(post: string) {
    try {
      if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      const typedData = await requestTypedData({
        request: {
          post: post,
        },
      });

      return typedData.deletePost;
    } catch (error) {
      throw error;
    }
  }
  return post;
}
