import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { getUserIdFromRequest } from "@/lib/auth/serverAuth";
import { doc, setDoc, getDoc } from "firebase/firestore";
import { initFirebase } from "../../../../../firebaseConfig";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    const { accountId } = await req.json();

    if (!accountId) {
      return NextResponse.json({ error: "Account ID is required" }, { status: 400 });
    }

    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    console.log("Creating login link for account:", accountId, "user:", userId);

    // 1. Verify the account exists and retrieve account details
    let account;
    try {
      account = await stripe.accounts.retrieve(accountId);

      if (!account || account.object !== "account") {
        return NextResponse.json(
          { error: "Invalid account ID or account not found." },
          { status: 404 }
        );
      }

      console.log("Found existing account:", account.id);
    } catch (error) {
      console.error("Error retrieving account by ID:", error);
      return NextResponse.json(
        { error: "Account not found or invalid account ID." },
        { status: 404 }
      );
    }

    // 2. Create login link for hosted login
    const loginLink = await stripe.accounts.createLoginLink(accountId);

    // 3. Save/update account details in Firebase
    try {
      const { db } = await initFirebase();
      const sellerRef = doc(db, "sellers", userId);

      // Check if seller already exists
      const sellerDoc = await getDoc(sellerRef);
      const existingData = sellerDoc.exists() ? sellerDoc.data() : {};

      await setDoc(
        sellerRef,
        {
          ...existingData,
          stripeAccountId: account.id,
          email: account.email,
          businessName: account.business_profile?.name,
          lastLoginAt: new Date().toISOString(),
          onboardingComplete: account?.details_submitted || false,
          chargesEnabled: account?.charges_enabled || false,
          payoutsEnabled: account?.payouts_enabled || false,
          country: account.country,
          currency: account.default_currency,
          accountType: account.type,
          businessType: account.business_type,
        },
        { merge: true }
      );

      console.log("Updated Stripe account details in Firebase:", {
        userId,
        accountId: account.id,
        email: account.email,
      });
    } catch (firebaseError) {
      console.error("Error saving to Firebase:", firebaseError);
      // Continue with login link even if Firebase save fails
    }

    // 4. Return login link and account details
    return NextResponse.json({
      success: true,
      message: "Login link created successfully",
      loginUrl: loginLink.url,
      accountId: account.id,
      userId,
      account: {
        id: account.id,
        email: account.email,
        businessName: account.business_profile?.name,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        country: account.country,
        currency: account.default_currency,
        accountType: account.type,
        businessType: account.business_type,
        created: account.created,
        defaultCurrency: account.default_currency,
        externalAccounts: account.external_accounts?.data?.length || 0,
        requirements: {
          currentlyDue: account.requirements?.currently_due || [],
          eventuallyDue: account.requirements?.eventually_due || [],
          pastDue: account.requirements?.past_due || [],
          pendingVerification: account.requirements?.pending_verification || [],
        },
        capabilities: account.capabilities,
        settings: {
          payouts: account.settings?.payouts,
          payments: account.settings?.payments,
        },
      },
    });
  } catch (error) {
    console.error("Error creating login link:", error);
    return NextResponse.json({ error: "Failed to create login link" }, { status: 500 });
  }
}
