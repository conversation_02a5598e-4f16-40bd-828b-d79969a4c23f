import { useMutation, useQueryClient } from "@tanstack/react-query";
import { setAccessToken } from "./auth-helpers";
import { useAccount, useSignMessage } from "wagmi";
import {
  PageSize,
  useAccountsAvailableQuery,
  useAuthenticateMutation,
  useChallengeMutation,
} from "@/graphql/test/generated";

const onError = (error: any) => {
  console.log({ error });
};

export default function useLogin() {
      const {address} = useAccount();
  
  const { mutateAsync: sendSignedMessage } = useAuthenticateMutation();
  const client = useQueryClient();
  const { signMessageAsync } = useSignMessage({ mutation: { onError } });

  const { mutateAsync } = useChallengeMutation();

  // const { data: accountData } = useAccountsAvailableQuery(
  //   {
  //     accountsAvailableRequest: {
  //       managedBy: address,
  //       includeOwned: true,
  //       pageSize: PageSize.Fifty,
  //     },
  //     lastLoggedInAccountRequest: {
  //       address,
  //     },
  //   },
  //   {
  //     enabled: !!address,
  //     refetchOnWindowFocus: false,
  //   }
  // );

  // const allProfiles = accountData?.accountsAvailable.items || [];
  // const lastLogin = accountData?.lastLoggedInAccount;

  // const remainingProfiles = lastLogin
  //   ? allProfiles
  //       .filter(({ account }) => account.address !== lastLogin.address)
  //       .map(({ account }) => account)
  //   : allProfiles.map(({ account }) => account);

  // const accounts = lastLogin
  //   ? [lastLogin, ...remainingProfiles]
  //   : remainingProfiles;

  async function login(account?:string) {

    if (!address) return;

    const challenge = await mutateAsync({
      request: {
        accountOwner: {
          owner: address,
          account: account
          // "******************************************"
          // accounts?.[0]?.address,
          // account: address,
        },
      },
    });
    
    // await generateChallenge(address,data?.profilesManaged.items[0]?.id);

    /// Sign the challenge with  user wallet
    // const signature = await sdk?.wallet.sign(challenge.challenge.text);
    const signature = await signMessageAsync({
      message: challenge?.challenge.text,
    });

    // send  signed challenge to the Lens API
    const { authenticate } = await sendSignedMessage({
      request: {
        signature: signature,
        id: challenge.challenge.id,
      },
    });

    if (authenticate.__typename !== "AuthenticationTokens") {
      return;
    }
    const { accessToken, idToken: identityToken, refreshToken } = authenticate;
    setAccessToken(accessToken, identityToken, refreshToken);

    client.invalidateQueries(["lens-user", address]);
    // Small delay to ensure tokens and query invalidation are processed
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }
  return useMutation(login);
}
