import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  assetPrefix: "/assets",
  basePath: "",
  async rewrites() {
    return [
      {
        source: "/api/proxy/:path*",
        destination: "https://statics-v2.lens.dev/:path*", // Proxy the request
      },
    ];
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  env: {
    BASE_STORAGE_URL: process.env.BASE_STORAGE_URL,
  },
  // webpack: (config) => {
  //   config.resolve.fallback = {
  //     fs: false,
  //     net: false,
  //     tls: false,
  //   };
  //   return config;
  // },
  // Add security headers to prevent clickjacking
  async headers() {
    return [
      {
        // Apply to all routes
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY", // Prevents embedding in ANY iframe
          },
          {
            key: "Content-Security-Policy",
            value: "frame-ancestors 'none';", // Modern alternative to X-Frame-Options
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=(), payment=()",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
