"use client";
import useAuth from "@/hook";
import useEvent from "@/hook/events";
import React from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";

interface CustomCalendarProps {
  activeDate: Date;
  setActiveDate: (date: Date) => void;
}
function CalendarCompaBasket({
  activeBorderColor,
  activeDate,
  setActiveDate,
  
}: any) {
  const auth = useAuth();



  // console.log(activeBorderColor);

  return (
    <>
      <style>
        {`

        .react-calendar {
    border: 0px solid #a0a096;
}

        .calendar-container .react-calendar__month-view__days{
            display: flex
;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    content: center;
    justify-items: center;

        }
          /* ~~~ Container styles ~~~ */
          .calendar-container {
            max-width: 100%;
            margin: auto;
            margin-top: 20px;
            // background-color: #d4f7d4;
            padding: 10px;
            border-radius: 3px;
            border:none;
          }

          /* ~~~ Navigation styles ~~~ */
          .react-calendar__navigation {
            display: flex;
          }

          .react-calendar__navigation__label {
            font-weight: bold;
          }

          .react-calendar__navigation__arrow {
            flex-grow: 0.333;
          }

          /* ~~~ Label styles ~~~ */
          .react-calendar__month-view__weekdays {
            text-align: center;
          }

          /* ~~~ Button styles ~~~ */
          .react-calendar button {
            margin: 4px;
            // background-color: #E5B045;
            border: 0;
            border-radius: 3px;
            // color: white;
            padding: 0px 0;
            cursor: pointer;
            border-radius:100%;
            width:40px;
            font-size: 1.2rem;

          }

          .react-calendar button:hover {
            // background-color: #556b55;
          }

          .react-calendar button:active {
            // background-color: #fff;
          }

          /* ~~~ Day grid styles ~~~ */
          .react-calendar__month-view__days {
            display: grid !important;
            grid-template-columns: repeat(7, 14.2%);
          }

          .react-calendar__tile {
            max-width: initial !important;
          }

          .react-calendar__tile--range {
            // box-shadow: 0 0 6px 2px black;
          }

          /* ~~~ Neighboring month & weekend styles ~~~ */
          .react-calendar__month-view__days__day--neighboringMonth {
            opacity: 0.7;
          }

          .react-calendar__month-view__days__day--weekend {
            // color: #dfdfdf;
          }

          /* ~~~ Other view styles ~~~ */
          .react-calendar__year-view__months,
          .react-calendar__decade-view__years,
          .react-calendar__century-view__decades {
            display: grid !important;
            grid-template-columns: repeat(5, 20%);
          }

          .react-calendar__year-view__months {
            grid-template-columns: repeat(3, 33.3%);
          }

          .react-calendar__tile {
            max-width: initial !important;
          }
               /* Style for highlighted dates */
          .highlight {
            // background-color: activeBorderColor.activeBorderColor !important;
            color: white !important;
            border-radius: 50%;
          }

          // same highlighted dates code past in global css file
          .react-calendar__month-view__days__day--weekend {
    color: #333  !important;
}
        `}
      </style>

      <div
        className="calendar-container w-full row justify-center hide-scroll-custom overflow-y-auto"
        style={
          {
            "--active-bg-color-calender": activeBorderColor,
          } as React.CSSProperties
        }
      >
        <Calendar
          calendarType="gregory"
          next2Label={null}
          prev2Label={null}
        
          onClickDay={(value, event) => {
            // console.log({ value });

            
              setActiveDate(value);
            
          }}
        />
      </div>
    </>
  );
}

export default CalendarCompaBasket;
