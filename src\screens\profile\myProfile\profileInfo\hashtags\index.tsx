"use client";
import { <PERSON>, Edit2, <PERSON><PERSON>, X } from "react-feather";
import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import useAuth from "@/hook";
import { updateUser } from "@/services/usersServices";
const data = [
  "Deephouse style",
  "Musician",
  "Music",
  "Creator",
  "Deephouse",
  "New songs",
  "Music track",
];

import { Modal, ModalBody, ModalContent } from "@heroui/react";

const ProfileInfoHashtags = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hashtags, setHashtags] = useState<string[]>(props?.hashtags || []);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");

  // No longer using useEffect to handle success state
  // Now handling it directly in the handleSubmit function

  const addHashtag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && e.currentTarget.value.trim()) {
      e.preventDefault();
      setHashtags([...hashtags, e.currentTarget.value.trim()]);
      e.currentTarget.value = ""; // Clear input
    }
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter((hashtag) => hashtag !== tag));
  };

  const auth = useAuth();

  const handleSubmit = async () => {
    if (hashtags) {
      setUploadStatus("uploading");

      try {
        const updatedData = {
          hashtags: hashtags,
        };
        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setUploadStatus("success");

          // Set a timeout to close the modal and refresh the data
          setTimeout(() => {
            // Trigger parent component to refresh data
            props.onClickAction(true);

            // Close the modal
            setIsOpen(false);

            // Reset states
            setHashtags([]);
            setUploadStatus("idle");
          }, 1500);

          // Force close the modal after a longer delay if it's still open
          setTimeout(() => {
            setIsOpen(false);
          }, 2000);
        } else {
          console.error("Error updating Hashtags:", response.error);
          setUploadStatus("idle");
        }
      } catch (error) {
        console.error("Error updating Hashtags:", error);
        setUploadStatus("idle");
      }
    }
  };
  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">Hashtags</span>
            </div>

            {!props.isOtherProfile && (
              <div
                onClick={() => {
                  setIsOpen(true), setHashtags(props?.hashtags || []);
                }}
                className=" cursor-pointer"
              >
                <Edit2 color={props.bgColor} className="max-md:h-[20px]" />
              </div>
            )}
          </div>
          <div className="mt-4 max-md:mt-2">
            {props?.hashtags &&
              props?.hashtags?.map((item: string, index: number) => {
                return (
                  <Badge
                    variant="secondary"
                    className="mr-2 mb-2 px-[4.9rem] max-md:px-[1rem] py-[2px] text-[#404040] bg-[#EEEEEE] text-[14px] font-normal"
                    key={index}
                  >
                    {item}
                  </Badge>
                );
              })}
          </div>
        </div>
      </div>

      {/* PersonalMotto Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={(open) => {
            // Always allow closing the modal
            setIsOpen(open);
          }}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {uploadStatus === "success" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Hashtags Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your hashtags have been updated and the changes will be visible on your
                        profile.
                      </p>
                    </div>
                  ) : uploadStatus === "uploading" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Updating Your Hashtags...
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your information. This may take a moment.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div onClick={() => setIsOpen(false)} className="cursor-pointer">
                          <X />
                        </div>
                        <p className="font-bold text-primary">Edit Hashtags</p>
                        <div
                          className={`flex items-center ${
                            hashtags.length > 0
                              ? "text-primary cursor-pointer"
                              : "text-borderColor cursor-not-allowed"
                          }`}
                          onClick={hashtags.length > 0 ? handleSubmit : undefined}
                        >
                          Save
                        </div>
                      </div>

                      <div>
                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Enter hashtags*
                          </Label>
                          <div className="border-2 rounded-lg p-3">
                            <div className="flex flex-wrap gap-2 mb-32">
                              {hashtags.map((tag, index) => (
                                <span
                                  key={index}
                                  className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                                >
                                  {tag}
                                  <button
                                    type="button"
                                    onClick={() => removeHashtag(tag)}
                                    className="text-primary pt-[2px] pl-2"
                                  >
                                    <X size={15} strokeWidth="3px" />
                                  </button>
                                </span>
                              ))}
                            </div>
                            <input
                              type="text"
                              placeholder="Enter hashtags here "
                              className="w-full pt-2 border-none border-gray-300 rounded-md outline-none"
                              onKeyDown={addHashtag}
                            />
                          </div>
                        </div>
                        <p className="text-sm text-subtitle mt-2">
                          Hashtag is word, which allows users to discover your posts and services.
                        </p>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfoHashtags;
