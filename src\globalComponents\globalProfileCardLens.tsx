"use client";
import { Badge } from "@/components/ui/badge";
import useAuth from "@/hook";
import Link from "next/link";
import { LENS_CONTRACT_ABI, LENS_CONTRACT_ADDRESS } from "@/const/contracts";
import { useCallback, useEffect, useState, useRef } from "react";
import { useUnFollow } from "@/lib/useUnfollow";
import { useFollow } from "@/lib/useFollow";
import useLensUser from "@/lib/auth/useLensUser";
import { GetWalletId } from "@/services/authBridgeService";
import { useAccountQuery } from "@/graphql/test/generated";
import { useAccount } from "wagmi";
// Import the global sign-in store
import { useSignInStore } from "@/components/GlobalSignInButton";

type Status = "follow" | "unfollow";

const GlobalProfileCardLens = (props: any) => {
  const isAuthW3 = GetWalletId();
  const [isLoading, setIsLoading] = useState(false);
  const { address, isConnected } = useAccount();

  // Add this state to track local follow status
  const [localFollowStatus, setLocalFollowStatus] = useState<boolean | undefined>(undefined);

  // Add a state to track if we're waiting for the initial data
  const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);

  // Add a state to track if this component is mounted
  const isMounted = useRef(true);

  // **************************** lens follow user *********************
  const { mutateAsync: followUser, isSuccess, data: followData } = useFollow();
  const [Txn_id, setTxn_id] = useState<string | null>(null);
  const [CurrentStatus, setCurrentStatus] = useState<Status>("follow");

  useEffect(() => {
    // console.log({ isSuccess, followData });

    if (isSuccess) {
      setTxn_id(
        // @ts-ignore
        followData.txId
      );
    }
  }, [isSuccess, followData, Txn_id]);

  const unfollow = useUnFollow();

  const { data: profileData, refetch } = useAccountQuery(
    {
      request: {
        address: props?.id,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!props?.id,
    }
  );

  // Set up the mounted ref
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Initialize localFollowStatus from profileData when it loads
  useEffect(() => {
    if (profileData?.account?.operations && isMounted.current) {
      setLocalFollowStatus(profileData.account.operations.isFollowedByMe);
      setCurrentStatus(profileData.account.operations.isFollowedByMe ? "unfollow" : "follow");
      setIsInitialDataLoaded(true);
      // Ensure loading is false when data is loaded
      setIsLoading(false);
    }
  }, [profileData]);

  // Force refetch when props.id changes to ensure we have the latest follow status
  useEffect(() => {
    if (props.id) {
      // Reset states immediately
      setIsInitialDataLoaded(false);
      setIsLoading(false);

      // Use requestAnimationFrame to ensure the UI updates before refetching
      // This is more reliable than setTimeout
      requestAnimationFrame(() => {
        if (isMounted.current) {
          refetch()
            .then(() => {
              if (isMounted.current) {
                // Ensure loading is false after refetch
                setIsLoading(false);
                setIsInitialDataLoaded(true);
              }
            })
            .catch(() => {
              if (isMounted.current) {
                // Ensure loading is false even if refetch fails
                setIsLoading(false);
                setIsInitialDataLoaded(true);
              }
            });
        }
      });
    }
  }, [props.id, refetch]);

  // Reset loading state if component unmounts or props change
  useEffect(() => {
    return () => {
      // When unmounting, isMounted will be set to false in the cleanup function
      // of the first useEffect, so we don't need to check it here
      setIsLoading(false);
      setIsInitialDataLoaded(false);
    };
  }, [props.id]);

  return (
    <div className="w-full">
      <div className="row justify-between mb-3 w-full ">
        <div className="row gap-2">
          <div>
            <img
              src={props.avatar ? props.avatar : "/assets/profileAvatar.svg"}
              alt=""
              className="w-[40px] h-[40px] min-h-[40px] min-w-[40px] rounded-full object-cover"
              style={{
                border: "3px solid",
                borderColor: props?.themeProperties?.backgroundColor || "#000",
              }}
            />
          </div>
          <Link href={`/profile/lens/${props.id}`}>
            <div className="cursor-pointer">
              <p className="font-bold font-sf">{props.profile_name || "Profile Name*"}</p>
              <p className="text-[#616770] line-clamp-1 -mt-2">{props.location || "Location*"}</p>
            </div>
          </Link>
        </div>

        {address || isConnected ? (
          <div>
            {!isInitialDataLoaded ? (
              // Show loading state while waiting for initial data
              <Badge
                className="btn-xs font-normal font-sf border-primary btn min-w-20 w-20 pointer-events-none opacity-70"
                variant="outline"
              >
                <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
              </Badge>
            ) : localFollowStatus === false ? (
              <Badge
                className={`btn-xs text-white min-w-20 w-20 ${
                  isLoading ? "pointer-events-none opacity-70" : ""
                }`}
                onClick={async () => {
                  if (isLoading) return; // Extra protection
                  try {
                    setIsLoading(true);
                    // Update local state immediately for responsive UI
                    setLocalFollowStatus(true);
                    setCurrentStatus("follow");

                    // Notify parent component about the follow action (if props.onFollowChange exists)
                    if (props.onFollowChange) {
                      props.onFollowChange(props.id, true);
                    }

                    await followUser(props?.id);
                    // Refetch to ensure server state is in sync
                    refetch();
                  } catch (error) {
                    // Revert on error
                    setLocalFollowStatus(false);
                    console.error("Follow failed:", error);
                  } finally {
                    setIsLoading(false);
                  }
                }}
              >
                {isLoading ? (
                  <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                ) : (
                  "Follow"
                )}
              </Badge>
            ) : (
              <Badge
                className={`btn-xs font-normal font-sf border-primary btn min-w-20 w-20 ${
                  isLoading ? "pointer-events-none opacity-70" : ""
                }`}
                variant="outline"
                onClick={async () => {
                  if (isLoading) return; // Extra protection
                  try {
                    setIsLoading(true);
                    // Update local state immediately for responsive UI
                    setLocalFollowStatus(false);
                    setCurrentStatus("unfollow");

                    // Notify parent component about the unfollow action (if props.onFollowChange exists)
                    if (props.onFollowChange) {
                      props.onFollowChange(props.id, false);
                    }

                    const resp = await unfollow(props?.id);
                    // Refetch to ensure server state is in sync
                    refetch();
                  } catch (error) {
                    // Revert on error
                    setLocalFollowStatus(true);
                    console.error("Unfollow failed:", error);
                  } finally {
                    setIsLoading(false);
                  }
                }}
                style={{ fontWeight: 400 }}
              >
                {isLoading ? (
                  <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                ) : (
                  "Unfollow"
                )}
              </Badge>
            )}
          </div>
        ) : (
          <Badge
            className="btn-xs font-normal font-sf text-white min-w-20 w-20"
            onClick={() => {
              // Use the global sign-in store to open the wallet connect modal
              useSignInStore.getState().setIsOpen(true);
            }}
          >
            Follow
          </Badge>
        )}
      </div>
    </div>
  );
};

export default GlobalProfileCardLens;
