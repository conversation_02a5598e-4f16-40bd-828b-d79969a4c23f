"use client";
import {
  ExplorePublicationsQuery,
  PublicationsQuery,
} from "@/graphql/generated";
import { Play } from "react-feather";
import { exportData } from "./lensPost";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";

type Props = {
  publication:
    | ExplorePublicationsQuery["explorePublications"]["items"][0]
    | PublicationsQuery["publications"]["items"][0];
};

export default function ImageGrid({ data }: any) {

  return (
    <>
      {/* for post dispaly grid */}
      <div className="max-md:hidden md:overflow-hidden grid-cols-4 max-w-[400px]">
        {Array.from({ length: 4 }).map((_, indexs) => (
          <div key={indexs} className="mt-0">
            {Array.from({ length: 1 }).map(
              (_, index) =>
                true && (
                  <div className="w-full mt-0" key={indexs}>
                    <div className="grid grid-cols-6 gap-[1px] mb-[1px] min-h-[100px]">
                      <img
                        src={
                          sanitizeDStorageUrl(
                            exportData[indexs + index]?.metadata?.asset?.image
                            ? exportData[indexs + index]?.metadata?.image?.item
                            : exportData[indexs + index]?.metadata?.video?.cover
                            )
                        }
                        alt=""
                        className=" col-span-4 h-full w-full object-cover border-2 max-h-[116px]"
                        // style={{
                        //   borderColor: themeProperties.backgroundColor,
                        // }}
                      />
                      <div className="w-full relative col-span-2 h-full">
                        <img
                          src={
                            sanitizeDStorageUrl(exportData
                              ? exportData[indexs + index + 1]?.metadata?.image?.item
                              : "")
                          }
                          alt=""
                          className=" w-full border-2 max-h-[116px] object-cover h-full"
                          //   style={{
                          //     borderColor: themeProperties.backgroundColor,
                          //   }}
                        />
                        <span className="absolute top-1 right-1 text-white row gap-1">
                          <img
                            src="/assets/lens.png"
                            alt=""
                            className="h-5 w-7"
                          />
                          <Play />
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-6 gap-[1px]">
                      <div className=" col-span-2 h-full">
                        <img
                          src={
                           sanitizeDStorageUrl( exportData[indexs + index + 2]?.metadata?.image?.item)
                          }
                          alt=""
                          className="  w-full border-2 object-cover h-1/2 mb-[1px]"
                          //   style={{
                          //     borderColor: themeProperties.backgroundColor,
                          //   }}
                        />
                        <img
                          src={
                            sanitizeDStorageUrl(exportData[indexs + index + 3]?.metadata?.image?.item)
                          }
                          alt=""
                          className="w-full border-2 object-cover h-1/2"
                          //   style={{
                          //     borderColor: themeProperties.backgroundColor,
                          //   }}
                        />
                      </div>
                      <img
                        src={
                          sanitizeDStorageUrl(exportData[indexs + index + 4]?.metadata?.image?.item)
                        }
                        alt=""
                        className=" col-span-4  w-full h-full object-cover border-2 min-h-[200px]"
                        // style={{
                        //   borderColor: themeProperties.backgroundColor,
                        // }}
                      />
                    </div>
                  </div>
                )
            )}
          </div>
        ))}
      </div>
    </>
  );
}
