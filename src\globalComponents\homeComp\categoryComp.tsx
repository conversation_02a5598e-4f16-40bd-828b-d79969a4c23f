import Link from "next/link";

const CategoryComp = (props: any) => {
  function hexToRgba(hex: any, alpha = 1) {
    const [r, g, b] = hex
      .replace(/^#/, "")
      .match(/.{1,2}/g)
      .map((val: any) => parseInt(val, 16));
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  return (
    <>
      <div className=" md:sticky md:-top-[9.5rem] bg-white z-20 pb-[2px] pt-2 max-md:pt-0">
        <Link href={`/selectedCategory/${props.item.title}`}>
          <div
            className="relative w-full  md:mb-3  max-md:min-h-[100px] min-h-[200px] "
            id="img"
            // style={{ height: `${imgHeight}px` }}
          >
            {" "}
            {/* Adjust height as needed */}
            <img
              src={props.item.img}
              alt="Sample Image"
              className="md:w-[350px] h-[200px]  md:rounded-md mb-3 max-md:mb-0 md:min-w-[350px] object-cover max-md:w-full max-md:rounded-tl-[60px] transition-transform duration-200 max-md:hidden"
              // style={{ transform: `scale(${scale})` }}
            />
            <img
              src={props.item.imgMobile}
              alt="Sample Image"
              className="md:w-[350px] h-[110px]  md:rounded-md max-md:mb-0 md:min-w-[350px] object-cover max-md:w-full max-md:rounded-tl-[60px] transition-transform duration-200 md:hidden"
              // style={{ transform: `scale(${scale})` }}
            />
            {/* Gradient overlay */}
            <div
              className="absolute inset-0 z-10 bottom-0 rounded-md max-md:hidden"
              style={{
                background: `linear-gradient(359.81deg, ${hexToRgba(
                  props.item.backgroundColor,
                  1
                )} 16.34%, ${hexToRgba(props.item.backgroundColor, 0.444)} 44.01%, ${hexToRgba(
                  props.item.backgroundColor,
                  0
                )} 105.33%)`,
              }}
            />
            {/* for mobile view */}
            <div
              className="absolute inset-0 z-10 bottom-0 md:rounded-md md:hidden max-md:rounded-tl-[60px]" // Hidden on medium and larger screens
              style={{
                background: `linear-gradient(83.81deg, ${hexToRgba(
                  props.item.backgroundColor,
                  0.9
                )} 12.75%, ${hexToRgba(
                  props.item.backgroundColor,
                  0
                )} 49.03%, ${hexToRgba(props.item.backgroundColor, 1)} 87.61%)`,
              }}
            />
            <div className=" absolute bottom-4 z-20 left-6">
              <p className="text-white text-xl font-bold">{props.item.title}</p>
            </div>
          </div>
        </Link>
      </div>
    </>
  );
};

export default CategoryComp;
