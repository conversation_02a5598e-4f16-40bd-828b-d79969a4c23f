"use client";
import { useEffect, useState, useRef } from "react";

import React from "react";

import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import {
  FollowersOrderBy,
  FollowersQuery,
  PageSize,
  useFollowersQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";
import EmptyState from "@/components/EmptyState";
const Following = (otherUserID: any) => {
  const [currentFollowersCursor, setCurrentFollowersCursor] = React.useState<string | null>(null);
  const [allFollowers, setAllFollowers] = useState<FollowersQuery["followers"]["items"]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true); // Track if this is the first load
  const [isPaginating, setIsPaginating] = useState(false); // Track pagination loading
  const [localLoading, setLocalLoading] = useState(true); // Local loading state for skeleton
  const [scrollPosition, setScrollPosition] = useState(0); // Store scroll position
  const containerRef = useRef<HTMLDivElement>(null); // Reference to scroll container

  // Add a function to handle follow/unfollow state changes
  const handleFollowChange = (profileId: string, isFollowed: boolean) => {
    // Update the UI immediately for better user experience
    const updatedFollowers = [...allFollowers];
    for (let i = 0; i < updatedFollowers.length; i++) {
      if (updatedFollowers[i].follower.address === profileId) {
        try {
          // Try to update the follow status directly
          // This might not work due to TypeScript issues, but it's worth trying
          // @ts-ignore - Ignore TypeScript errors for this operation
          updatedFollowers[i].follower.operations.isFollowedByMe = isFollowed;
        } catch (error) {
          console.error("Error updating follow status:", error);
        }
      }
    }

    try {
      // Try to update the followers list
      // @ts-ignore - Ignore TypeScript errors for this operation
      setAllFollowers(updatedFollowers);
    } catch (error) {
      console.error("Error updating followers list:", error);
    }
  };

  const { data: followers, isLoading: followers_loading } = useFollowersQuery(
    {
      request: {
        account: otherUserID.otherUserID,
        pageSize: PageSize.Fifty,
        orderBy: FollowersOrderBy.Desc,
        cursor: currentFollowersCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // console.log(followers);

  // Reset data when otherUserID changes
  useEffect(() => {
    setAllFollowers([]);
    setCurrentFollowersCursor(null);
    setIsInitialLoad(true);
    setIsPaginating(false);
    setLocalLoading(true);
  }, [otherUserID.otherUserID]);

  // Handle loading states
  useEffect(() => {
    // Set pagination loading state when loading starts
    if (followers_loading) {
      // If we have existing followers, this is pagination
      if (allFollowers.length > 0) {
        setIsPaginating(true);
        // Store current scroll position before pagination
        if (containerRef.current) {
          setScrollPosition(containerRef.current.scrollTop);
        }
      } else {
        // This is initial load
        setIsInitialLoad(true);
      }
    } else {
      // Loading finished
      setIsPaginating(false);
      setIsInitialLoad(false);

      // Restore scroll position after pagination (with a small delay)
      if (containerRef.current && scrollPosition > 0) {
        setTimeout(() => {
          if (containerRef.current) {
            containerRef.current.scrollTop = scrollPosition;
          }
        }, 100);
      }
    }
  }, [followers_loading, allFollowers.length, scrollPosition]);

  // Effect to manage local loading state - only show skeleton on initial load
  useEffect(() => {
    // Only show skeleton loader on initial load, not during pagination
    if (isInitialLoad && followers_loading) {
      setLocalLoading(true);
    }

    // Only transition to non-loading state when loading is complete
    if (!followers_loading) {
      // Use a single timeout to prevent state flickering
      const timer = setTimeout(() => {
        setLocalLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [followers_loading, isInitialLoad]);

  useEffect(() => {
    if (followers?.followers?.items) {
      // Only append new data if it's not already in the list
      const newItems = followers.followers.items.filter(
        (newItem) =>
          !allFollowers.some(
            (existingItem) => existingItem.follower.address === newItem.follower.address
          )
      );

      if (newItems.length > 0) {
        setAllFollowers((prev) => [...prev, ...newItems]);
      }
    }
  }, [followers, allFollowers]);

  const loadNextFollowers = () => {
    const nextCursor = followers?.followers?.pageInfo?.next;
    if (nextCursor) {
      setCurrentFollowersCursor(nextCursor);
    }
  };

  // Function to detect scroll bottom
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight + 20) {
      loadNextFollowers();
    }
  };

  return (
    <div
      className="overflow-y-scroll hide-scroll-custom bg-white h-[calc(100vh-280px)] max-md:h-[calc(100vh-107px)]"
      onScroll={handleScroll}
      ref={containerRef}
    >
      {localLoading ? (
        <div className="p-4">
          <ProfileCardSkeleton count={6} columns={2} showGrid={true} />
        </div>
      ) : allFollowers.length === 0 ? (
        <EmptyState
          type="followers"
          title="No followers yet"
          message={
            otherUserID.otherUserID && otherUserID.otherUserID !== "my-profile"
              ? "This user doesn't have any followers yet."
              : "When people follow you, they'll appear here. Share your profile to get more followers!"
          }
          isOwnProfile={!(otherUserID.otherUserID && otherUserID.otherUserID !== "my-profile")}
          customIcon="/assets/empty-followers.svg"
        />
      ) : (
        <div>
          <div className="grid grid-cols-2 max-md:grid-cols-1 gap-3 p-4">
            {allFollowers.map((curr: FollowersQuery["followers"]["items"][0], index) => {
              return (
                <div key={index} className="w-full">
                  <div className="row justify-between mt-0">
                    <GlobalProfileCardLens
                      themeProperties="#00"
                      isFollow={curr?.follower?.operations?.isFollowedByMe}
                      location={
                        getLocation(curr?.follower?.metadata?.attributes) ||
                        curr?.follower?.username?.localName ||
                        "location*"
                      }
                      profile_name={curr.follower?.metadata?.name || "profile name"}
                      avatar={
                        curr?.follower?.metadata?.picture ??
                        "https://static.hey.xyz/images/default.png"
                      }
                      id={curr?.follower.username?.localName}
                      onFollowChange={handleFollowChange}
                    />
                  </div>
                </div>
              );
            })}
          </div>

          {/* Pagination loader - show at bottom when paginating */}
          {isPaginating && (
            <div className="p-4">
              <ProfileCardSkeleton count={2} columns={2} showGrid={true} />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Following;
