"use client";
import { <PERSON>, Edit2, <PERSON><PERSON>, <PERSON> } from "react-feather";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { updateUser } from "@/services/usersServices";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
const ProfileInfoPersonalMotto = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [personalMotto, setPersonalMotto]: any = useState(props?.personalMotto);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");

  // fetch data from firebase
  const auth = useAuth();
  const profile = useProfile(auth?.userData?.uid);

  // No longer using useEffect to handle success state
  // Now handling it directly in the handleSubmit function

  const handleSubmit = async () => {
    if (personalMotto) {
      setUploadStatus("uploading");

      try {
        const updatedData = {
          personal_moto: personalMotto,
        };
        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setUploadStatus("success");

          // Set a timeout to close the modal and refresh the data
          setTimeout(() => {
            // Trigger parent component to refresh data
            props.onClickAction(true);

            // Close the modal
            setIsOpen(false);

            // Reset states
            setPersonalMotto("");
            setUploadStatus("idle");
          }, 1500);

          // Force close the modal after a longer delay if it's still open
          setTimeout(() => {
            setIsOpen(false);
          }, 2000);
        } else {
          console.error("Error updating Personal Motto:", response.error);
          setUploadStatus("idle");
        }
      } catch (error) {
        console.error("Error updating Personal Motto:", error);
        setUploadStatus("idle");
      }
    }
  };
  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">Personal Motto </span>

              <p className="text-primary max-md:text-sm">{props?.personalMotto}</p>
            </div>
            {!props.isOtherProfile && (
              <div
                onClick={() => {
                  setIsOpen(true), setPersonalMotto(props?.personalMotto);
                }}
                className=" cursor-pointer"
              >
                <Edit2 color={props.bgColor} className="max-md:h-[20px]" />
              </div>
            )}
          </div>
        </div>
      </div>
      {/* PersonalMotto Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={(open) => {
            // Always allow closing the modal
            setIsOpen(open);
          }}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {uploadStatus === "success" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Personal Motto Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your personal motto has been updated and the changes will be visible on your
                        profile.
                      </p>
                    </div>
                  ) : uploadStatus === "uploading" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Updating Your Personal Motto...
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your information. This may take a moment.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div onClick={() => setIsOpen(false)} className="cursor-pointer">
                          <X />
                        </div>
                        <p className="font-bold text-primary">Edit Personal Motto</p>
                        <div
                          className={`flex items-center ${
                            personalMotto
                              ? "text-primary cursor-pointer"
                              : "text-borderColor cursor-not-allowed"
                          }`}
                          onClick={personalMotto ? handleSubmit : undefined}
                        >
                          Save
                        </div>
                      </div>

                      <div>
                        <div className="grid w-full items-center gap-1.5 mt-6">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Personal Motto
                          </Label>
                          <Textarea
                            placeholder="Tell the world about you and your services"
                            className="resize-none h-60 outline-none text-lg text-primary"
                            value={personalMotto}
                            onChange={(e) => setPersonalMotto(e.target.value)}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfoPersonalMotto;
