"use client";
import React, { useEffect, useState, useRef } from "react";
import Image from "next/image";
import { useLoading } from "@/context/LoadingContext";

interface FullScreenLoaderProps {
  message?: string;
  subMessage?: string;
}

const FullScreenLoader: React.FC<FullScreenLoaderProps> = () => {
  const [logoIndex, setLogoIndex] = useState(0);
  const { hideLoader } = useLoading();
  // Initialize with a default value to avoid SSR issues
  const lastVisibilityState = useRef<DocumentVisibilityState>("visible");
  const walletConnectionDetected = useRef(false);

  // Logo rotation array
  const logos = [
    "/assets/logo/Default.png",
    "/assets/logo/Yellow.png",
    "/assets/logo/Red.png",
    "/assets/logo/Blue.png",
    "/assets/logo/green.png",
    "/assets/logo/Purple.png",
    "/assets/logo/Violet.png",
  ];

  // Initialize document-related values after component mounts (client-side only)
  useEffect(() => {
    // Update the ref with the actual document.visibilityState
    if (typeof document !== "undefined") {
      lastVisibilityState.current = document.visibilityState;
    }
  }, []);

  // Detect wallet connection by checking URL and localStorage
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === "undefined" || typeof localStorage === "undefined") {
      return;
    }

    // Check if there's a lens-user in localStorage, which indicates wallet connection
    const lensUser = localStorage.getItem("lens-user");
    const isWalletConnectInUrl =
      window.location.href.includes("wc:") || window.location.href.includes("walletconnect");

    if (lensUser || isWalletConnectInUrl) {
      walletConnectionDetected.current = true;

      // Set a safety timeout to hide the loader after 20 seconds
      // This ensures the loader doesn't stay forever if the visibility event doesn't trigger
      const safetyTimeout = setTimeout(() => {
        hideLoader();
      }, 20000);

      return () => clearTimeout(safetyTimeout);
    }
  }, [hideLoader]);

  // Handle visibility change to detect when user returns from wallet app
  useEffect(() => {
    // Only run in browser environment
    if (typeof document === "undefined") {
      return;
    }

    const handleVisibilityChange = () => {
      // If we were hidden and now we're visible again, and wallet connection was detected
      if (
        lastVisibilityState.current === "hidden" &&
        document.visibilityState === "visible" &&
        walletConnectionDetected.current
      ) {
        // Hide the loader when user returns from wallet app
        hideLoader();
      }

      lastVisibilityState.current = document.visibilityState;
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [hideLoader]);

  // Animate logo rotation - same as LoadingOverlay
  useEffect(() => {
    const interval = setInterval(() => {
      setLogoIndex((prevIndex) => (prevIndex + 1) % logos.length);
    }, 250); // Change image every 0.25 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div
      className="fixed inset-0 z-[9999] flex flex-col items-center justify-center bg-white"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: "100vw",
        height: "100vh",
        backgroundImage:
          "radial-gradient(circle at 25px 25px, #f5f5f5 2%, transparent 0%), radial-gradient(circle at 75px 75px, #f5f5f5 2%, transparent 0%)",
        backgroundSize: "100px 100px",
      }}
    >
      <div className="flex flex-col items-center justify-center p-6 text-center max-w-md">
        {/* Rotating Logos - same as LoadingOverlay */}
        <div className="w-24 h-24 mb-4">
          <Image
            src={logos[logoIndex]}
            alt="Loading Logo"
            width={96}
            height={96}
            className="animate-fade-in"
          />
        </div>

        {/* Shimmering AMUZN Text - same as LoadingOverlay */}
        <div className="text-2xl font-sf font-bold text-gray-800 animate-shimmer">AMUZN</div>
      </div>

      {/* Animated colorful bottom bar */}
      <div className="absolute bottom-0 left-0 right-0 h-3 flex overflow-hidden">
        <div className="flex-1 bg-[#E5B045] animate-pulse"></div>
        <div
          className="flex-1 bg-[#CF5943] animate-pulse"
          style={{ animationDelay: "150ms" }}
        ></div>
        <div
          className="flex-1 bg-[#3C5F9A] animate-pulse"
          style={{ animationDelay: "300ms" }}
        ></div>
        <div
          className="flex-1 bg-[#46B933] animate-pulse"
          style={{ animationDelay: "450ms" }}
        ></div>
        <div
          className="flex-1 bg-[#E073D2] animate-pulse"
          style={{ animationDelay: "600ms" }}
        ></div>
        <div
          className="flex-1 bg-[#5331BC] animate-pulse"
          style={{ animationDelay: "750ms" }}
        ></div>
      </div>

      {/* Tailwind CSS animations - same as LoadingOverlay */}
      <style jsx>{`
        @keyframes shimmer {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 1;
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite ease-in-out;
        }

        @keyframes fade-in {
          0% {
            opacity: 1;
            transform: scale(0.9);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default FullScreenLoader;
