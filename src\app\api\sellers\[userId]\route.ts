import { NextRequest, NextResponse } from 'next/server';
import { getCompleteSellerInfo } from '@/services/sellerService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const result = await getCompleteSellerInfo(userId);

    if (result.success) {
      return NextResponse.json({
        success: true,
        seller: result.seller,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
      }, { status: 404 });
    }
  } catch (error) {
    console.error('Error fetching seller info:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch seller information',
    }, { status: 500 });
  }
}
