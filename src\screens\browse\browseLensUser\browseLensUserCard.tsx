import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../theme";
import {
  Check,
  CheckCircle,
  Circle,
  Copy,
  Play,
  X,
  Twitter,
  Facebook,
  Instagram,
  Loader,
} from "react-feather";
import { <PERSON><PERSON>, <PERSON>dalB<PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react";
import { Star, MessageSquare, Bookmark, MoreHorizontal } from "react-feather";
import { useEffect, useRef, useState } from "react";
import LazyMedia from "@/components/LazyMedia";

import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import React from "react";
import { Label } from "@radix-ui/react-label";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import useAuth from "@/hook";
import { PublicationReportingReason } from "@/graphql/generated";
import { useReaction } from "@/lib/useReaction";
import { useComment } from "@/lib/useCommen";
import { useQueryClient } from "@tanstack/react-query";
import { useBookMark } from "@/lib/useBookMark";

import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import Video from "yet-another-react-lightbox/plugins/video";
import {
  PageSize,
  PostReactionType,
  PostReferenceType,
  PostReportReason,
  PostsQuery,
  PostVisibilityFilter,
  ReferenceRelevancyFilter,
  usePostReferencesQuery,
  useReportPostMutation,
  useTransactionStatusQuery,
} from "@/graphql/test/generated";
import { getFormattedTime, getLocation } from "@/lib/utils";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";
import SignInButton from "@/components/SignInButton";
import Link from "next/link";
import { useAccount } from "wagmi";
import ServiceDescription from "@/globalComponents/formatText";
import { useSignInStore } from "@/components/GlobalSignInButton";
import Zoom from "yet-another-react-lightbox/plugins/zoom";

const resonData = ["FRAUD", "ILLEGAL", "SENSITIVE", "SPAM"];

function getReasonKey(reason: any) {
  switch (reason) {
    case PublicationReportingReason.Fraud:
      return "fraudReason";
    case PublicationReportingReason.Illegal:
      return "illegalReason";
    case PublicationReportingReason.Sensitive:
      return "sensitiveReason";
    case PublicationReportingReason.Spam:
      return "spamReason";
    default:
      return null;
  }
}

const BrowseLensUserCard = (props: {
  cardData: PostsQuery["posts"]["items"][0];
  border: string;
  userID: string;
  location: any;
  profileAvatar: any;
  profileName: any;
  localName: any;
  category: any;
}) => {
  const { address } = useAccount();

  const user = useAuth();
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [showMoreOptionsmodl, setShowMoreOptionsmodl] = useState(false);

  const [isOpen, setIsOpen] = useState(false);
  const [openVideo, setOpenVideo] = useState(false);
  const [openImage, setOpenImage] = useState(false);
  const [isResonPost, setIsResonPost] = useState(false);
  const [isDeletePost, setIsDeletePost] = useState(false);
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isOpenShare, setIsOpenShare] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const postsRef = useRef<HTMLDivElement>(null);
  const [toggleSave, setToggleSave] = useState(false);
  const [toggleStar, setToggleStar] = useState(false);
  const [starCount, setStarCount] = useState(0);
  const [reload, setSetreload] = useState(0);

  const [isOpenSheet, setIsOpenSheet] = useState(false);
  const [isTrue, setIsTrue] = useState(false);
  const [isCheck, setIsCheck] = useState(Array(0).fill(false));
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null); // Track selected icon index
  const [reportComment, setReportComment] = useState("");
  const [reportRespon, setReportReason] = useState("");
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [isImageLoading, setIsImageLoading] = useState(true);

  const REPORT_STATUS = {
    IDLE: "idle" as const,
    UPLOADING: "uploading" as const,
    SUCCESS: "success" as const,
  };

  type ReportStatus = (typeof REPORT_STATUS)[keyof typeof REPORT_STATUS];
  const [reportStatus, setReportStatus] = useState<ReportStatus>(REPORT_STATUS.IDLE);

  // Effect to handle report success state
  useEffect(() => {
    if (reportStatus === REPORT_STATUS.SUCCESS) {
      // Wait 1.5 seconds before closing the modal to show success state
      const timer = setTimeout(() => {
        setIsResonPost(false);
        // Reset form and status
        setReportReason("");
        setReportComment("");
        setReportStatus(REPORT_STATUS.IDLE);
        // Reset the checkbox states
        setIsCheck(Array(resonData.length).fill(false));
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [reportStatus]);

  const handleStarClick = () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }
    setToggleStar(!toggleStar);
    toggleStar ? setStarCount(0) : setStarCount(1);
  };

  const handleSaveClick = () => {
    if (!user?.isLogin) {
      setIsSigninOpen(true);
      return;
    }
    setToggleSave(!toggleSave);
  };
  const queryClient = useQueryClient();

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ["publications", postId] });
  };
  const handleMessageClick = () => {
    setIsOpen(true);
  };

  const handleIsCheck = (id: number) => {
    setReportReason(resonData[id]);
    const newState = [...isCheck];
    newState[id] = !newState[id]; // Toggle the specific icon's state
    setIsCheck(newState);
  };

  const handleInfoClick = (index: number) => {
    setSelectedIndex(index === selectedIndex ? null : index); // Deselect if clicked again
    setIsTrue(true);
  };

  // **************************** comments **********************
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);
  // console.log(props.cardData.id);
  const [message, setMessage] = useState("");
  const [temp, setTemp] = useState(false);
  const [postId, setPostId] = useState(props.cardData.id);

  const commentResp = useComment();

  const {
    isLoading: commentsLoading,
    data: comments,
    error: commentsError,
    refetch: refetchComments,
  } = usePostReferencesQuery(
    {
      request: {
        referencedPost: props.cardData.id, // post id,
        referenceTypes: [PostReferenceType.CommentOn],
        cursor: currentCursor,
        pageSize: PageSize.Fifty,
        relevancyFilter: ReferenceRelevancyFilter.All,
        visibilityFilter: PostVisibilityFilter.Visible,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  const [isDisable, setIsDisable] = useState(false);
  const [Txn_id, setTxn_id] = useState<string | null>(null);
  const { data: transactionData, refetch: refetchTransactionStatus } = useTransactionStatusQuery(
    {
      request: {
        txHash: Txn_id,
      },
    },
    {
      enabled: !!Txn_id,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (!Txn_id) return;
    // console.log({ Txn_id });

    const POLL_INTERVAL = 1000;
    const MAX_CHECK_COUNT = 15;
    let checkCnt = 0;

    const intervalId = setInterval(async () => {
      if (checkCnt > MAX_CHECK_COUNT) {
        alert(` timeout `);
        clearInterval(intervalId);
      }
      if (
        transactionData?.transactionStatus?.__typename === "FinishedTransactionStatus" ||
        transactionData?.transactionStatus?.__typename === "FailedTransactionStatus"
      ) {
        alert(`comment added successfully ✅`);
        await refetchComments();
        setMessage("");
        setIsOpen(false);
        setIsDisable(false);
        setTemp(true);

        clearInterval(intervalId);
      } else {
        checkCnt++;
        await refetchTransactionStatus();
      }
    }, POLL_INTERVAL);

    return () => clearInterval(intervalId); // Cleanup on unmount
  }, [Txn_id, transactionData, refetchTransactionStatus]);

  const handleComment = async () => {
    if (!address) {
      sessionStorage.setItem("input", message);
      sessionStorage.setItem("openPost", props.cardData.id);
      useSignInStore.getState().setIsOpen(true);
      return;
    }
    try {
      console.log("Click Comment");
      setIsDisable(true);
      const Check = false;
      // props.cardData.momoka === null ? false : true;
      const resp = await commentResp(props.cardData.id, message, Check);
      if (resp?.post?.__typename === "PostResponse") {
        setTxn_id(resp?.post?.hash);
      }
    } catch (error: any) {
      alert(error?.message ?? "something went wrong");
      setIsDisable(false);
    }
  };

  useEffect(() => {
    if (comments) {
      // console.log({ comments });
      // setAllFollowers((prev) => [...prev, ...followers.followers.items]); // Append new data
    }
  }, [comments, temp, postId]);

  //  like dislike
  const addReaction = useReaction();
  const bookmarkResp = useBookMark();

  const handlikedislike = async () => {
    if (!address) {
      useSignInStore.getState().setIsOpen(true);
      return;
    }
    // console.log(props.cardData.operations.hasReacted);
    try {
      if (props.cardData?.__typename !== "Post") {
        console.log("not a post");
        return;
      }

      if (!props.cardData?.operations?.hasReacted) {
        const resplike = await addReaction(props.cardData.id, PostReactionType.Upvote, "like");
        if (props.cardData.operations) {
          props.cardData.operations.hasReacted = true;
        }

        if (props?.cardData?.stats?.reactions) {
          props.cardData.stats.reactions++;
        }

        // console.log({ resplike });
      } else {
        const respdislike = await addReaction(
          props.cardData.id,
          PostReactionType.Downvote,
          "dislike"
        );
        if (props.cardData.operations) {
          props.cardData.operations.hasReacted = false;
        }

        if (props?.cardData?.stats?.reactions) {
          props.cardData.stats.reactions--;
        }
        // console.log({ respdislike });
      }
    } catch (error: any) {
      alert(error?.message ?? "something went wrong");
    }
  };

  const handBookMark = async () => {
    if (!user?.isLoginLens) {
      useSignInStore.getState().setIsOpen(true);
      return;
    }
    try {
      if (props.cardData?.__typename !== "Post") {
        console.log("not a post");
        return;
      }
      if (props?.cardData?.operations?.hasBookmarked) {
        const resp = await bookmarkResp(props.cardData.id, false);
        props.cardData.operations.hasBookmarked = false;
      } else {
        const resp = await bookmarkResp(props.cardData.id, true);
        if (props.cardData.operations) {
          props.cardData.operations.hasBookmarked = true;
        }
      }
    } catch (error) {
      alert(error ?? "something went wrong");
    }
  };

  useEffect(() => {
    // console.log(props.cardData.operations.hasReacted);
  }, [
    handlikedislike,
    handBookMark,
    props?.cardData?.__typename === "Post" && props.cardData?.operations?.hasBookmarked,
    props?.cardData?.__typename === "Post" && props.cardData?.operations?.hasReacted,
  ]);

  // Handle Report

  const { mutateAsync: reportPost } = useReportPostMutation();

  // Function to render the appropriate content based on report status
  const renderReportContent = () => {
    if (reportStatus === REPORT_STATUS.SUCCESS) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">Report Submitted Successfully!</h2>
          <p className="text-gray-500 text-center">
            Thank you for your report. We will review it and take appropriate action.
          </p>
        </div>
      );
    }

    if (reportStatus === REPORT_STATUS.UPLOADING) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Submitting Your Report...</h2>
          <p className="text-gray-500 text-center">
            Please wait while we process your report. This may take a moment.
          </p>
        </div>
      );
    }

    // Default form content - will be handled in the modal
    return null;
  };

  // shere post
  function copyProfileLink(id: string) {
    const category =
      props.category === "Storytelling"
        ? "Literature"
        : props.category || "";
    const encodedCategory = encodeURIComponent(category);
    const url = `https://www.amuzn.app/browse/${encodedCategory}/${id}%20${props.userID}`;
    navigator.clipboard
      .writeText(url)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy link: ", err);
      });
  }

  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoUrl, setVideoUrl] = useState("");
  const [imageUrl2, setImageUrl2] = useState("");

  const handleClick = (event: React.MouseEvent<HTMLVideoElement>, vdoUrl: string) => {
    event.preventDefault();
    event.stopPropagation();
    setVideoUrl(vdoUrl);
    setOpenVideo(true);
  };

  // Handle lightbox open for LazyMedia component
  const handleLightboxOpen = (src: string, type: "image" | "video") => {
    if (type === "image") {
      setImageUrl2(src);
      setOpenImage(true);
    } else if (type === "video") {
      setVideoUrl(src);
      setOpenVideo(true);
    }
  };

  const hexToRgb = (hex: string) => {
    hex = hex.replace(/^#/, "");
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `${r}, ${g}, ${b}`;
  };

  // Helper function to render media (image or video)
  const renderMedia = (post: any, className: string) => {
    if (!post) return null;

    if (post?.metadata?.image?.item) {
      return (
        <LazyMedia
          src={sanitizeDStorageUrl(post?.metadata?.image?.item || "")}
          alt={post.metadata.image.item || "Post image"}
          type="image"
          className={className}
          style={{
            backgroundColor: `rgba(${hexToRgb(props.border)}, 0.3)`,
            borderColor: props.border,
            aspectRatio: "auto",
            width: "auto",
          }}
          placeholderClassName="bg-gray-100"
        />
      );
    } else if (post?.metadata?.video?.item) {
      return (
        <div className="relative">
          <LazyMedia
            src={sanitizeDStorageUrl(post.metadata.video.item)}
            alt="Video post"
            type="video"
            className={className}
            style={{
              backgroundColor: `rgba(${hexToRgb(props.border)}, 0.3)`,
              borderColor: props.border,
            }}
            controls={true}
            autoPlay={true}
            muted={false}
            poster={sanitizeDStorageUrl(post.metadata.video.cover)}
            placeholderClassName="bg-gray-800"
          />
        </div>
      );
    }

    return null;
  };

  // console.log(props.cardData);

  return (
    <>
      <div>
        <div className="mb-6 flex flex-col relative">
          <div className="min-w-full">
            <div className="w-full ">
              {
                //@ts-ignore
                props.cardData?.metadata?.image?.item ? (
                  <>
                    <div>
                      <LazyMedia
                        src={
                          // @ts-ignore
                          sanitizeDStorageUrl(
                            //@ts-ignore
                            props.cardData?.metadata?.image?.item
                          )
                        }
                        alt={`Image post by ${props.profileName}`}
                        type="image"
                        className="w-full h-full border-2 object-contain aspect-square"
                        style={{
                          borderColor: props.border,
                          backgroundColor: `rgba(${hexToRgb(props.border)}, 0.3)`,
                        }}
                        onLoad={() => setIsImageLoading(false)}
                        onError={() => setIsImageLoading(false)}
                        placeholderClassName="bg-gray-100"
                        enableLightbox={true}
                        onLightboxOpen={handleLightboxOpen}
                      />
                    </div>
                  </>
                ) : (
                  <div className="relative">
                    <LazyMedia
                      type="video"
                      src={
                        // @ts-ignore
                        sanitizeDStorageUrl(
                          //@ts-ignore
                          props.cardData?.metadata?.video?.item
                        )
                      }
                      alt={`Video post by ${props.profileName}`}
                      className="w-full h-full border-2 object-cover aspect-square"
                      style={{
                        borderColor: props.border,
                      }}
                      controls={false}
                      autoPlay={false}
                      muted={true}
                      showPlayIcon={true}
                      onLoad={() => setIsVideoLoading(false)}
                      onError={() => setIsVideoLoading(false)}
                      placeholderClassName="bg-gray-800"
                      enableLightbox={true}
                      onLightboxOpen={handleLightboxOpen}
                    />
                  </div>
                )
              }
            </div>
            {/* Content Below */}
            <div className="row justify-between mt-2">
              <p className="text-[14px] text-titleLabel">{props.location}</p>
              <div className="row gap-3">
                <p
                  className={
                    props?.cardData?.__typename === "Post" && props.cardData?.operations?.hasReacted
                      ? " text-sm -mr-1"
                      : "text-iconColor text-sm -mr-1"
                  }
                  style={
                    props?.cardData?.__typename === "Post" &&
                    // props?.cardData?.stats?.reactions
                    props.cardData?.operations?.hasReacted
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                >
                  {/* {starCount  reactions} */}
                  {props?.cardData?.__typename === "Post" &&
                    (props.cardData?.stats?.reactions ? props.cardData?.stats?.reactions : starCount)}
                </p>

                <Star
                  className={
                    props?.cardData?.__typename === "Post" && props.cardData?.operations?.hasReacted
                      ? "cursor-pointer"
                      : "text-iconColor cursor-pointer"
                  }
                  style={
                    props?.cardData?.__typename === "Post" && props.cardData?.operations?.hasReacted
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                  onClick={() => {
                    if (!address) {
                      useSignInStore.getState().setIsOpen(true);
                    } else {
                      handlikedislike();
                    }
                  }}
                />

                <MessageSquare
                  className="text-iconColor cursor-pointer"
                  style={
                    toggleSave
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                  onClick={handleMessageClick}
                />
                <Bookmark
                  className={
                    props?.cardData?.__typename === "Post" &&
                    props.cardData.operations?.hasBookmarked
                      ? "cursor-pointer" // Add static styles here
                      : "text-iconColor cursor-pointer"
                  }
                  style={
                    props?.cardData?.__typename === "Post" &&
                    props.cardData.operations?.hasBookmarked
                      ? {
                          color: props.border, // Dynamic text color
                          fill: props.border, // Dynamic fill color
                        }
                      : undefined
                  }
                  onClick={handBookMark}
                />
                <DropdownMenu open={showMoreOptions} onOpenChange={setShowMoreOptions}>
                  <DropdownMenuTrigger asChild>
                    <MoreHorizontal
                      className="text-iconColor cursor-pointer max-md:hidden"
                      onClick={() => setShowMoreOptions(true)}
                    />
                  </DropdownMenuTrigger>
                  {true ? (
                    <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          setIsOpenShare(true), setShowMoreOptions(false);
                        }}
                      >
                        Share post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel
                        className="text-center font-normal text-base  pt-3 cursor-pointer"
                        onClick={() => {
                          setShowMoreOptions(false);
                          address
                            ? setIsResonPost(true)
                            : useSignInStore.getState().setIsOpen(true);
                        }}
                      >
                        Report post
                      </DropdownMenuLabel>
                      {/* <DropdownMenuLabel className="text-center font-normal text-base cursor-pointer">
                          Block Profile
                        </DropdownMenuLabel> */}
                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  ) : (
                    <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                      <DropdownMenuLabel className="text-center font-normal text-base border-b-2 py-3 cursor-pointer">
                        Edit post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => setIsOpenShare(true)}
                      >
                        Share post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => setIsDeletePost(true)}
                      >
                        Delete post
                      </DropdownMenuLabel>
                      <DropdownMenuLabel className="text-center font-normal text-base cursor-pointer">
                        Cancel
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  )}
                </DropdownMenu>
                <MoreHorizontal
                  className="text-iconColor cursor-pointer md:hidden"
                  onClick={() => setIsOpenSheet(true)}
                />
              </div>
            </div>
            <div>
              <p className="text-subtitle text-[14px]">
                {getFormattedTime(props?.cardData?.timestamp)}
              </p>
              <p className="text-primary line-clamp-3" onClick={handleMessageClick}>
                {props?.cardData?.__typename === "Post" &&
                //@ts-ignore
                props.cardData?.metadata?.content
                  ? //@ts-ignore
                    props.cardData?.metadata?.content
                  : "about"}
              </p>
              {comments?.postReferences?.items
                ?.slice(0, 1)
                .map((current: PostsQuery["posts"]["items"][0], index) => {
                  return (
                    <div className="flex gap-2" key={index} onClick={handleMessageClick}>
                      <p className="font-bold">{current?.author?.username?.localName} </p>
                      <p className=" line-clamp-2">
                        {
                          // @ts-ignore
                          current?.metadata?.content
                        }
                      </p>
                    </div>
                  );
                })}
              {comments?.postReferences?.items && comments?.postReferences?.items.length > 0 && (
                <p
                  className="text-[14px] text-subtitle cursor-pointer"
                  onClick={handleMessageClick}
                >
                  See all comments ({comments?.postReferences.items.length})
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
      {/* Comment Modal */}
      <div>
        <Modal
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          placement="auto"
          size="5xl"
          isDismissable={false}
          hideCloseButton={false}
          classNames={{
            body: " z-[9999]",
          }}
        >
          <ModalContent className="px-3 py-5 max-md:py-0 max-md:px-0 rounded-3xl h-[80vh] max-md:rounded-none max-md:h-full max-md:max-h-screen mx-0 max-md:overflow-hidden">
            {(onClose) => (
              <>
                <ModalBody className="p-0 max-md:h-full max-md:flex max-md:flex-col">
                  <div className="max-md:min-w-full pb-4 max-md:pb-0 rounded-3xl max-md:rounded-none max-md:h-full max-md:flex max-md:flex-col">
                    <div className="grid grid-cols-2 gap-4 max-md:grid-cols-1 max-md:h-full max-md:flex max-md:flex-col">
                      <div className="flex flex-col justify-between max-md:hidden">
                        {/* Post Media */}
                        <div className="flex flex-fill justify-center items-center min-h-[65vh] max-h-[65vh] max-md:min-h-[40vh] max-md:max-h-[40vh]">
                          {
                            // @ts-ignore
                            props.cardData?.metadata?.video?.item && (
                              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                <Play className="text-white h-12 w-12 opacity-80" />
                              </div>
                            )
                          }
                          {renderMedia(
                            props.cardData,
                            "w-full max-h-[60vh] max-md:min-h-[40vh] max-md:max-h-[40vh]"
                          )}
                        </div>
                        {/* Post Info & Actions */}
                        <div className="row justify-between mt-2 p-3">
                          <p className="text-[14px] text-titleLabel">
                            {getFormattedTime(props?.cardData?.timestamp)}
                          </p>

                          <div className="row gap-3">
                            <p
                              className={
                                props?.cardData?.__typename === "Post" &&
                                props.cardData?.operations?.hasReacted
                                  ? " text-sm -mr-1"
                                  : "text-iconColor text-sm -mr-1"
                              }
                              style={
                                props?.cardData?.__typename === "Post" &&
                                // props?.cardData?.stats?.reactions
                                props.cardData?.operations?.hasReacted
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                            >
                              {/* {starCount  reactions} */}
                              {props?.cardData?.__typename === "Post" &&
                                (props.cardData?.stats?.reactions
                                  ? props.cardData?.stats?.reactions
                                  : starCount)}
                            </p>
                            <Star
                              className={
                                props?.cardData?.__typename === "Post" &&
                                props.cardData?.operations?.hasReacted
                                  ? "cursor-pointer"
                                  : "text-iconColor cursor-pointer"
                              }
                              style={
                                props?.cardData?.__typename === "Post" &&
                                props.cardData?.operations?.hasReacted
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                              onClick={() => {
                                if (!address) {
                                  useSignInStore.getState().setIsOpen(true);
                                } else {
                                  handlikedislike();
                                }
                              }}
                            />
                            <MessageSquare
                              className="text-iconColor cursor-pointer"
                              style={
                                false
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                              // onClick={handleMessageClick}
                            />
                            <Bookmark
                              className={
                                props?.cardData?.__typename === "Post" &&
                                props.cardData.operations?.hasBookmarked
                                  ? "cursor-pointer" // Add static styles here
                                  : "text-iconColor cursor-pointer"
                              }
                              style={
                                props?.cardData?.__typename === "Post" &&
                                props.cardData.operations?.hasBookmarked
                                  ? {
                                      color: props.border, // Dynamic text color
                                      fill: props.border, // Dynamic fill color
                                    }
                                  : undefined
                              }
                              // onClick={handBookMark}

                              onClick={() => {
                                if (!address) {
                                  useSignInStore.getState().setIsOpen(true);
                                } else {
                                  handBookMark();
                                }
                              }}
                            />
                            <DropdownMenu
                              open={showMoreOptionsmodl}
                              onOpenChange={setShowMoreOptionsmodl}
                            >
                              <DropdownMenuTrigger asChild>
                                <MoreHorizontal
                                  className="text-iconColor cursor-pointer max-md:hidden"
                                  onClick={() => setShowMoreOptionsmodl(true)}
                                />
                              </DropdownMenuTrigger>
                              {true ? (
                                <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => {
                                      setIsOpenShare(true), setShowMoreOptionsmodl(false);
                                    }}
                                  >
                                    Share post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base  pt-3 cursor-pointer"
                                    onClick={() => {
                                      setShowMoreOptionsmodl(false);
                                      address
                                        ? setIsResonPost(true)
                                        : useSignInStore.getState().setIsOpen(true);
                                    }}
                                  >
                                    Report post
                                  </DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                </DropdownMenuContent>
                              ) : (
                                <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                                  <DropdownMenuLabel className="text-center font-normal text-base border-b-2 py-3 cursor-pointer">
                                    Edit post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => setIsOpenShare(true)}
                                  >
                                    Share post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel
                                    className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                    onClick={() => setIsDeletePost(true)}
                                  >
                                    Delete post
                                  </DropdownMenuLabel>
                                  <DropdownMenuLabel className="text-center font-normal text-base cursor-pointer">
                                    Cancel
                                  </DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                </DropdownMenuContent>
                              )}
                            </DropdownMenu>
                            <MoreHorizontal
                              className="text-iconColor cursor-pointer md:hidden"
                              onClick={() => setIsOpenSheet(true)}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col justify-between h-full max-md:h-full max-md:flex-1 max-md:min-h-0 relative">
                        {/* Mobile Header - Show profile info at top on mobile */}
                        <div className="md:hidden px-3 py-2 border-b bg-white sticky top-0 z-8">
                          <div className="flex items-center gap-2">
                            <Link
                              //@ts-ignore
                              href={`/profile/lens/${props.localName}`}
                            >
                              <LazyMedia
                                src={props.profileAvatar}
                                alt="Profile Avatar"
                                type="image"
                                className="w-[40px] h-[40px] rounded-full object-cover"
                                placeholderClassName="bg-gray-200 rounded-full"
                              />
                            </Link>
                            <div>
                              <Link
                                //@ts-ignore
                                href={`/profile/lens/${props.localName}`}
                              >
                                <p className="font-bold text-sm">
                                  {
                                    //@ts-ignore
                                    props.profileName || "Profile Name"
                                  }
                                </p>
                              </Link>
                              <p className="text-[#616770] text-xs">{props.localName}</p>
                            </div>
                          </div>
                        </div>

                        {/* Content area with scrollbar */}
                        <div className="px-3 max-md:px-4 overflow-y-auto flex-1 max-md:min-h-0 max-h-[calc(65vh-120px)] max-md:max-h-none">
                          <div className="md:sticky md:top-0 bg-white max-md:hidden">
                            <div className="row gap-2 items-start max-md:items-center">
                              <div>
                                <Link
                                  //@ts-ignore
                                  href={`/profile/lens/${props.localName}`}
                                >
                                  <div>
                                    <LazyMedia
                                      src={props.profileAvatar}
                                      alt="Profile Avatar"
                                      type="image"
                                      className="w-[40px] h-[40px] rounded-full object-cover"
                                      placeholderClassName="bg-gray-200 rounded-full"
                                    />
                                  </div>
                                </Link>
                              </div>
                              <div className="flex flex-row gap-2 max-md:flex-col">
                                <Link
                                  //@ts-ignore
                                  href={`/profile/lens/${props.localName}`}
                                >
                                  <p className="font-bold text-nowrap">
                                    {
                                      //@ts-ignore
                                      props.profileName || "Profile Name"
                                    }
                                  </p>
                                </Link>
                                <p className="text-[#616770] max-md:-mt-1"> {props.localName}</p>
                              </div>
                            </div>
                          </div>

                          <div className="max-md:pb-2">
                            {/* Content with text wrapping */}
                            <div className="text-primary break-words whitespace-pre-wrap mb-2 border-b pb-2 md:pl-[40px] max-md:pl-0">
                              {props?.cardData?.__typename === "Post" && (
                                <ServiceDescription
                                  description={
                                    //@ts-ignore
                                    props.cardData?.metadata?.content
                                  }
                                />
                              )}
                            </div>

                            {/* Comments Section */}
                            <div className="mt-4 mb-4 max-md:mb-4">
                              <h3 className="font-bold text-sm mb-2">Comments</h3>
                              {commentsLoading ? (
                                <div className="text-center py-4">Loading comments...</div>
                              ) : comments?.postReferences.items &&
                                comments?.postReferences.items.length > 0 ? (
                                <div className="space-y-3">
                                  {comments?.postReferences.items.map(
                                    (current: PostsQuery["posts"]["items"][0], index) => (
                                      <div key={index} className="flex gap-2 mb-2 border-b pb-2">
                                        <div className="flex-shrink-0"></div>
                                        <div className="flex-grow">
                                          <Link
                                            href={`/profile/lens/${current?.author.username?.localName}`}
                                          >
                                            <p className="font-semibold text-sm">
                                              {
                                                // @ts-ignore
                                                current?.author?.metadata?.name || "Anonymous"
                                              }{" "}
                                            </p>
                                          </Link>
                                          <div className="break-words whitespace-pre-wrap text-sm">
                                            {
                                              //@ts-ignore
                                              current?.metadata?.content
                                            }
                                          </div>
                                          <p className="text-xs text-subtitle mt-1">
                                            {getFormattedTime(current.timestamp || Date.now())}
                                          </p>
                                        </div>
                                      </div>
                                    )
                                  )}
                                </div>
                              ) : (
                                <div className="text-center py-4 text-subtitle">
                                  No comments yet. Be the first to comment!
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Comment Input - Fixed positioning for mobile */}
                        <div className="grid w-full gap-1.5 max-md:gap-0 px-3 pt-2 bg-background border-t max-md:sticky max-md:bottom-0 max-md:bg-white max-md:z-20 max-md:border-t-2 max-md:shadow-lg">
                          <Label
                            htmlFor="comment"
                            className="text-primary text-sm font-bold max-md:text-start"
                          >
                            Add a comment
                          </Label>
                          <Textarea
                            placeholder="Write a comment..."
                            id="comment"
                            className="text-primary text-base max-md:min-h-[80px] resize-none"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            onFocus={() => {
                              // Ensure modal stays in proper position on iOS
                              if (typeof window !== "undefined" && window.scrollTo) {
                                setTimeout(() => window.scrollTo(0, 0), 100);
                              }
                            }}
                          />
                          <Badge
                            className="btn-xs btn text-center mt-2 rounded-full w-[100px] min-h-[30px] cursor-pointer max-md:mb-2"
                            variant="outline"
                            onClick={() => !isDisable && handleComment()}
                          >
                            {isDisable ? (
                              <span className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></span>
                            ) : (
                              "Post"
                            )}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
      {/* Video LightBox */}
      <Lightbox
        open={openVideo}
        close={() => setOpenVideo(false)}
        plugins={[Video]}
        carousel={{ preload: 0, finite: true }}
        toolbar={{ buttons: ["close"] }}
        controller={{
          closeOnBackdropClick: true,
          touchAction: "none",
        }}
        slides={[
          {
            type: "video",
            width: 1280,
            height: 720,
            controls: true,
            muted: false,
            autoPlay: true,
            sources: [
              {
                src: videoUrl,
                type: "video/mp4",
              },
            ],
          },
        ]}
        styles={{
          container: {
            background: "rgba(0, 0, 0, 0.6)",
          },
        }}
      />

      {/* Image LightBox */}
      <Lightbox
        open={openImage}
        close={() => setOpenImage(false)}
        carousel={{ preload: 0, finite: true }}
        toolbar={{ buttons: ["close"] }}
        controller={{
          closeOnBackdropClick: true,
          touchAction: "none",
        }}
        slides={[
          {
            src: imageUrl2,
            width: 1280,
            height: 720,
          },
        ]}
        plugins={[Zoom]}
        zoom={{
          maxZoomPixelRatio: 3,
          zoomInMultiplier: 2,
          doubleTapDelay: 300,
          doubleClickDelay: 300,
          doubleClickMaxStops: 2,
          keyboardMoveDistance: 50,
          wheelZoomDistanceFactor: 100,
          pinchZoomDistanceFactor: 100,
          scrollToZoom: true,
        }}
        styles={{
          container: {
            background: "rgba(0, 0, 0, 0.6)",
          },
        }}
      />

      <style>
        {`
          .yarl__navigation_prev {
            display: none !important;
          }
          .yarl__video_container video {
            max-width: max-content !important;
          }
          .yarl__navigation_next {
            display: none !important;
          }
        `}
      </style>

      {/* Bottom Icon  */}
      <Sheet open={isOpenSheet} onOpenChange={setIsOpenSheet}>
        <SheetContent side="bottom" className="bg-transparent border-none">
          <div className="bg-white rounded-[14px]">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
              onClick={() => {
                setIsOpenSheet(false), setIsOpenShare(true);
              }}
            >
              Share post
            </button>
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px]"
              onClick={() => {
                address ? setIsResonPost(true) : useSignInStore.getState().setIsOpen(true),
                  setIsOpenSheet(false);
              }}
            >
              Report post
            </button>
          </div>
          <div className="mt-4">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px] "
              onClick={() => setIsOpenSheet(false)}
            >
              Cancel
            </button>
          </div>
        </SheetContent>
      </Sheet>
      {/* Report Post */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isResonPost}
          placement="auto"
          onOpenChange={setIsResonPost}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {renderReportContent() || (
                    <div className=" relative w-full">
                      <div className="row justify-between mb-5">
                        <X
                          onClick={() => setIsResonPost(false)}
                          className="text-primary cursor-pointer"
                        />
                        <p className="font-bold  text-titleLabel ">Report Post</p>
                        <p className=" opacity-0">Test</p>
                      </div>

                      <div className="min-w-full pb-5 ">
                        <div className="grid w-full gap-1.5 px-3 ">
                          <div className="grid w-full md:max-w-sm items-center gap-1 mt-4 max-md:text-start">
                            <Label
                              htmlFor="message"
                              className="text-primary text-base font-bold max-md:text-start"
                            >
                              Reason
                            </Label>
                            {resonData.map((item, indexs) => (
                              <div className="grid grid-cols-1 mt-1" key={indexs}>
                                <div className="row gap-3">
                                  {isCheck[indexs] ? (
                                    <CheckCircle
                                      className="text-primary w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                      size={18}
                                      onClick={() => handleIsCheck(indexs)}
                                    />
                                  ) : (
                                    <Circle
                                      className="text-subtitle w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                      // size={18}
                                      onClick={() => handleIsCheck(indexs)}
                                    />
                                  )}
                                  <p
                                    className={
                                      isCheck[indexs]
                                        ? "text-primary cursor-pointer"
                                        : "text-subtitle cursor-pointer"
                                    }
                                    onClick={() => handleIsCheck(indexs)}
                                  >
                                    {item}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                          <div className="grid items-center gap-1.5 mt-4 max-md:text-start ">
                            <Label htmlFor="message" className="text-primary text-base font-bold">
                              Сomment
                            </Label>

                            <Textarea
                              placeholder="Сomment..."
                              id="message"
                              className="text-primary text-base w-full"
                              value={reportComment}
                              onChange={(e) => setReportComment(e.target.value)}
                              disabled={reportStatus === REPORT_STATUS.UPLOADING}
                            />
                          </div>
                          <Badge
                            className={`btn-xs btn text-center mt-5 rounded-full w-[150px] min-h-[30px] ${
                              reportStatus === REPORT_STATUS.UPLOADING
                                ? "opacity-50 cursor-not-allowed"
                                : "cursor-pointer"
                            }`}
                            variant="outline"
                            onClick={async () => {
                              if (reportStatus === REPORT_STATUS.UPLOADING) return;

                              if (!reportRespon || !reportComment) {
                                alert("Please select a reason and provide a comment.");
                                return;
                              }

                              setReportStatus(REPORT_STATUS.UPLOADING);

                              try {
                                if (!address) {
                                  useSignInStore.getState().setIsOpen(true);
                                  setReportStatus(REPORT_STATUS.IDLE);
                                  return;
                                }
                                const reasonKey = getReasonKey(reportRespon);

                                if (!reasonKey) {
                                  throw new Error("Invalid reporting reason");
                                }

                                const resp = await reportPost({
                                  request: {
                                    post: props.cardData.id,
                                    additionalComment: reportComment,
                                    reason: reportRespon as PostReportReason,
                                  },
                                });

                                if (resp) {
                                  setReportStatus(REPORT_STATUS.SUCCESS);
                                } else {
                                  setReportStatus(REPORT_STATUS.IDLE);
                                  alert("Failed to submit report. Please try again.");
                                }
                              } catch (error) {
                                console.error("Error submitting report:", error);
                                setReportStatus(REPORT_STATUS.IDLE);
                                alert("An error occurred while submitting the report.");
                              }
                            }}
                          >
                            {reportStatus === REPORT_STATUS.UPLOADING ? "Submitting..." : "Confirm"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
      {/* Signout Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isDeletePost}
          placement="auto"
          onOpenChange={setIsDeletePost}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    Are you sure you would like to delete the post?
                  </p>
                  <div>
                    <Button
                      variant="outline"
                      className=" rounded-full w-full mt-5 border-black text-black border-2 py-5  text-base"
                      // onClick={() => {
                      //   logOut(), setIsOpen(false);
                      // }}
                    >
                      Yes, delete
                    </Button>
                    <Button
                      variant="outline"
                      className=" rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base "
                      onClick={() => setIsDeletePost(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* SignIn Modal */}
      <div className="max-md:h-full px-5">
        <Modal
          isDismissable={false}
          isOpen={isSigninOpen}
          placement="auto"
          onOpenChange={setIsSigninOpen}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div
                    className="absolute top-6 left-6 cursor-pointer"
                    onClick={() => {
                      sessionStorage.removeItem("input");
                      sessionStorage.removeItem("openPost");
                      setIsSigninOpen(false);
                    }}
                  >
                    <X />
                  </div>
                  <SignInButton />
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Share Post Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenShare}
          placement="auto"
          onOpenChange={setIsOpenShare}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {() => (
              <>
                <ModalBody>
                  <div>
                    <div>
                      <div className="flex items-center justify-between">
                        <div
                          onClick={() => setIsOpenShare(false)}
                          className="p-1.5 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                        >
                          <X size={20} />
                        </div>
                        <h3 className="font-bold text-lg text-primary">Share Post</h3>
                        <div className="w-8"></div>
                      </div>

                      <div className="mt-6">
                        <div className="flex items-center justify-between border border-gray-200 rounded-lg p-3 bg-gray-50">
                          <div className="truncate mr-2 text-gray-700">
                            https://www.amuzn.app/browse/
                            {props.category === "Storytelling" ? "Literature" : props.category}/
                            {props.cardData.id}%20{props.userID}
                          </div>
                          <div
                            className={`cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-all ${
                              isCopied ? "bg-green-100 text-green-600" : ""
                            }`}
                            onClick={() => copyProfileLink(props.cardData.id)}
                          >
                            {isCopied ? <Check size={18} /> : <Copy size={18} />}
                          </div>
                        </div>

                        <p className="text-gray-500 text-sm mt-4 mb-2">Share on social media</p>

                        <div className="grid grid-cols-4 gap-3 mt-2">
                          <a
                            href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                              `https://www.amuzn.app/browse/${
                                props.category === "Storytelling" ? "Literature" : props.category
                              }/${props.cardData.id}%20${props.userID}`
                            )}&text=${encodeURIComponent("Check out this post on Amuzn!")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Twitter size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Twitter</span>
                          </a>

                          <a
                            href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                              `https://www.amuzn.app/browse/${
                                props.category === "Storytelling" ? "Literature" : props.category
                              }/${props.cardData.id}%20${props.userID}`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Facebook size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Facebook</span>
                          </a>

                          <div
                            onClick={() => {
                              // Instagram doesn't have a direct share URL, so we'll copy the link to clipboard
                              const postUrl = `https://www.amuzn.app/browse/${
                                props.category === "Storytelling" ? "Literature" : props.category
                              }/${props.cardData.id}%20${props.userID}`;
                              navigator.clipboard.writeText(postUrl);
                              setIsCopied(true);
                              setTimeout(() => setIsCopied(false), 2000);
                              // Open Instagram in a new tab
                              window.open("https://www.instagram.com/", "_blank");
                            }}
                            className="flex flex-col items-center justify-center cursor-pointer"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Instagram size={24} className="border-0 outline-0" />
                            </div>
                            <span className="text-xs">Instagram</span>
                          </div>

                          <a
                            href={`https://wa.me/?text=${encodeURIComponent(
                              `Check out this post on Amuzn! https://www.amuzn.app/browse/${
                                props.category === "Storytelling" ? "Literature" : props.category
                              }/${props.cardData.id}%20${props.userID}`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className="fill-primary">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.966-.94 1.164-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.007-.372-.009-.571-.009-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.099 3.2 5.077 4.363.71.306 1.263.489 1.694.625.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/>
                                <path d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.341 4.997l-1.409 5.151 5.283-1.389c1.475.807 3.153 1.238 4.782 1.238h.001c5.514 0 9.997-4.483 9.997-9.997 0-2.669-1.04-5.178-2.929-7.067-1.889-1.889-4.398-2.93-7.069-2.93zm0 17.995c-1.462 0-2.892-.393-4.13-1.137l-.295-.174-3.134.823.837-3.057-.192-.314c-.822-1.346-1.257-2.899-1.257-4.486 0-4.411 3.588-7.999 7.999-7.999 2.137 0 4.146.833 5.656 2.344 1.511 1.511 2.344 3.52 2.344 5.656 0 4.411-3.588 7.999-7.999 7.999z"/>
                              </svg>
                            </div>
                            <span className="text-xs">WhatsApp</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default BrowseLensUserCard;
