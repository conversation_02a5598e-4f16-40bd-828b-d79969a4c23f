"use client";
import MobilePostsHome from "./posts/MobilePostsHome";
import { useEffect, useState } from "react";

export function MobileHome() {
  // Calculate the available height for the content
  const [containerHeight, setContainerHeight] = useState("calc(100vh - 140px)");

  useEffect(() => {
    const updateHeight = () => {
      // Adjust this value based on your navbar height
      setContainerHeight("calc(100vh - 140px)");
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);

    return () => {
      window.removeEventListener("resize", updateHeight);
    };
  }, []);

  return (
    <div
      className="md:hidden"
      style={{
        height: containerHeight,
        position: "fixed",
        top: "126px", // Adjust this to match your navbar height
        left: 0,
        right: 0,
        bottom: 0,
        overflow: "hidden",
      }}
    >
      <MobilePostsHome />
    </div>
  );
}

export default MobileHome;
