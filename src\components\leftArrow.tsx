import { useState, useEffect } from "react";

interface ScrollButtonProps {
  scrollRef: React.RefObject<HTMLDivElement>;
}

const ScrollButtonLeft = ({ scrollRef }: ScrollButtonProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkScrollability = () => {
      const container = scrollRef.current;
      if (container) {
        setIsVisible(container.scrollLeft > 0); // Show if not at the start
      }
    };

    const container = scrollRef.current;
    if (container) {
      container.addEventListener("scroll", checkScrollability);
    }

    window.addEventListener("resize", checkScrollability);
    checkScrollability(); // Initial check

    return () => {
      if (container) {
        container.removeEventListener("scroll", checkScrollability);
      }
      window.removeEventListener("resize", checkScrollability);
    };
  }, [scrollRef]);

  const handleScroll = () => {
    const container = scrollRef.current;
    if (container) {
      container.scrollBy({ left: -350, behavior: "smooth" });
    }
  };

  if (!isVisible) return null;

  if (isVisible) {
    console.log("visible");
  } else console.log(" not visible");

  return (
    <div
      onClick={handleScroll}
      className="cursor-pointer fixed bottom-0 left-0 top-48 w-[100px] bg-gradient-to-r from-white flex flex-row justify-center items-center z-[49]"
    >
      <button
        className="fixed left-0 top-2/3 transform translate-x-1/2 rounded z-[50]"
        style={{ height: "80px" }}
      >
        <img src="/assets/ChevronsDown.svg" className="h-[50px] w-[50px] rotate-90" />
      </button>
    </div>
  );
};

export default ScrollButtonLeft;
