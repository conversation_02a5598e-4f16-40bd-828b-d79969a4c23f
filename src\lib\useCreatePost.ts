import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import { useCreatePostMutation } from "@/graphql/test/generated";
import { v4 } from "uuid";
import { immutable, StorageClient } from "@lens-chain/storage-client";
import { useAccount } from "wagmi";

export function useCreatePost() {
  const { mutateAsync: requestTypedData } = useCreatePostMutation();

  const { address,isConnected } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();
  const storageClient = StorageClient.create();
  const acl = immutable(232);

  async function post(post: File, description: string) {
    try {
      if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      const response = await storageClient.uploadFile(post, { acl });
      const payload = {
        $schema: "https://json-schemas.lens.dev/posts/image/3.0.0.json",
        name: "Post by",
        description: description,
        external_url: "https://mytext.com",
        image: "https://text.com/image.png",
        lens: {
          id: v4(),
          locale: "en",
          mainContentFocus: post.type.startsWith("image") ? "IMAGE" : "VIDEO",
          title: `Post by @${
            JSON.parse(localStorage?.getItem("lens-user") ?? "")?.metadata?.name
          }`,
          image: {
            item: response.uri,
            type: post.type,
          },
          content: description,
        },
      };
      const { uri } = await storageClient.uploadAsJson(payload, {
        acl: immutable(232),
      });

      const typedData = await requestTypedData({
        request: {
          contentUri: uri,
        },
      });

      return typedData.post;
    } catch (error) {
      throw error;
    }
  }
  return post;
}
