const data = [
  {
    orderinfo: "Order info",
    date: "11.07.2020",
    time: "90:00",
    titel: "Payment received",
    details: "<PERSON> received 80% of order service cost.",
  },

  {
    orderinfo: "Order info",
    date: "11.07.2020",
    time: "90:00",
    titel: "Payment received",
    details: "<PERSON> received 80% of order service cost.",
  },
];
const ActiveLog = () => {
  return (
    <>
      <div className="overflow-y-scroll hide-scroll h-screen pb-72">
        {data.map((item, index) => {
          return (
            <div className="mb-6">
              <div className="row justify-between mb-1">
                <p className="text-primary">{item.orderinfo}</p>
                <div className="row gap-2">
                  <p>{item.date}</p>
                  <p>{item.time}</p>
                </div>
              </div>
              <p className="text-titleLabel font-bold uppercase mb-3">
                {item.titel}
              </p>

              <p className="text-subtitle">{item.details}</p>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default ActiveLog;
