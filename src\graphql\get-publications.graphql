#import "./publication-fragments.graphql"

query Publications($request: PublicationsRequest!) {
  publications(request: $request) {
    items {
      __typename
      ... on Post {
        ...PostFields
      }

      ... on Comment {
        ...CommentFields
      }

      ... on Mirror {
        ...MirrorFields
      }

      ... on Quote {
        ...QuoteFields
      }
    }
    pageInfo {
      prev
      next
    }
  }
}