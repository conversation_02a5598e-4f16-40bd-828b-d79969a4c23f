"use client";
import { getAllPostsTest } from "@/services/postService";
import { useCallback, useEffect, useRef, useState } from "react";
import { themes } from "../../../../theme";
import { Play } from "react-feather";
import LensData from "@/hook/lensData";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getUserById, getUserByPostId } from "@/services/usersServices";
import { PostsQuery } from "@/graphql/test/generated";
import { useRouter } from "next/navigation";
import Link from "next/link";
import PostSkeletonLoader from "./PostSkeletonLoader";
import LazyMedia from "@/components/LazyMedia";

const Web3PostMobileView = (props: any) => {
  const user = useAuth();
  const { profileData: profileDataById } = useProfile(user?.userId || "");

  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const isFetched = useRef<boolean>(false);
  const routeCategory = props.themeProperties.title;
  const category = props.themeProperties.title.toLowerCase();

  const { publicationsData }: { publicationsData: PostsQuery | undefined } = LensData(category);

  // console.log({ publicationsData });

  // Generate public URL for Firebase Storage

  const fetchAllPosts = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getAllPostsTest();

      if (response?.posts) {
        const categorizedData: Record<string, any[]> = {};
        const myFeed: any[] = [];
        const myFeedSet = new Set();

        // To remove duplicate posts globally
        const uniquePostsMap = new Map<string, any>();

        // Fetch user followers only once if logged in
        let userFollowings: string[] = [];
        if (user?.isLogin) {
          const userResponse = await getUserById(user?.userId);
          if (userResponse.success) {
            userFollowings = userResponse.user?.following || [];
          }
        }

        // Process posts asynchronously
        await Promise.all(
          response.posts.map(async (post: any) => {
            if (uniquePostsMap.has(post.id)) return; // Skip duplicate posts

            const [postCategory, userId] = await Promise.all([post?.category, post?.user_id]);

            const normalizedCategory =
              post.category === "Storytelling" ? "Literature" : post.category;

            // Store unique posts
            uniquePostsMap.set(post.id, {
              ...post,
              category: normalizedCategory,
            });

            if (!categorizedData[normalizedCategory]) {
              categorizedData[normalizedCategory] = [];
            }
            categorizedData[normalizedCategory].push(post);
            // Add to "My Feed" if the user follows the post's author
            if (
              user?.isLogin &&
              userId &&
              userFollowings.includes(userId) &&
              !myFeedSet.has(post.id)
            ) {
              myFeedSet.add(post.id);
              myFeed.push({ ...post, category: normalizedCategory });
            }
          })
        );

        // Sort posts in each category by `created` field
        for (const category in categorizedData) {
          categorizedData[category].sort(
            (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime()
          );
        }

        // Add "My Feed" if user is logged in
        if (user?.isLogin) {
          categorizedData["My Feed"] = myFeed;
        }

        // console.log({ categorizedData });

        setCategoryData(categorizedData);

        // setPostsLoaded(true);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [profileDataById]); // Keep dependency consistent with events function

  useEffect(() => {
    fetchAllPosts();
  }, [fetchAllPosts]); // Ensures function runs only when dependencies change

  const [videoUrl, setVideoUrl] = useState("");
  const [imageUrl2, setImageUrl2] = useState("");
  const [openImage, setOpenImage] = useState(false);
  const [openVideo, setOpenVideo] = useState(false);

  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [isImageLoading, setIsImageLoading] = useState(true);

  const handleLightboxOpen = (src: string, type: "image" | "video") => {
    if (type === "image") {
      setImageUrl2(src);
      setOpenImage(true);
    } else if (type === "video") {
      setVideoUrl(src);
      setOpenVideo(true);
    }
  };

  return (
    <>
      {loading ? (
        <PostSkeletonLoader
          isScroll={props.isScroll}
          borderColor={props.borderColor}
          count={5} // Always show 5 items per row
        />
      ) : (
        <div className="w-full">
          {Object.entries(themes).map(([themeName, themeProperties]) => (
            <div key={themeName} className="">
              {props.themeProperties.title === themeProperties.title && (
                <div>
                  {categoryData[themeProperties.title]?.length > 0 ? (
                    <div>
                      {props.isScroll && (
                        <div>
                          <div
                            className={`mt-0 flex flex-row gird grid-rows-2 gap-x-[2px] ${!props.isScroll ? "overflow-x-hidden" : ""}`}
                          >
                            {categoryData[themeProperties.title]
                              .slice(5, categoryData[themeProperties.title].length) // Display only the first 5 items
                              .map((post, index) => (
                                <div key={index} className="cursor-pointer">
                                  <div className="mt-[2px]">
                                    <Link
                                      href={`/browse/${routeCategory}/${publicationsData?.posts?.items[index]?.id}%20${publicationsData?.posts?.items[index]?.author?.address}`}
                                      className="block"
                                    >
                                      {
                                        // only render images and videos
                                        // (
                                        // @ts-ignore
                                        publicationsData?.posts.items[index]?.metadata?.image // @ts-ignore
                                          ?.item ? (
                                          // <img
                                          //   src={
                                          //     // @ts-ignore
                                          //     sanitizeDStorageUrl(
                                          //       // @ts-ignore
                                          //       publicationsData?.posts.items[
                                          //         index // @ts-ignore
                                          //       ]?.metadata?.image?.item || "" // @ts-ignore
                                          //     )
                                          //   }
                                          //   alt={""}
                                          //   className="col-span-4 h-full w-full object-cover border-2 min-h-[85px] min-w-[85px]  max-h-[85px] max-w-[85px]"
                                          //   style={{
                                          //     borderColor: props.borderColor,
                                          //   }}
                                          // />

                                          <LazyMedia
                                            src={
                                              // @ts-ignore
                                              sanitizeDStorageUrl(
                                                // @ts-ignore
                                                publicationsData?.posts.items[
                                                  index // @ts-ignore
                                                ]?.metadata?.image?.item || "" // @ts-ignore
                                              )
                                            }
                                            // alt={post.metadata.image.item || "Post image"}
                                            type="image"
                                            alt={"post image"}
                                            className="col-span-4 h-full w-full object-cover border-2 min-h-[85px] min-w-[85px]  max-h-[85px] max-w-[85px]"
                                            style={{
                                              borderColor: props.borderColor,
                                            }}
                                            placeholderClassName="bg-gray-100"
                                          />
                                        ) : (
                                          // <video
                                          //   key={
                                          //     // @ts-ignore
                                          //     sanitizeDStorageUrl(
                                          //       publicationsData?.posts.items[
                                          //         index // @ts-ignore
                                          //       ]?.metadata?.video?.item // @ts-ignore
                                          //     )
                                          //   }
                                          //   controls={false}
                                          //   autoPlay={false}
                                          //   muted
                                          //   className="col-span-4 h-full w-full object-cover border-2 min-h-[85px] min-w-[85px]  max-h-[85px] max-w-[85px]"
                                          //   style={{
                                          //     borderColor: props.borderColor,
                                          //   }}
                                          // >
                                          //   <source
                                          //     src={
                                          //       // @ts-ignore
                                          //       sanitizeDStorageUrl(
                                          //         publicationsData?.posts.items[
                                          //           index
                                          //           // @ts-ignore
                                          //         ]?.metadata?.video?.item
                                          //       )
                                          //     }
                                          //     type="video/mp4"
                                          //   />
                                          //   Your browser does not support the video tag.
                                          // </video>

                                          <div className="relative">
                                            <LazyMedia
                                              src={
                                                // @ts-ignore
                                                sanitizeDStorageUrl(
                                                  publicationsData?.posts.items[
                                                    index
                                                    // @ts-ignore
                                                  ]?.metadata?.video?.item
                                                )
                                              }
                                              alt="Video post"
                                              type="video"
                                              className="col-span-4 h-full w-full object-cover border-2 min-h-[85px] min-w-[85px]  max-h-[85px] max-w-[85px]"
                                              style={{
                                                borderColor: props.borderColor,
                                              }}
                                              controls={false}
                                              autoPlay={false}
                                              muted={true}
                                              showPlayIcon={true}
                                              onLoad={() => setIsVideoLoading(false)}
                                              onError={() => setIsVideoLoading(false)}
                                              placeholderClassName="bg-gray-800"
                                              // enableLightbox={true}
                                              // onLightboxOpen={handleLightboxOpen}
                                            />
                                          </div>
                                        )
                                      }
                                    </Link>
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-4">
                      <div className="w-16 h-16 mb-2 rounded-full bg-gray-100 flex items-center justify-center">
                        <svg
                          className="w-8 h-8 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                          ></path>
                        </svg>
                      </div>
                      <p className="text-gray-500 text-center">
                        No posts available in this category.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default Web3PostMobileView;
