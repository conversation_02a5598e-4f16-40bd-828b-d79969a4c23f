import React from "react";
import Link from "next/link";
import { ShoppingCart } from "react-feather";

const EmptyBasket = () => {
  return (
    <div className="flex flex-col items-center justify-center h-[70vh] px-4">
      <div className="bg-gray-100 p-6 rounded-full mb-6">
        <ShoppingCart size={32} className="text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-gray-800 mb-2">Your basket is empty</h3>
      <p className="text-gray-500 text-center mb-6 max-w-[280px]">
        Looks like you haven't added any services to your basket yet.
      </p>
      <Link
        href="/profile"
        className="bg-primary text-white px-6 py-3 rounded-full font-medium hover:bg-primary/90 transition-colors"
      >
        Explore Services
      </Link>
    </div>
  );
};

export default EmptyBasket; 