/**
 * Utility functions for sanitizing and enhancing HTML content from Firebase Remote Config
 */

import DOMPurify from "dompurify";

/**
 * Sanitizes HTML content and enhances it with proper link attributes
 *
 * @param htmlContent - Raw HTML content from Firebase Remote Config
 * @returns Sanitized and enhanced HTML content
 */
export const sanitizeAndEnhanceHtml = (htmlContent: string): string => {
  if (!htmlContent) return "";

  // Configure DOMPurify to allow certain tags and attributes
  const purifyConfig = {
    ALLOWED_TAGS: [
      "a",
      "b",
      "br",
      "div",
      "em",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "i",
      "img",
      "li",
      "ol",
      "p",
      "span",
      "strong",
      "table",
      "tbody",
      "td",
      "th",
      "thead",
      "tr",
      "u",
      "ul",
      "blockquote",
      "hr",
      "pre",
      "code",
    ],
    ALLOWED_ATTR: [
      "href",
      "target",
      "rel",
      "src",
      "alt",
      "class",
      "id",
      "style",
      "width",
      "height",
      "title",
    ],
    ADD_ATTR: ["target", "rel"],
  };

  // Sanitize the HTML content
  const sanitizedHtml = DOMPurify.sanitize(htmlContent, purifyConfig);

  // Check if we're in a browser environment before using DOMParser
  if (typeof window === "undefined") {
    // Server-side rendering - return sanitized HTML without DOM manipulation
    return sanitizedHtml;
  }

  // Client-side rendering - use DOMParser for additional processing
  // Create a DOM parser to manipulate the HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(sanitizedHtml, "text/html");

  // Process all links to add target="_blank" and rel="noopener noreferrer"
  const links = doc.querySelectorAll("a");
  links.forEach((link) => {
    link.setAttribute("target", "_blank");
    link.setAttribute("rel", "noopener noreferrer");

    // Add a class for styling
    link.classList.add("remote-content-link");
  });

  // Process all images to add responsive classes
  const images = doc.querySelectorAll("img");
  images.forEach((img) => {
    img.classList.add("remote-content-img");

    // Ensure alt text
    if (!img.hasAttribute("alt")) {
      img.setAttribute("alt", "Image");
    }
  });

  // Get the processed HTML
  return doc.body.innerHTML;
};

export default sanitizeAndEnhanceHtml;
