// src/services/currencyService.ts

/**
 * Map of currency codes to their respective symbols
 */
export const currencySymbols: Record<string, string> = {
  USD: "$", // US Dollar
  EUR: "€", // Euro
  GBP: "£", // British Pound
  JPY: "¥", // Japanese Yen
  INR: "₹", // Indian Rupee
  AUD: "A$", // Australian Dollar
  CAD: "C$", // Canadian Dollar
  CHF: "CHF", // Swiss Franc
  CNY: "¥", // Chinese Yuan
  HKD: "HK$", // Hong Kong Dollar
  NZD: "NZ$", // New Zealand Dollar
  SEK: "kr", // Swedish Krona
  KRW: "₩", // South Korean Won
  SGD: "S$", // Singapore Dollar
  NOK: "kr", // Norwegian Krone
  MXN: "Mex$", // Mexican Peso
  BRL: "R$", // Brazilian Real
  RUB: "₽", // Russian Ruble
};

/**
 * Default currency used throughout the app
 */
export const DEFAULT_CURRENCY = "GBP";

/**
 * Initialize or get the current currency from localStorage
 */
export const initializeCurrency = (): string => {
  try {
    // Get currency from localStorage
    const storedCurrency = localStorage.getItem("currency");

    // If valid JSON and not null
    if (storedCurrency) {
      // Handle both JSON string and raw string formats
      let parsedCurrency;

      try {
        // Try parsing as JSON
        parsedCurrency = JSON.parse(storedCurrency);
      } catch (e) {
        // If not valid JSON, use as is (raw string)
        parsedCurrency = storedCurrency;
      }

      // If parsed value is valid, return it
      if (typeof parsedCurrency === "string" && parsedCurrency.trim()) {
        return parsedCurrency.toUpperCase();
      }
    }

    // If no valid currency found, set default and return it
    localStorage.setItem("currency", DEFAULT_CURRENCY);
    return DEFAULT_CURRENCY;
  } catch (error) {
    console.error("Error initializing currency:", error);
    return DEFAULT_CURRENCY;
  }
};

/**
 * Get the symbol for a given currency code
 *
 * @param currencyCode - The currency code (e.g., 'USD', 'EUR')
 * @returns The currency symbol
 */
export const getCurrencySymbol = (currencyCode?: string): string => {
  if (!currencyCode) {
    const currentCurrency = initializeCurrency();
    // console.log(currentCurrency);

    return currencySymbols[currentCurrency] || "£";
  }

  // Handle both string and JSON formats
  let code;
  try {
    code =
      typeof currencyCode === "string"
        ? currencyCode.startsWith('"')
          ? JSON.parse(currencyCode)
          : currencyCode
        : String(currencyCode);
  } catch (e) {
    code = currencyCode;
  }

  code = code.toUpperCase();
  return currencySymbols[code] || currencySymbols[DEFAULT_CURRENCY] || "£";
};

/**
 * Update currency in localStorage and update all relevant UI elements
 *
 * @param currencyCode - The currency code to set
 * @returns Boolean indicating success
 */
export const updateCurrency = (currencyCode: string): boolean => {
  try {
    const validCode = currencyCode.trim().toUpperCase();

    // Only update if it's a valid currency code
    if (validCode && currencySymbols[validCode]) {
      localStorage.setItem("currency", validCode);
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error updating currency:", error);
    return false;
  }
};

/**
 * Format a number as a currency string
 *
 * @param amount - The amount to format
 * @param currencyCode - The currency code (optional, will use stored value if not provided)
 * @param locale - The locale to use for formatting (default: 'en-US')
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number | string,
  currencyCode?: string,
  locale: string = "en-US"
): string => {
  const numericAmount = typeof amount === "string" ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) {
    return `${getCurrencySymbol(currencyCode)}0.00`;
  }

  const code = currencyCode || initializeCurrency();

  try {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: code.toUpperCase(),
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numericAmount);
  } catch (error) {
    // Fallback if the Intl API fails
    return `${getCurrencySymbol(code)}${numericAmount.toFixed(2)}`;
  }
};

/**
 * Format a price with the list price calculation (adding 16% service fee)
 *
 * @param price - The base price
 * @param currencyCode - Optional currency code
 * @returns Formatted price with symbol
 */
export const formatListPrice = (price: string | number, currencyCode?: string): string => {
  if (!price || isNaN(Number(price))) {
    return `${getCurrencySymbol(currencyCode)}0.00`;
  }

  const numericPrice = typeof price === "string" ? parseFloat(price) : price;
  const listPrice = numericPrice / (1 - 0.16);
  return `${getCurrencySymbol(currencyCode)}${listPrice.toFixed(2)}`;
};

/**
 * Create hook-compatible currency context for React components
 *
 * @returns Current currency symbol
 */
export const useCurrencySymbol = (): string => {
  return getCurrencySymbol();
};
