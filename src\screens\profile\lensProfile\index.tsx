"use client";
import { useEffect, useState } from "react";
import ProfileInfo from "./profileInfo";
import TabsMyProfile from "./tabs";
import { themes } from "../../../../theme";
import { useAccountQuery, useFullAccountQuery } from "@/graphql/test/generated";
import useAuth from "@/hook";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import CreatePost from "./tabs/posts/createPost";
import { useAccount } from "wagmi";
import { Plus } from "react-feather";
const MyLensProfile = (props: any) => {
  const [isOtherProfile, setIsOtherProfile] = useState(false);
  const [selectedTabs, setSelectedTabs] = useState("Posts"); // NEW: Loading state
  const [selectedTabsStatus, setSelectedTabsStatus] = useState(false); // NEW: Loading state
  const [isOpen, setIsOpen] = useState(false);
  const [isRefatch, setIsRefatch] = useState(false);
  const [selectedProfileTabs, setSelectedProfileTabs] = useState("Following"); // NEW: Loading state

  let bgColor: any;
  const auth = useAuth();

  {
    Object.entries(themes)
      .filter(([_, themeProperties]) => themeProperties.title === props.Category)
      .map(
        ([themeName, themeProperties]) => (bgColor = themeProperties.backgroundColor)
        // console.log(themeName, themeProperties)
      );
  }

  const userId = "my-profile";
  // console.log(props.userId);

  useEffect(() => {
    const fetchData = async () => {
      // const resp = await getId({ id: props.userId });
      // JSON.parse(localStorage.getItem("lens-user") || "{}")?.owner === address
console.log(JSON.parse(localStorage.getItem("lens-user") || "{}")?.username.localName);
console.log(props.userId);


      if (JSON.parse(localStorage.getItem("lens-user") || "{}")?.address === props.userId) {
        setIsOtherProfile(true);
      }
    };
    fetchData();
  });
  // console.log(props.otherUserStatus);

  //******************************  Lens Data fetch by id  ******************************

  const {
    data: profileQueryData,
    isLoading: isLoadingProfile,
    error: profileQueryError,
    refetch,
  } = useFullAccountQuery(
    {
      accountRequest: {
        address: props.userId,
      },
      accountStatsRequest: {
        account: props?.userId,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );
  profileQueryData?.account?.metadata?.bio;
  profileQueryData?.accountStats?.graphFollowStats?.following;
  return (
    <>
      <div className="relative h-full">
        <div className="grid grid-cols-2 md:gap-3 max-md:grid-cols-1 max-md:gap-y-3 mt-1 max-md:bg-white">
          <div className="md:overflow-y-scroll md:overflow-x-hidden hide-scroll-custom md:h-screen pb-48 max-md:pb-0 mb-8 max-md:mb-0 shadow-lg max-md:shadow-none bg-white pt-2">
            <ProfileInfo
              isOtherProfileStatus={props.otherUserStatus}
              isOtherProfile={isOtherProfile}
              Category={props.Category}
              otherUserID={props.userId}
              profileQueryData={profileQueryData}
              setSelectedTabs={setSelectedTabs}
              setSelectedTabsStatus={setSelectedTabsStatus}
              refetch={refetch}
              setSelectedProfileTabs={setSelectedProfileTabs}
            />
          </div>
          <div className="lg:h-screen shadow-lg max-lg:shadow-none bg-white  pt-2">
            <TabsMyProfile
              activeBgColor={bgColor}
              isOtherProfile={isOtherProfile}
              isOtherProfileStatus={props.otherUserStatus}
              otherUserID={props.userId}
              profileQueryData={profileQueryData}
              selectedTabs={selectedTabs}
              setSelectedTabs={setSelectedTabs}
              selectedTabsStatus={selectedTabsStatus}
              setSelectedProfileTabs={setSelectedProfileTabs}
              selectedProfileTabs={selectedProfileTabs}
            />
          </div>
        </div>
        <div>
          {selectedTabs == "Posts" && isOtherProfile && (
            <div
              className="fixed bottom-6 right-10 max-md:right-4 max-md:bottom-2 z-50 p-3 rounded-full text-white font-bold cursor-pointer bg-color-indicator-plus shadow-lg"
              onClick={() => setIsOpen(true)}
              style={
                {
                  "--active-bg-color-indicator-plus": bgColor,
                } as React.CSSProperties
              } // Set CSS variable
            >
              <Plus className="w-10 h-10" style={{ strokeWidth: "2.5px" }} />
            </div>
          )}
        </div>
        <div>
          <Modal
            isDismissable={false}
            isOpen={isOpen}
            placement="auto"
            onOpenChange={setIsOpen}
            hideCloseButton={true}
          >
            <ModalContent className="modal-content">
              {(onClose) => (
                <>
                  <ModalBody>
                    <div className="max-h-[80vh] max-md:max-h-[90vh] overflow-y-auto hide-scroll-custom">
                      {selectedTabs == "Posts" && (
                        <CreatePost
                          category={props.Category}
                          setIsOpen={setIsOpen}
                          setIsRefatch={setIsRefatch}
                          // setToggleMain={setToggleMain}
                        />
                      )}
                      {selectedTabs == "Services" && (
                        // <CreateService
                        //   category={profile?.profileData?.categories}
                        //   setIsOpen={setIsOpen}
                        //   setToggleMain={setToggleMain}
                        // />
                        <p>create post</p>
                      )}
                      {selectedTabs == "Events" && (
                        // <CreateEvent
                        //   category={profile?.profileData?.categories}
                        //   setIsOpen={setIsOpen}
                        //   setToggleMain={setToggleMain}
                        // />
                        <p>create post</p>
                      )}
                    </div>
                  </ModalBody>
                </>
              )}
            </ModalContent>
          </Modal>
        </div>
      </div>
    </>
  );
};

export default MyLensProfile;
