import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  ApolloLink,
} from "@apollo/client";

const httpLink = createHttpLink({
  uri: LENS_API_URL,
});

const authLink = new ApolloLink((operation, forward) => {
  const token = localStorage.getItem("lens-auth-token");

  // Debug token
  // console.log('Token being used for request:', token ? token.substring(0, 20) + '...' : 'no token');

  // Important: Include the 'Bearer' prefix
  operation.setContext(({ headers = {} }) => ({
    headers: {
      ...headers,
      "x-access-token": token ? `Bearer ${token}` : "",
      Authorization: token ? `Bearer ${token}` : "",
    },
  }));

  return forward(operation);
});

// Debug link to log requests
const debugLink = new ApolloLink((operation, forward) => {
  // console.log(`Request: ${operation.operationName}`, {
  //   variables: operation.variables,
  //   headers: operation.getContext().headers,
  // });

  return forward(operation).map((response) => {
    // console.log(`Response: ${operation.operationName}`, response);
    return response;
  });
});
import { RetryLink } from "@apollo/client/link/retry";
import { LENS_API_URL } from "./constant";

const retryLink = new RetryLink({
  delay: {
    initial: 100
  },
  attempts: {
    max: 2,
    retryIf: (error) => Boolean(error)
  }
});

export const apolloClient = new ApolloClient({
  link: authLink ? ApolloLink.from([debugLink, authLink, httpLink , retryLink]) : 
  ApolloLink.from([debugLink, httpLink , retryLink]),
  cache: new InMemoryCache(),
});
