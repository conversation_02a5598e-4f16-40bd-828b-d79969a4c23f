import {
  SideSheetContainer,
  SideSheetHeader,
  SideSheetDescription,
} from "@/components/ui/sidebarSheet";
import * as Tabs from "@radix-ui/react-tabs";
import { TabsContent, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import Detail from "./detail";
import ActiveLog from "./activeLog";
export function OrderCardDetail({
  open,
  onOpenChange,
  cardId,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cardId: any;
}) {
  return (
    <SideSheetContainer
      className="max-md:left-0 left-[48.25rem] max-lg:left-[26.25rem] max-w-[26.25rem]"
      open={open}
      onOpenChange={(isOpen) => {
        // Prevent closing the sheet unless explicitly triggered
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Header */}
      <SideSheetHeader className="px-8 xs-px-2">
        {/* Header */}
        <div
          className="cursor-pointer text-base font-normal row gap-2"
          // Close only when Back button is clicked
          onClick={() => onOpenChange(false)}
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">{cardId}</p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>
      {/* Tabs */}
      <SideSheetDescription>
        <div className="flex flex-col mr-4">
          <div className="row justify-between">
            <div>
              <p className="text-primary">Current Status: </p>
              <p className="font-bold">NEW</p>
            </div>
            <Badge className=" btn-xs text-white btn">Change Status</Badge>
          </div>
          <Tabs.Root
            defaultValue="Details"
            className=" overflow-scroll h-full hide-scroll max-md:overflow-x-hidden mt-4"
          >
            <div className="sticky top-0 bg-white z-50 pb-2 ">
              <Tabs.List
                className="TabsListBg w-full"
                aria-label="Manage your account"
                style={
                  {
                    "--active-bg-color": "#BDBDBD",
                  } as React.CSSProperties
                }
              >
                <Tabs.Trigger className="TabsTriggerBg" value="Details">
                  Details
                </Tabs.Trigger>
                <Tabs.Trigger className="TabsTriggerBg" value="Activity log">
                  Activity log
                </Tabs.Trigger>
              </Tabs.List>
            </div>

            <div className="pb-12">
              <TabsContent value="Details" className="h-full">
                <Detail />
              </TabsContent>
              <TabsContent value="Activity log">
                <ActiveLog />
              </TabsContent>
            </div>
          </Tabs.Root>
        </div>
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
