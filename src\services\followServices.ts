import {
  arrayRemove,
  arrayUnion,
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { User } from "./UserInterface";
import { initFirebase } from "../../firebaseConfig";
import { getAuth } from "firebase/auth";
import { NotificationEvents, NotificationManager } from "./notificationService";

// const resp = await FollowerManager.getInstance().FollowByUserId({
//   src_id:"eEeCcJ3In5bWGHbauY4nRHgxuOn2",
//      dest_id:"Q7VlDFTQWLfQx9lsEfnPstVs6Mq1"
// })

//   const resp = await FollowerManager.getInstance().UnfollowByUserId({
//     src_id:"eEeCcJ3In5bWGHbauY4nRHgxuOn2" ,
//     dest_id:"Q7VlDFTQWLfQx9lsEfnPstVs6Mq1"
//   })

// const resp = await FollowerManager.getInstance().GetFollowersByUserId("WUOkxZXbzvS59NtNFEQHY8IyG993");

export class FollowerManager {
  private USERS_COLLECTION = "users";

  static instance: FollowerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new FollowerManager();
    }
    return this.instance;
  }

  // mutations

  async FollowByUserId({ src_id, dest_id }: { src_id: string | undefined; dest_id: string }) {
    try {
      if (!src_id) {
        console.error("Source ID is required for follow action");
        return "failed";
      }

      const auth = getAuth();
      const user = auth.currentUser;
      if (!user?.uid) {
        return { success: false };
      }
      const { db } = await initFirebase();

      const _src = doc(db, this.USERS_COLLECTION, src_id);
      const _dest = doc(db, this.USERS_COLLECTION, dest_id);
      if (src_id === dest_id) return "failed";

      await updateDoc(_src, {
        bookmarks: arrayUnion(dest_id),
      });

      // update notification
      await NotificationManager.getInstance().CreateNotification({
        payload:{
          src_id , 
          dest_id , 
          event:NotificationEvents.FOLLOW,
        }
      })
     

      return "success";
    } catch (error) {
      console.error("Error following user:", error);
      throw new Error("follow_req_failed");
    }
  }

  async UnfollowByUserId({ src_id, dest_id }: { src_id: string | undefined; dest_id: string }) {
    try {
      if (!src_id) {
        console.error("Source ID is required for unfollow action");
        return "failed";
      }

      const auth = getAuth();
      const user = auth.currentUser;
      if (!user?.uid) {
        return { success: false };
      }
      const { db } = await initFirebase();

      const _src = doc(db, this.USERS_COLLECTION, src_id);
      const _dest = doc(db, this.USERS_COLLECTION, dest_id);

      if (src_id === dest_id) return "failed";

      await updateDoc(_src, {
        bookmarks: arrayRemove(dest_id),
      });

      // await updateDoc(_dest, {
      //   followers: arrayRemove(src_id),
      // });

      return "success";
    } catch (error) {
      console.error("Error unfollowing user:", error);
      throw new Error("unfollow_req_failed");
    }
  }

  private async getFollowers(userId: string): Promise<User[]> {
    const { db } = await initFirebase();

    const usersCollection = collection(db, this.USERS_COLLECTION);
    try {
      const followersQuery = query(usersCollection, where("bookmarks", "array-contains", userId));
      const followersSnap = await getDocs(followersQuery);
      // console.log({followersSnap});

      return followersSnap.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as User);
    } catch (error) {
      console.error("Error fetching followers:", error);
      return [];
    }
  }

  async GetFollowersByUserId(userId: string) {
    try {
      // Check if userId is valid
      if (!userId || typeof userId !== "string" || userId.trim() === "") {
        console.error("Invalid userId provided:", userId);
        return [];
      }

      const { db } = await initFirebase();

      const userRef = doc(db, this.USERS_COLLECTION, userId);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) {
        throw new Error("user_not_found");
      }

      const follower: User[] = await this.getFollowers(userId);

      return follower;
    } catch (error) {
      console.error("Error getting followers:", error);
      return [];
      // throw new Error("get_followers_failed");
    }
  }

  async GetFollowingsByUserId(userId: string) {
    try {
      // Check if userId is valid
      if (!userId || typeof userId !== "string" || userId.trim() === "") {
        console.error("Invalid userId provided:", userId);
        return [];
      }

      const { db } = await initFirebase();

      const userRef = doc(db, this.USERS_COLLECTION, userId);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) {
        throw new Error("user_not_found");
      }

      const userData = userSnap.data();
      const followingIds: string[] = userData?.bookmarks || [];

      if (followingIds.length === 0) return [];

      const usersCollection = collection(db, this.USERS_COLLECTION);
      const batchSize = 10; // Firestore in query supports max 10 values

      // Split followingIds into chunks of 10
      const chunks = [];
      for (let i = 0; i < followingIds.length; i += batchSize) {
        chunks.push(followingIds.slice(i, i + batchSize));
      }

      // Fetch data in batches

      const queries = chunks.map((chunk) => {
        const followingsQuery = query(usersCollection, where("__name__", "in", chunk));
        return getDocs(followingsQuery);
      });
      const snapshots = await Promise.all(queries);
      const followingsData = snapshots.flatMap((snapshot) =>
        snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
      );

      // const followingsData = [];
      // for (const chunk of chunks) {
      //   const followingsQuery = query(usersCollection, where("__name__", "in", chunk));
      //   const followingsSnap = await getDocs(followingsQuery);
      //   followingsData.push(...followingsSnap.docs.map((doc) => ({ id: doc.id, ...doc.data() })));
      // }

      // console.log({ followingsData, userId });

      return followingsData;
    } catch (error) {
      console.error("Error getting followings:", error);
      return [];

      // throw new Error("get_followings_failed");
    }
  }
}
