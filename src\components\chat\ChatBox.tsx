import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Send } from 'react-feather';
import { ChatManager } from '../../services/chatService';
import useAuth from '@/hook';
import { Messages } from '../../services/chatService';
import RichTextFormatter from '../RichTextFormatter';

// Extend the Messages type to include id
type MessageWithId = Messages & { id: string };

interface ChatBoxProps {
  selectedChatId: string;
  selectedChatName: string;
  selectedChatImg?: string;
  userProfileName?: string;
  onBack?: () => void;
  chatData: {
    chatId: string;
    userName: string;
    imgUrl: string;
    chat: {
      fromProfile: string;
      toProfile: string;
      fromProfileName: string;
      toProfileName: string;
    };
    messages: any[];
  };
}

const ChatBox: React.FC<ChatBoxProps> = ({ 
  selectedChatId,
  selectedChatName, 
  selectedChatImg, 
  userProfileName,
  onBack,
  chatData 
}) => {
  const [messages, setMessages] = useState<MessageWithId[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const auth = useAuth();
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  
  // Function to handle keyboard visibility
  useEffect(() => {
    const handleResize = () => {
      const isKeyboard = window.innerHeight < window.outerHeight * 0.75;
      setIsKeyboardVisible(isKeyboard);
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('focusin', () => setIsKeyboardVisible(true));
    document.addEventListener('focusout', () => setIsKeyboardVisible(false));

    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('focusin', () => setIsKeyboardVisible(true));
      document.removeEventListener('focusout', () => setIsKeyboardVisible(false));
    };
  }, []);

  // Initialize messages from chatData
  useEffect(() => {
    if (chatData?.messages) {
      setMessages(chatData.messages.map(msg => ({
        ...msg,
        id: msg.id || Math.random().toString(36).substr(2, 9) // Ensure each message has an id
      })));
    }
  }, [chatData]);

  // Initialize chat and listen for messages
  useEffect(() => {
    let stopListening: (() => void) | undefined;

    const initChat = async () => {
      try {
        const otherUserId = auth.userId === chatData.chat.fromProfile 
          ? chatData.chat.toProfile 
          : chatData.chat.fromProfile;
        
        if (!otherUserId) return;

        stopListening = await ChatManager.getInstance().GetMessagesBetweenUsers({
          userA: otherUserId,
          userB: auth.userId,
          onUpdate: (newMessages: MessageWithId[]) => {
            setMessages(newMessages);
            
            // Update seen flag for new messages
            if (newMessages?.length) {
              ChatManager.getInstance().UpdateSeenFlag({
                chatId: newMessages[0]?.chatId,
                currentUserId: auth.userId,
                msgs: newMessages,
              });
            }
          },
        });
      } catch (error) {
        console.error('Error initializing chat:', error);
      }
    };

    if (selectedChatId && auth.userId && chatData) {
      initChat();
    }

    return () => {
      if (stopListening) {
        stopListening();
      }
    };
  }, [selectedChatId, auth.userId, chatData]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedChatId) {
      console.error('No chat selected');
      return;
    }

    if (!auth.userId) {
      console.error('User not authenticated');
      return;
    }

    if (!chatData) {
      console.error('Chat data not available');
      return;
    }

    if (!newMessage.trim()) {
      return;
    }

    try {
      const otherUserId = auth.userId === chatData.chat.fromProfile 
        ? chatData.chat.toProfile 
        : chatData.chat.fromProfile;

      if (!otherUserId) {
        console.error('Could not determine recipient ID');
        return;
      }

      await ChatManager.getInstance().SendMessage({
        chatId: selectedChatId,
        message: newMessage.trim(),
        senderId: auth.userId,
        senderName: auth.userData?.profile_name ?? "",
        recipientId: otherUserId,
        recipientName: selectedChatName,
      });
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleFocus = () => {
    setTimeout(() => {
      const messagesContainer = document.querySelector('.messages-container');
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }, 100);
  };

  return (
    <div className="flex flex-col h-full bg-white relative">
      {/* Messages */}
      <div className="messages-container flex-1 overflow-y-auto overflow-x-hidden px-2 py-4">
        <div className="flex flex-col gap-1">
          {(() => {
            let lastDate: Date | null = null;
            const today = new Date();
            const tomorrow = new Date();
            tomorrow.setDate(today.getDate() + 1);
            function isSameDay(d1: Date, d2: Date) {
              return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
            }
            function formatDateLabel(date: Date) {
              if (isSameDay(date, today)) return 'Today';
              if (isSameDay(date, tomorrow)) return 'Tomorrow';
              return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
            }
            return messages.map((message, idx) => {
              let msgDate: Date;
              if (message.createdAt && typeof message.createdAt === 'object' && typeof message.createdAt.toDate === 'function') {
                msgDate = message.createdAt.toDate();
              } else {
                msgDate = new Date(message.createdAt as unknown as string | number | Date);
              }
              let showDate = false;
              if (!lastDate || !isSameDay(msgDate, lastDate)) {
                showDate = true;
                lastDate = msgDate;
              }
              return (
                <React.Fragment key={message.id}>
                  {showDate && (
                    <div className="w-full flex justify-center my-2">
                      <span className="bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full font-medium shadow-sm">
                        {formatDateLabel(msgDate)}
                      </span>
                    </div>
                  )}
                  <div
                    className={`flex mb-1 ${message.senderId === auth.userId ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className="flex flex-row gap-0.5 max-w-[85%]">
                      <div
                        className={`px-3 py-2 rounded-[18px] row gap-2 ${
                          message.senderId === auth.userId
                            ? 'bg-gray-200'
                            : 'bg-gray-100'
                        } break-all whitespace-pre-wrap flex flex-col justify-end items-end`}
                      >
                        <RichTextFormatter
                            text={(() => { try { return message.message ? decodeURIComponent(message.message) : ""; } catch { return message.message || ""; } })()}
                            className="text-[15px] text-gray-900 break-all whitespace-pre-wrap w-full"
                            preserveWhitespace={true}
                            enableMarkdown={true}
                          />
                        <span className="text-xs text-gray-400 px-1 min-w-[45px] self-end">
                          {msgDate.toLocaleTimeString('en-GB', { 
                            hour: '2-digit', 
                            minute: '2-digit',
                            hour12: false 
                          })}
                        </span>
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              );
            });
          })()}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Message Input */}
      <div className={`border-t border-gray-200 w-full sticky bottom-0 left-0 right-0 px-2 pt-3 ${isKeyboardVisible ? 'mb-[env(keyboard-inset-height,0)]' : ''}`}>
        <form onSubmit={handleSendMessage} className="flex items-center gap-3">
          <div className="flex-1 bg-white rounded-sm border border-gray-200 overflow-hidden">
            <textarea
              // type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onFocus={handleFocus}
              placeholder="Start Chat..."
              className="w-full px-4 py-2.5 text-[15px] focus:outline-none h-[50px] resize-none"
            />
          </div>
          <button
            type="submit"
            disabled={!newMessage.trim()}
            className={`flex items-center justify-center min-w-[60px] h-[38px] rounded-full transition-colors ${
              newMessage.trim()
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-400'
            }`}
          >
            <span className="font-medium text-[15px]">Send</span>
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatBox; 