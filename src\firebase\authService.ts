"use client";
import {
  Auth,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
} from "firebase/auth";

import { toast } from "sonner";

import { signInWithPopup, FacebookAuthProvider } from "firebase/auth";
import { initFirebase } from "../../firebaseConfig";

// Sign up
export const signUp = async (email: string, password: string) => {
  try {
    const { auth } = await initFirebase();
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error: any) {
    if (error) {
      toast("Error: Email is already in use", {
        description: "Please use a different email address.",
        action: {
          label: "Try Again",
          onClick: () => console.log("Retrying signup"),
        },
      });
      return error; // Exit early
    }
    console.error("Error signing up:", error);
    throw error; // Re-throw other errors
  }
};

// Log in
export const logIn = async (email: string, password: string) => {
  try {
    const { auth } = await initFirebase();
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    // console.log({ userCredential });

    return userCredential.user;
  } catch (error) {
    throw error;
  }
};

// Log out
export const logOut = async () => {
  try {
    const { auth } = await initFirebase();
    await signOut(auth);
    localStorage.removeItem("user");
  } catch (error) {
    throw error;
  }
};

export const googleLogin = async () => {
  try {
    const { auth, googleProvider } = await initFirebase();

    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;
    // console.log("User Info:", user);
    return user; // Return user info for further use
  } catch (error: any) {
    console.error("Error during Google Login:", error.message);
    throw error;
  }
};

export const appleLogin = async () => {
  try {
    const { auth, appleProvider } = await initFirebase();

    const result = await signInWithPopup(auth, appleProvider); // Apple login
    const user = result.user;
    // console.log("Apple User Info:", user);
    return user;
  } catch (error: any) {
    console.error("Error during Apple Login:", error.message);
    throw error;
  }
};

export const facebookLogin = async () => {
  const facebookProvider = new FacebookAuthProvider();

  try {
    const { auth } = await initFirebase();

    const result = await signInWithPopup(auth, facebookProvider);

    // The signed-in user information.
    const user = result.user;

    return user;
  } catch (error: any) {
    console.error("Error during Facebook Login:", error.message);

    // Optional: Handle specific error codes (e.g., user cancels login)
    if (error.code === "auth/account-exists-with-different-credential") {
      console.error("Account exists with a different credential.");
    }

    throw error;
  }
};
