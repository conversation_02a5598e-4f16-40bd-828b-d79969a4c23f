import { isTokenExpired, readAccessToken } from "@/lib/auth/auth-helpers";
import refreshAccessToken from "@/lib/auth/refreshAccessToken";
import { LENS_API_URL } from "@/lib/constant";

export const fetcher = <TData, TVariables>(
  query: string,
  variables?: TVariables,
  options?: RequestInit["headers"]
): (() => Promise<TData>) => {
  // TODO ✅✅✅✅✅

  async function getAccessToken() {
    // 1. Check the local storage for the access token
    const token = readAccessToken();

    // 2. If there isn't a token, then return null (not signed in)
    if (!token) return null;

    let accessToken = token.accessToken;

    // 3. If there is a token, then check it's expiration
    if (isTokenExpired(token.exp)) {
      // 4. If it's expired, update it using the refresh token
      const newToken = await refreshAccessToken();
      if (!newToken) return null;
      accessToken = newToken;
    }

    // Finally, return the token
    return accessToken;
  }

  return async () => {
    const token = typeof window !== "undefined" ? await getAccessToken() : null;

    const res = await fetch(LENS_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...options,
        Authorization: token ? `Bearer ${token}` : "",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const json = await res.json();

    if (json.errors) {
      // console.error("GraphQL Error Details:", json.errors);
      const { message } = json.errors[0] || {};
      throw new Error(message || "Error…");
    }

    return json.data;
  };
};
