"use client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEffect, useRef, useState } from "react";
import { updateUser } from "@/services/usersServices";
import { arrayUnion } from "firebase/firestore";
import { createEvent, getEventById, Event, updateEvent } from "@/services/eventsServices";
import useAuth from "@/hook";
import { X, Loader, Check } from "react-feather";
import { Textarea } from "@/components/ui/textarea";
import ServiceDescription from "@/globalComponents/formatText";

const EditEvents = ({ eventId, onSelectServiceDetails, otherUserID }: any) => {
  const auth = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const postsRef = useRef<HTMLDivElement>(null);

  const [title, setTitle] = useState<string>("");
  const [about, setAbout] = useState<string>("");
  const [date, setDate] = useState<string>("");
  const [time, setTime] = useState<string>("");
  const [dateTime, setDateTime] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [eventData, setEventData] = useState<any>(null);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");

  // Validation states
  const [errors, setErrors] = useState<{
    title: string;
    about: string;
    date: string;
    time: string;
  }>({
    title: "",
    about: "",
    date: "",
    time: "",
  });
  const [touched, setTouched] = useState<{
    title: boolean;
    about: boolean;
    date: boolean;
    time: boolean;
  }>({
    title: false,
    about: false,
    date: false,
    time: false,
  });

  // Fetch event data if eventId is provided
  useEffect(() => {
    const fetchEventData = async () => {
      if (eventId) {
        setIsLoading(true);
        setIsEditMode(true);

        try {
          // console.log("Fetching event with ID:", eventId);
          const response = await getEventById(eventId);
          // console.log("Event fetch response:", response);

          if (response.success && response.event) {
            const event = response.event;
            setEventData(event);

            // Set form fields directly from event data
            setTitle(event.name || "");
            setAbout(event.description || "");

            // Handle date formatting
            if (event.date) {
              setDateTime(event.date);

              try {
                // Try parsing the date string - first remove any ordinal indicators (1st, 2nd, etc.)
                const cleanDateStr = event.date.replace(/(\d+)(st|nd|rd|th)/, "$1");

                // Parse date with various possible formats
                let dateObj;
                try {
                  // Try direct parsing
                  dateObj = new Date(cleanDateStr);

                  // If that didn't work, try manual parsing
                  if (isNaN(dateObj.getTime())) {
                    // Example format: "11 June 2023 10:00"
                    const parts = cleanDateStr.split(" ");
                    const day = parseInt(parts[0]);
                    const month = new Date(Date.parse(parts[1] + " 1, 2000")).getMonth();
                    const year = parseInt(parts[2]);
                    const timeParts = parts[3].split(":");
                    const hours = parseInt(timeParts[0]);
                    const minutes = parseInt(timeParts[1]);

                    dateObj = new Date(year, month, day, hours, minutes);
                  }
                } catch (e) {
                  console.error("Error in date parsing:", e);
                }

                if (dateObj && !isNaN(dateObj.getTime())) {
                  // Format date as YYYY-MM-DD for input
                  const year = dateObj.getFullYear();
                  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
                  const day = String(dateObj.getDate()).padStart(2, "0");
                  const formattedDate = `${year}-${month}-${day}`;

                  // Format time as HH:MM for input
                  const hours = String(dateObj.getHours()).padStart(2, "0");
                  const minutes = String(dateObj.getMinutes()).padStart(2, "0");
                  const formattedTime = `${hours}:${minutes}`;

                  // console.log("Setting date to:", formattedDate);
                  // console.log("Setting time to:", formattedTime);

                  setDate(formattedDate);
                  setTime(formattedTime);
                } else {
                  console.error("Invalid date object:", dateObj);
                }
              } catch (error) {
                console.error("Error parsing date:", error);
              }
            }
          } else {
            console.error("Error fetching event:", response.error);
          }
        } catch (error) {
          console.error("Error in fetch operation:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    // Reset form when eventId changes
    setTitle("");
    setAbout("");
    setDate("");
    setTime("");
    setDateTime("");

    fetchEventData();
  }, [eventId]);

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === "success") {
      // Wait 1.5 seconds before navigating back
      const timer = setTimeout(() => {
        // Force a refresh of the event list by passing a refresh flag
        if (onSelectServiceDetails) onSelectServiceDetails("refresh");
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, onSelectServiceDetails]);

  // Add debug useEffect to monitor state changes

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Mark all fields as touched to show validation errors
    setTouched({
      title: true,
      about: true,
      date: true,
      time: true,
    });

    // Validate all fields
    const isValid = validateForm();

    if (!isValid) {
      // If form is not valid, don't submit
      return;
    }

    const formData = {
      id: isEditMode ? eventId : auth?.userData?.uid,
      name: title,
      description: about,
      date: dateTime,
    };

    try {
      setIsLoading(true);
      setUploadStatus("uploading");

      if (isEditMode && eventId) {
        // Update existing event
        const response = await updateEvent(eventId, formData);
        if (response.success) {
          console.log("Event updated successfully");
          setUploadStatus("success");
          // Navigation will happen via useEffect after showing success state
        } else {
          console.error("Error updating event:", response.error);
          setUploadStatus("idle");
          setIsLoading(false);
        }
      } else {
        // Create new event
        const response = await createEvent(formData);
        if (response.success) {
          const postId = response.id;
          const userId = auth?.userData?.uid;

          if (userId && postId) {
            await updateUser(userId, { events: arrayUnion(postId) });
            console.log("Event created and user updated successfully");
            setUploadStatus("success");
            // Navigation will happen via useEffect after showing success state
          }
        } else {
          console.error("Error creating event:", response);
          setUploadStatus("idle");
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error("Operation failed:", error);
      setUploadStatus("idle");
      setIsLoading(false);
    }
  };

  // Function to update date and time in the required format
  const updateDateTime = (selectedDate: string, selectedTime: string) => {
    if (selectedDate && selectedTime) {
      const [year, month, day] = selectedDate.split("-");
      const [hours, minutes] = selectedTime.split(":");

      // Convert month number to full month name
      const monthName = new Date(parseInt(year), parseInt(month) - 1).toLocaleString("default", {
        month: "long",
      });

      // Format date as "11 June 2023 10:00"
      const formattedDateTime = `${parseInt(day)} ${monthName} ${year} ${hours}:${minutes}`;

      setDateTime(formattedDateTime);
    }
  };

  // Validation function
  const validateField = (name: string, value: string) => {
    let errorMessage = "";

    switch (name) {
      case "title":
        if (!value.trim()) {
          errorMessage = "Event name is required";
        }
        // else if (value.trim().length < 3) {
        //   errorMessage = "Event name must be at least 3 characters";
        // } else if (value.trim().length > 50) {
        //   errorMessage = "Event name must be less than 50 characters";
        // }
        break;
      case "about":
        if (!value.trim()) {
          errorMessage = "Description is required";
        }
        // else if (value.trim().length < 10) {
        //   errorMessage = "Description must be at least 10 characters";
        // }
        break;
      case "date":
        if (!value) {
          errorMessage = "Date is required";
        } else {
          const selectedDate = new Date(value);
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          if (selectedDate < today) {
            errorMessage = "Date cannot be in the past";
          }
        }
        break;
      case "time":
        if (!value) {
          errorMessage = "Time is required";
        }
        break;
      default:
        break;
    }

    return errorMessage;
  };

  // Validate all fields
  const validateForm = () => {
    const newErrors = {
      title: validateField("title", title),
      about: validateField("about", about),
      date: validateField("date", date),
      time: validateField("time", time),
    };

    setErrors(newErrors);

    // Form is valid if all error messages are empty
    return Object.values(newErrors).every((error) => error === "");
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Update field value
    if (name === "title") {
      setTitle(value);
    } else if (name === "description") {
      setAbout(value);
    }

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      [name]: true,
    }));

    // Validate field
    setErrors((prev) => ({
      ...prev,
      [name]: validateField(name, value),
    }));
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setDate(value);
    updateDateTime(value, time);

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      date: true,
    }));

    // Validate field
    setErrors((prev) => ({
      ...prev,
      date: validateField("date", value),
    }));
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setTime(value);
    updateDateTime(date, value);

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      time: true,
    }));

    // Validate field
    setErrors((prev) => ({
      ...prev,
      time: validateField("time", value),
    }));
  };

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === "success") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">
            Event {isEditMode ? "Updated" : "Created"} Successfully!
          </h2>
          <p className="text-gray-500">
            Your event has been {isEditMode ? "updated" : "created"} and will be visible on your
            profile.
          </p>
        </div>
      );
    }

    if (uploadStatus === "uploading") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">
            {isEditMode ? "Updating" : "Creating"} Your Event...
          </h2>
          <p className="text-gray-500">
            Please wait while we {isEditMode ? "update" : "create"} your event. This may take a
            moment.
          </p>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Loading Event Data...</h2>
          <p className="text-gray-500">Please wait while we load your event information.</p>
        </div>
      );
    }

    return (
      <>
        <div className="flex justify-between mb-4">
          <p
            onClick={() => onSelectServiceDetails()}
            className="font-bold text-primary cursor-pointer"
          >
            <X />
          </p>
          <p className="font-bold text-primary cursor-pointer">
            {otherUserID === "my-profile" ? " Edit Event" : "Event Details"}
          </p>
          {otherUserID === "my-profile" ? (
            <p
              onClick={() => {
                // Mark all fields as touched to show validation errors
                setTouched({
                  title: true,
                  about: true,
                  date: true,
                  time: true,
                });

                // Only submit if all fields are valid
                if (
                  title &&
                  about &&
                  date &&
                  time &&
                  !errors.title &&
                  !errors.about &&
                  !errors.date &&
                  !errors.time
                ) {
                  // Create a synthetic form event
                  const syntheticEvent = {
                    preventDefault: () => {},
                  } as React.FormEvent;
                  handleSubmit(syntheticEvent);
                }
              }}
              className={
                title &&
                about &&
                date &&
                time &&
                !errors.title &&
                !errors.about &&
                !errors.date &&
                !errors.time
                  ? "font-bold text-primary cursor-pointer"
                  : "font-bold text-borderColor cursor-not-allowed"
              }
            >
              {isEditMode ? "Update" : "Save"}
            </p>
          ) : (
            <p className="none opacity-0">{isEditMode ? "Update" : "Save"}</p>
          )}
        </div>

        <div className="bg-gray-50">
          <div className="w-full mx-auto bg-white rounded-md p-3 py-4">
            {otherUserID === "my-profile" ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid w-full md:max-full items-center gap-1.5 mt-6 max-md:text-start">
                  <Label
                    htmlFor="eventName"
                    className="text-primary mb-2 font-[600] block text-start"
                  >
                    Event name
                  </Label>
                  <Input
                    type="text"
                    id="eventName"
                    name="title"
                    placeholder="Event name"
                    className={`border p-2 rounded w-full text-primary h-10 ${
                      touched.title && errors.title ? "border-red-500" : ""
                    }`}
                    value={title}
                    onChange={(e) => {
                      setTitle(e.target.value);
                      setTouched((prev) => ({ ...prev, title: true }));
                      setErrors((prev) => ({
                        ...prev,
                        title: validateField("title", e.target.value),
                      }));
                    }}
                    disabled={otherUserID === "my-profile" ? false : true}
                  />
                  {touched.title && errors.title && (
                    <p className="text-red-500 text-xs mt-1">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="description"
                    className="text-primary mb-2 font-[600] block text-start"
                  >
                    Description
                  </label>
                  <Textarea
                    id="description"
                    name="description"
                    value={about}
                    onChange={(e) => {
                      setAbout(e.target.value);
                      setTouched((prev) => ({ ...prev, about: true }));
                      setErrors((prev) => ({
                        ...prev,
                        about: validateField("about", e.target.value),
                      }));
                    }}
                    placeholder="Tell the world about your event"
                    className={`w-full p-2 border border-gray-300 rounded-md ${
                      touched.about && errors.about ? "border-red-500" : ""
                    }`}
                    disabled={otherUserID === "my-profile" ? false : true}
                  />
                  {touched.about && errors.about && (
                    <p className="text-red-500 text-xs mt-1">{errors.about}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <label
                    htmlFor="event-datetime"
                    className="text-primary mb-2 font-[600] block text-start"
                  >
                    Event Date & Time
                  </label>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Date Selection */}
                    <div className="space-y-2">
                      <label htmlFor="event-date" className="text-sm text-gray-600 font-medium">
                        Date
                      </label>
                      <div
                        className="relative"
                        onClick={() => {
                          if (otherUserID === "my-profile") {
                            const dateInput = document.getElementById(
                              "event-date"
                            ) as HTMLInputElement;
                            dateInput?.showPicker && dateInput.showPicker();
                          }
                        }}
                      >
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                        </div>
                        <input
                          type="date"
                          id="event-date"
                          value={date}
                          onChange={handleDateChange}
                          className={`pl-10 w-full border p-2.5 rounded-md shadow-sm focus:ring-primary focus:border-primary ${
                            otherUserID !== "my-profile"
                              ? "opacity-50 cursor-not-allowed"
                              : "cursor-pointer"
                          } ${touched.date && errors.date ? "border-red-500" : "border-gray-300"}`}
                          disabled={otherUserID !== "my-profile"}
                          min={new Date().toISOString().split("T")[0]} // Prevent selecting past dates
                        />
                        {/* Transparent overlay to make entire area clickable */}
                        <div
                          className={`absolute inset-0 ${
                            otherUserID !== "my-profile" ? "" : "cursor-pointer"
                          }`}
                        ></div>
                      </div>
                      {touched.date && errors.date ? (
                        <p className="text-red-500 text-xs mt-1">{errors.date}</p>
                      ) : !date ? (
                        <p className="text-xs text-gray-500">Select the event date</p>
                      ) : null}
                    </div>

                    {/* Time Selection */}
                    <div className="space-y-2">
                      <label htmlFor="event-time" className="text-sm text-gray-600 font-medium">
                        Time
                      </label>
                      <div
                        className="relative"
                        onClick={() => {
                          if (otherUserID === "my-profile") {
                            const timeInput = document.getElementById(
                              "event-time"
                            ) as HTMLInputElement;
                            timeInput?.showPicker && timeInput.showPicker();
                          }
                        }}
                      >
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </div>
                        <input
                          type="time"
                          id="event-time"
                          value={time}
                          onChange={handleTimeChange}
                          className={`pl-10 w-full border p-2.5 rounded-md shadow-sm focus:ring-primary focus:border-primary ${
                            otherUserID !== "my-profile"
                              ? "opacity-50 cursor-not-allowed"
                              : "cursor-pointer"
                          } ${touched.time && errors.time ? "border-red-500" : "border-gray-300"}`}
                          disabled={otherUserID !== "my-profile"}
                        />
                        {/* Transparent overlay to make entire area clickable */}
                        <div
                          className={`absolute inset-0 ${
                            otherUserID !== "my-profile" ? "" : "cursor-pointer"
                          }`}
                        ></div>
                      </div>
                      {touched.time && errors.time ? (
                        <p className="text-red-500 text-xs mt-1">{errors.time}</p>
                      ) : !time ? (
                        <p className="text-xs text-gray-500">Select the event time</p>
                      ) : null}
                    </div>
                  </div>
                </div>
              </form>
            ) : (
              <div>
                <p className="my-3 text-primary text-lg font-bold max-md:text-base">{title}</p>
                <div className="text-subtitle mt-3 break-words whitespace-pre-wrap">
                  <ServiceDescription description={about} />
                </div>
                {/* Preview of formatted date and time */}
                {date && time && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200">
                    <p className="text-sm font-medium text-gray-700">Event scheduled for:</p>
                    <p className="text-primary font-semibold">{dateTime}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </>
    );
  };

  return <>{renderContent()}</>;
};

export default EditEvents;
