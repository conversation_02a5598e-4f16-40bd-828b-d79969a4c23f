import React, { useEffect, useState } from 'react';
import { ChatManager } from '../../services/chatService';
import useAuth from '@/hook';
import { Timestamp } from 'firebase/firestore';
import { Loader, MessageSquare } from 'react-feather';
import { collection, query, or, where, orderBy, onSnapshot, limit } from 'firebase/firestore';
import { initFirebase } from '../../../firebaseConfig';

interface ChatMessage {
  imgUrl: string;
  colorVal: string;
  userName: string;
  text: string;
  time: Timestamp;
  unread: number;
  chatId: string;
  chat: {
    fromProfile: string;
    toProfile: string;
    fromProfileName: string;
    toProfileName: string;
    profile_pic: string;

  };
  messages: any[];
}

interface ChatListProps {
  onSelectChat: (chatData: {
    chatId: string;
    userName: string;
    imgUrl: string;
    chat: ChatMessage['chat'];
    messages: any[];
  }) => void;
}

const ChatList: React.FC<ChatListProps> = ({ onSelectChat }) => {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const  auth = useAuth();

  // Function to generate initials from a name
  const getInitials = (name: string): string => {
    if (!name) return "";
    const nameParts = name.trim().split(/\s+/);
    return nameParts.length === 1
      ? nameParts[0].charAt(0).toUpperCase()
      : (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  };

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;
    if (auth.userId) {
      unsubscribe = ChatManager.getInstance().ListenToUserChatSummaries({
        user_id: auth.userId,
        onUpdate: (chatList) => {
          setChatMessages(chatList);
          setIsLoading(false);
        },
      });
    }
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [auth.userId]);

  if (isLoading) {
    return  <div className="flex flex-col items-center justify-center h-[calc(100vh-100px)] space-y-4">
    <div className="relative">
      <Loader size={48} className="text-primary animate-spin" />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-4 h-4 bg-white rounded-full"></div>
      </div>
    </div>
    <p className="text-sm text-gray-500">Loading Chat...</p>
  </div>;
  }


  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) {
      return undefined;
    }

    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };
  
  return (
    <div className="mr-2">
      {chatMessages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-[calc(100vh-100px)] space-y-4">
          <MessageSquare size={48} className="text-gray-300" />
          <p className="text-sm text-gray-500">No chats yet</p>
          <p className="text-xs text-gray-400">Start a conversation to see it here</p>
        </div>
      ) : (
        chatMessages.map((msg) => (
          <div key={msg.chatId}>
            <div
              className="flex justify-between px-4 max-md:px-2 py-3 items-start border-b-[1px] cursor-pointer hover:bg-gray-50"
              onClick={() => onSelectChat({
                chatId: msg.chatId,
                userName: msg.userName,
                imgUrl: msg.imgUrl,
                chat: msg.chat,
                messages: msg.messages
              })}
            >
              <div className="flex flex-row gap-3">
                <div>
                  {msg.imgUrl ? (
                    <img
                      src={generateFileUrl(msg.imgUrl) || "/assets/noimg.png"}
                      alt=""
                      className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] rounded-full object-cover"
                      style={{
                        border: "3px solid " + msg.colorVal,
                      }}
                      onError={(e) => {
                        e.currentTarget.src = "/assets/noimg.png";
                      }}
                    />
                  ) : (
                    <div
                      className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-base"
                      style={{
                        border: "3px solid " + msg.colorVal,
                      }}
                    >
                      {getInitials(msg.userName)}
                    </div>
                  )}
                </div>
                <div className="text-left ">
                  <p className="font-bold text-primary text-[16px] leading-5">{msg.userName}</p>
                  <p className="text-primary text-[14px] leading-5 line-clamp-2 break-words">
                    {msg.text}
                  </p>
                </div>
              </div>
              <div className="flex flex-col items-end gap-y-[14px]">
                <p className="text-[#B0B0B0] text-right w-[3.1rem] text-[14px] leading-5">
                  {msg.time?.toDate().toLocaleTimeString('en-GB', { 
                    hour: '2-digit', 
                    minute: '2-digit',
                    hour12: false 
                  })}
                </p>
                {msg.unread > 0 && (
                  <div className="rounded-full bg-[#333333] py-[2px] px-2 text-center text-[14px] font-bold text-white">
                    {msg.unread}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ChatList; 