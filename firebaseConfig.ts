// firebaseConfig.ts
// This is a temporary solution to maintain backward compatibility
// while we transition to using the Firebase context
import { FirebaseApp, initializeApp } from "firebase/app";
import { Analytics, getAnalytics } from "firebase/analytics";
import { Auth, getAuth, GoogleAuthProvider, OAuthProvider } from "firebase/auth";
import { Firestore, getFirestore } from "firebase/firestore";
import { FirebaseStorage, getStorage } from "firebase/storage";

// Singleton pattern for Firebase initialization
let app: FirebaseApp | null = null;
let analytics: Analytics | null = null;
let auth: Auth | null = null;
let storage: FirebaseStorage | null = null;
let googleProvider: GoogleAuthProvider | null = null;
let appleProvider: OAuthProvider | null = null;
let db: Firestore | null = null;
let isInitialized = false;

// This function initializes Firebase if it hasn't been initialized yet
export const initFirebase = async () => {
  if (isInitialized && app && auth && db && storage && googleProvider && appleProvider) {
    // Firebase is already initialized, return the existing instances
    // No need to log anything here to avoid excessive console output
    return Promise.resolve({
      app,
      analytics,
      db,
      isLoading: false,
      auth,
      storage,
      googleProvider,
      appleProvider,
    });
  }

  try {
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
      messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID!,
      appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
    };

    app = initializeApp(firebaseConfig);

    // Initialize Firebase services
    if (typeof window !== "undefined") {
      analytics = getAnalytics(app);
    }

    auth = getAuth(app);
    db = getFirestore(app);
    storage = getStorage(app);
    googleProvider = new GoogleAuthProvider();
    appleProvider = new OAuthProvider("apple.com");

    isInitialized = true;

    // Log only once when Firebase is first initialized
    console.log("Firebase initialized successfully");

    return Promise.resolve({
      app,
      analytics,
      db,
      isLoading: false,
      auth,
      storage,
      googleProvider,
      appleProvider,
    });
  } catch (error) {
    console.error("Error initializing Firebase:", error);
    return Promise.reject(error);
  }
};
