"use client";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@radix-ui/react-dropdown-menu";
import { useEffect, useState } from "react";
import { Check, ChevronLeft, Loader } from "react-feather";
import EditCustomizationService from "./editCustomizations";
import {
  getServiceById,
  updateService,
  Service,
  formatDuration,
  listPricecal,
} from "@/services/serviceService";
import FileUploader from "./fileUploader";
import {
  getCurrencySymbol,
  initializeCurrency,
} from "@/services/currencyService";

interface EditServiceProps {
  onSelectService: (id: number) => void;
  id: string;
  categories?: string[];
  currencySymbol?: string;
}

const EditService = ({
  onSelectService,
  id,
  categories = ["Music", "Storytelling"],
  currencySymbol: propCurrencySymbol,
}: EditServiceProps) => {
  const [isTrue, setIsTrue] = useState(true);
  const [services, setServices]: any = useState<Service | undefined>();
  const [loading, setLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true); // Start with loading state active
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "success"
  >("idle");
  const [currencySymbol, setCurrencySymbol] = useState(
    propCurrencySymbol || "£"
  );
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    price: "",
    listPrice: "",
    duration: "",
    timeDays: "",
    category: "", // default category
    currency: initializeCurrency(), // Initialize with current app currency
  });

  const handleSelectService = (_: number) => {
    setIsTrue(!isTrue);
  };

  useEffect(() => {
    const fetchAllServices = async (id: any) => {
      setIsLoadingData(true); // Start loading

      // Set a timeout to ensure loading state is cleared even if fetch fails
      const loadingTimeout = setTimeout(() => {
        setIsLoadingData(false);
      }, 5000); // 5 second timeout as a fallback

      try {
        const response = await getServiceById(id);

        if (response?.success && response?.service) {
          // Type casting to make TypeScript happy
          const serviceData = response.service as unknown as Service;
          setServices(serviceData);

          // Get currency from service or use default
          const servicesCurrency =
            serviceData?.currency || initializeCurrency();

          // Only update the currency symbol if propCurrencySymbol wasn't provided
          if (!propCurrencySymbol) {
            const symbol = getCurrencySymbol(servicesCurrency);
            setCurrencySymbol(symbol);
          }

          // Pre-fill form data from services
          setFormData({
            title: serviceData?.title || "",
            description: serviceData?.description || "",
            price: serviceData?.price ? serviceData.price.toString() : "",
            listPrice: serviceData?.price
              ? (Number(serviceData.price) / (1 - 0.16)).toFixed(2)
              : "",
            duration: serviceData?.duration
              ? (Number(serviceData.duration) % 24).toString()
              : "",
            timeDays: serviceData?.duration
              ? Math.floor(Number(serviceData.duration) / 24).toString()
              : "",
            category: serviceData?.category || "",
            currency: servicesCurrency,
          });
        }
      } catch (error) {
        console.error("Error fetching service:", error);
        // Handle error state if needed
      } finally {
        clearTimeout(loadingTimeout); // Clear the timeout
        setIsLoadingData(false); // End loading regardless of success or failure
      }
    };

    if (id) {
      fetchAllServices(id);
    }
  }, [id, propCurrencySymbol]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === "success") {
      // Wait 1.5 seconds before navigating back
      const timer = setTimeout(() => {
        onSelectService(1);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, onSelectService]);

  const handleInputSave = async () => {
    setLoading(true);
    setUploadStatus("uploading");

    try {
      // Make sure currency is included in the update
      const dataToUpdate = {
        ...formData,
        currency: formData.currency || initializeCurrency(),
      };

      const update = await updateService(id, dataToUpdate);

      if (update.success) {
        setUploadStatus("success");
      } else {
        console.error("Failed to update service:", update.error);
        setUploadStatus("idle");
        setLoading(false);
      }
    } catch (error) {
      console.error("Error updating service:", error);
      setUploadStatus("idle");
      setLoading(false);
    }
  };

  // For calculating list price from service cost (add 16%)
  useEffect(() => {
    if (formData.price && formData.price !== "") {
      const serviceCost = parseFloat(formData.price);
      // Calculate listPrice as serviceCost / (1 - 0.16) for proper markup
      // This ensures the serviceCost is what remains after 16% is removed from listPrice
      const listPrice = parseFloat((serviceCost / (1 - 0.16)).toFixed(2));

      setFormData((prev) => ({
        ...prev,
        listPrice: listPrice.toString(),
      }));
    }
  }, [formData.price]);

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === "success") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2 text-center">
            Service Updated Successfully!
          </h2>
          <p className="text-gray-500 text-center">
            Your service has been updated and the changes will be visible on
            your profile.
          </p>
        </div>
      );
    }

    if (uploadStatus === "uploading") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2 text-center">
            Updating Your Service...
          </h2>
          <p className="text-gray-500 text-center">
            Please wait while we update your service. This may take a moment.
          </p>
        </div>
      );
    }

    if (isLoadingData) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2 text-center">
            Loading Service Data...
          </h2>
          <p className="text-gray-500 text-center">
            Please wait while we load your service information.
          </p>
        </div>
      );
    }

    return (
      <div>
        <div className="">
          {/* header */}
          <div className="w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div
                onClick={() => isTrue && onSelectService(1)}
                className="cursor-pointer row"
              >
                <ChevronLeft />
                <p className="pl-[2px]">Back</p>
              </div>
              <p className="text-titleLabel  font-bold">Edit Service</p>
              <p className="cursor-pointer" onClick={() => handleInputSave()}>
                Save
              </p>
            </div>
          </div>
          <div className="flex flex-row w-full overflow-scroll gap-3 hide-scroll-custom bg-white h-[calc(100vh-300px)] max-md:h-[calc(100vh-150px)] pt-4 pb-16 px-3">
            <div className="w-full">
              <p className="text-primary mb-1 font-[600]">Choose category</p>
              {categories && categories.length > 0 ? (
                <div className="row gap-3">
                  {categories.map((item: string, index: number) => (
                    <Button
                      key={index}
                      className={
                        formData.category === item
                          ? "text-white"
                          : "bg-[#F2F2F2] text-primary shadow-none hover:text-white"
                      }
                      onClick={() =>
                        setFormData((prev) => ({
                          ...prev,
                          category: item,
                        }))
                      }
                    >
                      {item}
                    </Button>
                  ))}
                </div>
              ) : (
                <p className="text-subtitle mt-2 mb-3">
                  No categories available.
                </p>
              )}
              <div>
                <div className="grid w-full items-center gap-1.5 mt-3">
                  <Label className="text-base font-[600] text-titleLabel">
                    Title
                  </Label>
                  <Input
                    name="title"
                    placeholder="Title"
                    className="resize-none h-[40px] outline-none text-lg text-primary"
                    value={formData.title}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="grid w-full items-center gap-1.5 mt-3">
                  <Label className="text-base font-[600] text-titleLabel">
                    Descriptions
                  </Label>
                  <Textarea
                    name="description"
                    placeholder="Descriptions"
                    className="resize-none h-40 outline-none text-lg text-primary"
                    value={formData.description}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-4">
                <div className="grid w-full  gap-1.5 mt-3">
                  <Label className="text-base font-[600] text-titleLabel">
                    Service Cost
                  </Label>
                  <p className="text-titleLabel items-center text-xs -mt-3">
                    (you will receive)
                  </p>
                  <div className="row gap-2">
                    <Input
                      name="price"
                      type="number"
                      placeholder="Service Cost"
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                      value={formData.price}
                      onChange={handleInputChange}
                      min={0}
                    />
                    <p>{currencySymbol}</p>
                  </div>
                  <p className="text-titleLabel text-xs font-light min-h-[32px]">
                    By Seller. VAT inclusive.
                  </p>
                </div>
                <div className="grid w-full items-center gap-1.5 mt-3">
                  <Label className="text-base font-[600] text-titleLabel">
                    List Price
                  </Label>
                  <p className="text-titleLabel text-xs -mt-3 ">
                    (customer will pay)
                  </p>
                  <div className="row gap-2">
                    <Input
                      name="listPrice"
                      disabled
                      placeholder="List Price"
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                      value={listPricecal(formData.price)}
                    />
                    <p>{currencySymbol}</p>
                  </div>
                  <p className="text-titleLabel text-xs font-light">
                    **Includes AMUZ App Fee (16% of List Price)
                  </p>
                </div>

                <div className="grid w-full items-center gap-1.5 mt-3">
                  <Label className="text-base font-[600] text-titleLabel">
                    Time
                  </Label>
                  <div className="row gap-2">
                    <Input
                      type="number"
                      name="duration"
                      placeholder="Hours"
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                      value={formData.duration}
                      onChange={handleInputChange}
                      max={112}
                      min={0}
                    />
                    <p>hours</p>
                  </div>
                </div>
                <div className="grid w-full items-center gap-1.5 mt-3">
                  <Label className="text-base font-[600] text-titleLabel">
                    Time
                  </Label>
                  <div className="row gap-2">
                    <Input
                      name="duration"
                      placeholder="days"
                      disabled
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                      value={formatDuration(formData.duration, {
                        dayLabel: "",
                        hourLabel: "",
                      })}
                    />
                    <p>days</p>
                  </div>
                </div>
              </div>

              <p className="mt-3">8 hours = 1 day</p>
              <p className="mt-3 text-xs">
                The service proposed delivery date shall not exceed 2 weeks (112
                hours).
              </p>
              <p className="mt-1 text-xs">
                The service delivery due date, based on above hour, will appear
                in 'My Order' page details after order obtaining ACCEPTED
                status.
              </p>

              {/* Media upload section */}
              <div className="mt-4">
                <p className="text-primary font-[600]">Media Files</p>
                <FileUploader />
              </div>

              {/* Customization section */}
              <p className="my-3 text-primary text-lg font-semibold text-center">
                Customization
              </p>

              <div className="grid grid-cols-2">
                <div>
                  <p className="text-primary  font-semibold">Option</p>
                </div>
                <div className="justify-end row">
                  <div className="row gap-4">
                    <p className="text-primary  font-semibold w-12 text-left">
                      Time
                    </p>
                    <p className="text-primary  font-semibold w-16 text-right">
                      Cost
                    </p>
                    <p className="opacity-0 w-8 text-center">hi</p>
                  </div>
                </div>
              </div>

              {/* Display existing customizations */}
              {services?.customizations_array &&
              services.customizations_array.length > 0 ? (
                services.customizations_array.map(
                  (item: any, indexs: number) => (
                    <div className="grid grid-cols-2 mt-2 " key={indexs}>
                      <div className="">
                        <p className="text-subtitle text-sm  mr-10">
                          {item.title || "Unnamed customization"}
                        </p>
                      </div>
                      <div className="justify-end row">
                        <div className="row gap-4">
                          <p className="text-subtitle w-12 text-left text-sm">
                            +
                            {item.duration
                              ? `${Math.floor(item.duration / 24)}d`
                              : "N/A"}
                          </p>
                          <p className="text-subtitle w-16 text-right text-sm">
                            +{currencySymbol}
                            {item.price
                              ? (item.price / (1 - 0.16)).toFixed(2)
                              : "0.00"}
                          </p>
                          <span className="text-borderColor w-8 text-left cursor-pointer">
                            ℹ️
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                )
              ) : (
                <p className="text-subtitle mt-3">
                  No customizations available.
                </p>
              )}

              <div className="row justify-center mt-5">
                <div className="row justify-center mt-5">
                  <Badge
                    className="btn-xs w-full py-4 border-primary btn text-white"
                    onClick={() => handleSelectService(1)}
                  >
                    Edit Customizations (
                    {services?.customizations_array?.length || 0})
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {isTrue ? (
        renderContent()
      ) : (
        <EditCustomizationService
          onSelectService={() => handleSelectService(1)}
          serviceId={id}
          customizations={services?.customizations_array || []}
          currencySymbol={currencySymbol}
          serviceCurrency={formData.currency}
          categories={categories}
        />
      )}
    </>
  );
};

export default EditService;
