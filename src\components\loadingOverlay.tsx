import React, { useEffect, useState } from "react";
import Image from "next/image";

const logos = [
  "/assets/logo/Default.png",
  "/assets/logo/Yellow.png",
  "/assets/logo/Red.png",
  "/assets/logo/Blue.png",
  "/assets/logo/green.png",
  "/assets/logo/Purple.png",
  "/assets/logo/Violet.png",
]; // Replace with actual paths

const LoadingOverlay = ({ isLoading }: { isLoading: boolean }) => {
  const [logoIndex, setLogoIndex] = useState(0);

  useEffect(() => {
    if (!isLoading) return;

    const interval = setInterval(() => {
      setLogoIndex((prevIndex) => (prevIndex + 1) % logos.length);
    }, 250); // Change image every 0.5 seconds

    return () => clearInterval(interval);
  }, [isLoading]);

  if (!isLoading) return null;

  return (
    <div className="absolute inset-0 bg-white/85  flex items-center justify-center z-50">
      {/* Wrapper to prevent blur on content */}
      <div className="flex flex-col items-center justify-center bg-transparent p-6 rounded-lg">
        {/* Rotating Logos */}
        <div className="w-24 h-24 mb-4">
          <Image
            src={logos[logoIndex]}
            alt="Loading Logo"
            width={96}
            height={96}
            className="animate-fade-in"
          />
        </div>

        {/* Shimmering AMZUN Text */}
        <div className="text-2xl font-sf font-bold text-gray-800 animate-shimmer">
          AMUZN
        </div>
      </div>

      {/* Tailwind CSS animations */}
      <style jsx>{`
        @keyframes shimmer {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 1;
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite ease-in-out;
        }

        @keyframes fade-in {
          0% {
            opacity: 1;
            transform: scale(0.9);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default LoadingOverlay;
