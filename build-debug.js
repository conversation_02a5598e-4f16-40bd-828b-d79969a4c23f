const { execSync } = require('child_process');
const fs = require('fs');

try {
  console.log('Starting Next.js build...');
  
  // Set environment variables
  process.env.NODE_OPTIONS = '--trace-warnings';
  
  // Run the build command and capture output
  const output = execSync('npx next build', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  // Write output to file
  fs.writeFileSync('build-output.txt', output);
  
  console.log('Build completed successfully!');
  console.log('Output saved to build-output.txt');
  
} catch (error) {
  console.error('Build failed with error:');
  console.error(error.message);
  
  // Write error output to file
  fs.writeFileSync('build-error.txt', error.message + '\n\n' + (error.stdout || '') + '\n\n' + (error.stderr || ''));
  
  console.log('Error details saved to build-error.txt');
}
