// services/chatService.ts

import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  Timestamp,
  query,
  where,
  serverTimestamp,
  setDoc,
  orderBy,
  onSnapshot,
  limit,
  or,
  getDoc,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { ActivityLog } from "./ordersServices";

const CHAT_COLLECTION = "chats"; // Name of your Firestore chat collection

// Interface for a chat message
export interface Chat {
  id?: string;
  fromProfile: string;
  fromProfileName: string;
  toProfile: string;
  toProfileName: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

export interface Messages {
  chatId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  message: string;
  senderId: string;
  senderName: string;
  recipientId: string;
  recipientName: string;
  seen: false;

  activityLog: ActivityLog;
}

export type ChatSummaryDetails = {
  data: ChatSummary[];
  totalUnreadCount: number;
};

export type ChatSummary = {
  chatId: string;
  chat: Chat;
  messages: Messages[];
  unreadCount: number;
  profile_detail:Array<{
    user_id:string;
    profile_pic:string;
  }>;
  profile_pic:string;
};
///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// /////

export class ChatManager {
  private CHAT_COLLECTION = "chats";
  private MESSAGE_SUB_COLLECTION = "messages";

  static instance: ChatManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new ChatManager();
    }
    return this.instance;
  }

  async CreateChatIfNotExists({
    fromProfile,
    fromProfileName,
    toProfile,
    toProfileName,
  }: {
    fromProfile: string;
    fromProfileName: string;
    toProfile: string;
    toProfileName: string;
  }): Promise<string> {
    const { db } = await initFirebase();

    const chatRef = collection(db, this.CHAT_COLLECTION);

    // Check for existing chat (bi-directional match)
    const chatQuery = query(
      chatRef,
      where("fromProfile", "in", [fromProfile, toProfile]),
      where("toProfile", "in", [fromProfile, toProfile])
    );

    const snapshot = await getDocs(chatQuery);
    const existing = snapshot.docs.find((doc) => {
      const data = doc.data();
      return (
        (data.fromProfile === fromProfile && data.toProfile === toProfile) ||
        (data.fromProfile === toProfile && data.toProfile === fromProfile)
      );
    });

    if (existing) {
      return existing.id;
    }

    // Create new chat
    const newChat = {
      fromProfile,
      fromProfileName,
      toProfile,
      toProfileName,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(chatRef, newChat);
    return docRef.id;
  }

  async SendMessage({
    chatId,
    message,
    senderId,
    senderName,
    recipientId,
    recipientName,
    activityLog,
  }: {
    chatId?: string;
    message: string;
    senderId: string;
    senderName: string;
    recipientId: string;
    recipientName: string;
    activityLog?: any;
  }) {
    const { db } = await initFirebase();
    let messagesRef: any;

    if (senderId === recipientId) return;

    if (!chatId) {
      chatId = await this.CreateChatIfNotExists({
        fromProfile: senderId,
        fromProfileName: senderName,
        toProfile: recipientId,
        toProfileName: recipientName,
      });
    }
    console.log({ chatId });

    messagesRef = collection(db, this.CHAT_COLLECTION, chatId, this.MESSAGE_SUB_COLLECTION);

    let messagePayload: any = {
      chatId,
      message,
      senderId,
      senderName,
      recipientId,
      recipientName,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      seen: false,
    };
    if (activityLog) {
      messagePayload.activityLog = activityLog;
    }
    console.log({ messagePayload });

    await addDoc(messagesRef, messagePayload);

    // Optionally update chat.updatedAt
    await setDoc(
      doc(db, "chats", chatId),
      {
        updatedAt: serverTimestamp(),
      },
      { merge: true }
    );
  }

  async GetMessagesBetweenUsers({
    userA,
    userB,
    onUpdate,
  }: {
    userA: string;
    userB: string;
    onUpdate: (messages: any[]) => void;
  }) {
    const { db } = await initFirebase();
    // Step 1: Find the chat doc between userA and userB (bi-directional match)
    const chatsRef = collection(db, this.CHAT_COLLECTION);

    const chatQuery = query(
      chatsRef,
      where("fromProfile", "in", [userA, userB]),
      where("toProfile", "in", [userA, userB])
    );
    // Return an async unsubscribe function
    let unsubscribe: () => void;

    (async () => {
      const snap = await getDocs(chatQuery);
      let chatDoc = snap.docs.find((doc) => {
        const d = doc.data();
        return (
          (d.fromProfile === userA && d.toProfile === userB) ||
          (d.fromProfile === userB && d.toProfile === userA)
        );
      });

      if (!chatDoc) {
        return [];
      }

      const messagesRef = collection(db, "chats", chatDoc.id, this.MESSAGE_SUB_COLLECTION);

      const messagesQuery = query(messagesRef, orderBy("createdAt", "asc"));

      unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
        const messages = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        onUpdate(messages);
      });
    })();

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }

  async UpdateSeenFlag({
    msgs,
    currentUserId,
    chatId,
  }: {
    msgs: Messages[];
    currentUserId: string;
    chatId: string;
  }) {
    try {
      const { db } = await initFirebase();

      const unseenMessages = msgs.filter((msg) => msg.recipientId === currentUserId && !msg.seen);

      for (const msg of unseenMessages) {
        // @ts-ignore
        const msgRef = doc(db, this.CHAT_COLLECTION, chatId, this.MESSAGE_SUB_COLLECTION, msg.id);
        await updateDoc(msgRef, {
          seen: true,
          seenAt: serverTimestamp(),
        });
      }
    } catch (error) {
      throw new Error("update_seen_flag_failed");
    }
  }

  async GetUnreadMessagesCountByChatId({ chatId, user_id }: { user_id: string; chatId: string }) {
    try {
      const { db } = await initFirebase();

      const messagesRef = collection(db, this.CHAT_COLLECTION, chatId, this.MESSAGE_SUB_COLLECTION);

      const unreadQuery = query(
        messagesRef,
        where("recipientId", "==", user_id),
        where("seen", "==", false)
      );

      const snap = await getDocs(unreadQuery);

      return snap.size; // number of unread messages
    } catch (error) {
      throw new Error("GetUnreadMessagesCountFailed");
    }
  }

  async GetUserChatSummaries({ user_id }: { user_id: string }): Promise<ChatSummaryDetails> {
    try {
      const { db } = await initFirebase();

      // Step 1: Get all chat documents where user is involved
      const chatsRef = collection(db, this.CHAT_COLLECTION);
      const chatQuery = query(
        chatsRef,
        or(where("fromProfile", "==", user_id), where("toProfile", "==", user_id)) , 
        orderBy("updatedAt","desc")
      );

      const chatSnap = await getDocs(chatQuery);

      const results = await Promise.all(
        chatSnap.docs.map(async (chatDoc) => {
          const chatData = chatDoc.data();
          const chatId = chatDoc.id;

          // Step 2: Get latest messages (limit to last 20 for performance)
          const messagesRef = collection(db, this.CHAT_COLLECTION, chatId, this.MESSAGE_SUB_COLLECTION);
          const messagesQuery = query(messagesRef, orderBy("createdAt", "desc"), limit(20));

          const messageSnap = await getDocs(messagesQuery);
          const messages: any = messageSnap.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }));

          // Step 3: Count unread messages where user is recipient and seen is false
          const unreadQuery = query(
            messagesRef,
            where("recipientId", "==", user_id),
            where("seen", "==", false)
          );
          const unreadSnap = await getDocs(unreadQuery);
          // Step 3: Get profile pics of both users
        const userIds = [chatData.fromProfile, chatData.toProfile];
        const userDocs = await Promise.all(
          userIds.map((uid) => getDoc(doc(db, "users", uid)))
        );

        const profile_detail = userDocs.map((docSnap, index) => {
          const fallbackId = userIds[index];
          if (!docSnap.exists()) return { user_id: fallbackId, profile_pic: "" };

          const data = docSnap.data();
          return {
            user_id: data.id ?? fallbackId,
            profile_pic: data.avatar ?? "",
          };
        });          
          
        
          //@ts-ignore
          chatData.profile_pic = profile_detail?.filter((current)=>current.user_id !== user_id)?.map((curr)=>curr.profile_pic)?.[0];

          return {
            chatId,
            chat: chatData,
            messages: messages.reverse(), // show oldest first
            unreadCount: unreadSnap.size,
            profile_detail,
          } as ChatSummary;
        })
      );

      let totalUnreadCount: number = 0;
      results?.forEach((current) => (totalUnreadCount += current.unreadCount));

      return {
        data: results,
        totalUnreadCount,
      };
    } catch (error) {
      console.log({error});
      // throw new Error("GetUserChatSummariesFailed");
      return {
        data: [],
        totalUnreadCount:0,
      };
    }
  }

  // Add a real-time listener for user chat summaries
  ListenToUserChatSummaries({ user_id, onUpdate }: { user_id: string, onUpdate: (chatList: any[]) => void }) {
    let unsubChats: (() => void) | undefined;
    let unsubMessages: { [chatId: string]: () => void } = {};
    let chatListMap = new Map();
    (async () => {
      const { db } = await initFirebase();
      const chatsRef = collection(db, this.CHAT_COLLECTION);
      const chatQuery = query(
        chatsRef,
        or(where('fromProfile', '==', user_id), where('toProfile', '==', user_id)),
        orderBy('updatedAt', 'desc')
      );
      unsubChats = onSnapshot(chatQuery, async (chatSnap) => {
        // Clean up old message listeners
        Object.values(unsubMessages).forEach(unsub => unsub());
        unsubMessages = {};
        chatListMap.clear();
        // Prepare all chat entries in parallel
        const chatPromises = chatSnap.docs.map(async (chatDoc) => {
          const chat = chatDoc.data();
          const chatId = chatDoc.id;
          const otherUser = user_id === chat.fromProfile
            ? { id: chat.toProfile, name: chat.toProfileName }
            : { id: chat.fromProfile, name: chat.fromProfileName };
          // Fetch avatar
          const userDocRef = doc(db, 'users', otherUser.id);
          const userDocSnap = await getDoc(userDocRef);
          const otherUserAvatar = userDocSnap.exists() ? userDocSnap.data().avatar || '' : '';
          // Prepare chat entry
          const chatEntry = {
            chatId,
            imgUrl: otherUserAvatar,
            colorVal: 'transparent',
            userName: otherUser.name,
            text: '',
            time: null,
            unread: 0,
            chat: chat,
            messages: [],
          };
          chatListMap.set(chatId, chatEntry);
          // Set up real-time listener for messages
          const messagesRef = collection(db, 'chats', chatId, 'messages');
          const messagesQuery = query(messagesRef, orderBy('createdAt', 'desc'), limit(20));
          unsubMessages[chatId] = onSnapshot(messagesQuery, (messagesSnap) => {
            const messages = messagesSnap.docs.map(doc => doc.data());
            const lastMessageDoc = messagesSnap.docs[0];
            const lastMessage = lastMessageDoc && typeof lastMessageDoc.data === 'function' ? { id: lastMessageDoc.id, ...lastMessageDoc.data() } : null;
            const unreadCount = messages.filter(msg => msg.recipientId === user_id && !msg.seen).length;
            // Only update the relevant chat entry in the map
            const entry = chatListMap.get(chatId);
            if (entry) {
              entry.text = lastMessage && 'message' in lastMessage ? lastMessage.message : '';
              entry.time = lastMessage && 'createdAt' in lastMessage ? lastMessage.createdAt : null;
              entry.unread = unreadCount;
              entry.messages = lastMessage ? [lastMessage] : [];
              // After updating, send the full list (sorted) to onUpdate
              const sortedList = Array.from(chatListMap.values()).sort((a, b) => (b.time?.toMillis?.() || 0) - (a.time?.toMillis?.() || 0));
              onUpdate([...sortedList]);
            }
          });
        });
        // Wait for all avatars to be fetched before first render
        await Promise.all(chatPromises);
        // Initial update (all chats, but messages may not be loaded yet)
        const sortedList = Array.from(chatListMap.values()).sort((a, b) => (b.time?.toMillis?.() || 0) - (a.time?.toMillis?.() || 0));
        onUpdate([...sortedList]);
      });
    })();
    return () => {
      if (unsubChats) unsubChats();
      Object.values(unsubMessages).forEach(unsub => unsub());
    };
  }
}
