import React from 'react';
import { cn } from "@/lib/utils";


interface SideSheetContainerProps extends React.HTMLAttributes<HTMLDivElement> {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

const SideSheetContainer: React.FC<SideSheetContainerProps> = ({
    className,
    open,
    onOpenChange,
    ...props
}) => (
    <div
        className={cn("flex flex-col items-center custom-overlay-chat max-w-[26.25rem]", className)}
        {...props}
    />
);
SideSheetContainer.displayName = "SideSheetContainer";

interface SideSheetHeaderProps extends React.HTMLAttributes<HTMLDivElement> { }

const SideSheetHeader: React.FC<SideSheetHeaderProps> = ({
    className,
    ...props
}) => (
    <div
        className={cn(
            "row justify-between py-4 z-50 bg-white absolute right-0 w-full",
            className
        )}
        {...props}
    />
);
SideSheetHeader.displayName = "SideSheetHeader";

interface SideSheetDescriptionProps extends React.HTMLAttributes<HTMLDivElement> { }

const SideSheetDescription: React.FC<SideSheetDescriptionProps> = ({
    className,
    ...props
}) => (
    <div
        className={cn(
            "flex mx-5 item-center justify-center mt-14 h-full pb-12 bg-white",
            className
        )}
        {...props}
    />
);
SideSheetDescription.displayName = "SideSheetDescription";

export { SideSheetContainer, SideSheetHeader, SideSheetDescription };
