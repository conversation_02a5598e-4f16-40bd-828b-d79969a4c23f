import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeAccountLinkRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      accountId,
      refreshUrl,
      returnUrl,
      type,
      isUS
    }: StripeAccountLinkRequest = req.body;

    if (!accountId || !refreshUrl || !returnUrl || !type) {
      return res.status(400).json({ 
        error: 'Account ID, refresh URL, return URL, and type are required' 
      });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const accountLink = await stripeService.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: type as 'account_onboarding' | 'account_update'
    });

    res.status(200).json({
      accountLink,
      success: true,
    });

  } catch (error) {
    console.error('Error creating account link:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
