"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";

interface GoogleMapsContextType {
  isLoaded: boolean;
  loadError: Error | null;
  placesService: google.maps.places.AutocompleteService | null;
}

const GoogleMapsContext = createContext<GoogleMapsContextType>({
  isLoaded: false,
  loadError: null,
  placesService: null,
});

export const useGoogleMaps = () => useContext(GoogleMapsContext);

interface GoogleMapsProviderProps {
  children: ReactNode;
  apiKey?: string;
}

export const GoogleMapsProvider: React.FC<GoogleMapsProviderProps> = ({
  children,
  apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);
  const [placesService, setPlacesService] = useState<google.maps.places.AutocompleteService | null>(null);

  useEffect(() => {
    // Check if the API is already loaded
    if (window.google && window.google.maps && window.google.maps.places) {
      setIsLoaded(true);
      setPlacesService(new window.google.maps.places.AutocompleteService());
      return;
    }

    // Prevent loading the script multiple times
    if (document.getElementById("google-maps-script")) {
      return;
    }

    const loadGoogleMapsAPI = () => {
      try {
        const script = document.createElement("script");
        script.id = "google-maps-script";
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        
        script.onload = () => {
          setIsLoaded(true);
          if (window.google && window.google.maps && window.google.maps.places) {
            setPlacesService(new window.google.maps.places.AutocompleteService());
          }
        };
        
        script.onerror = (error) => {
          setLoadError(new Error("Failed to load Google Maps API"));
          console.error("Google Maps API failed to load", error);
        };
        
        document.head.appendChild(script);
      } catch (error) {
        setLoadError(error instanceof Error ? error : new Error(String(error)));
        console.error("Error loading Google Maps API:", error);
      }
    };

    loadGoogleMapsAPI();

    // Cleanup function
    return () => {
      // We don't remove the script on unmount as it might be needed by other components
    };
  }, [apiKey]);

  return (
    <GoogleMapsContext.Provider value={{ isLoaded, loadError, placesService }}>
      {children}
    </GoogleMapsContext.Provider>
  );
};

export default GoogleMapsProvider;
