import { useState } from "react";
import Image from "next/image";
import { File, FilePlus, X } from "react-feather";

interface FileItem {
  id: string;
  file: File;
}

export default function FileUploader() {
  const [files, setFiles] = useState<FileItem[]>([]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (selectedFiles) {
      const newFiles: FileItem[] = Array.from(selectedFiles).map((file) => ({
        id: `${file.name}-${file.lastModified}`,
        file,
      }));

      // Check file size limits (4MB for images, 8MB for videos)
      const validFiles = newFiles.filter((fileItem) => {
        if (fileItem.file.type.startsWith("image/")) {
          return fileItem.file.size <= 4 * 1024 * 1024;
        } else if (fileItem.file.type.startsWith("video/")) {
          return fileItem.file.size <= 8 * 1024 * 1024;
        }
        return false;
      });

      setFiles((prevFiles) => [...prevFiles, ...validFiles]);
    }
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles((prevFiles) => prevFiles.filter((file) => file.id !== fileId));
  };

  return (
    <div className="p-0">
      <div className="row gap-3 mb-5 mt-3 items-center">
        <label className="relative block max-md:mb-4 p-4 rounded-lg text-center cursor-pointer bg-iconColor text-white">
          <input
            type="file"
            multiple
            onChange={handleFileChange}
            className="absolute inset-0 opacity-0 cursor-pointer"
          />
          <FilePlus />
        </label>
        <div className="pointer-events-none ">
          <p className="text-subtitle text-xs">File size max 4MB per photo</p>
          <p className="text-subtitle text-xs">
            File size max 8MB / 15 seconds per video
          </p>
        </div>
      </div>

      {/* Display selected files */}
      <div className="grid grid-cols-2 gap-4">
        {files.map((fileItem) => (
          <div
            key={fileItem.id}
            className="relative flex items-center p-3 bg-titleLabel min-h-14 rounded-xl text-white text-sm"
          >
            <div className="row gap-2">
              <div className="bg-white p-2 rounded-full">
                <File className="text-primary" size={18} />
              </div>
              <span className=" line-clamp-2 ">{fileItem.file.name}</span>
            </div>

            <button
              onClick={() => handleDeleteFile(fileItem.id)}
              className="absolute top-0 right-0 w-6 h-6 border-2 border-[#C4C4C4]  transform translate-x-1/2 -translate-y-1/2 p-1 bg-white rounded-full text-black row justify-center"
            >
              <X size={18} className="text-primary" strokeWidth="4px" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
