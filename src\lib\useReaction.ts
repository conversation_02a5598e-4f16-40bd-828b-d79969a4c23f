import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import {
  PostReactionType,
  useAddReactionMutation,
  useUndoReactionMutation,
} from "@/graphql/test/generated";
import { useAccount } from "wagmi";

export function useReaction() {
  const { mutateAsync: like_requestTypedData } = useAddReactionMutation();
  const { mutateAsync: dislike_requestTypedData } = useUndoReactionMutation();

  const { address , isConnected } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();

  async function reaction(
    forId: string,
    reaction: PostReactionType,
    type: "like" | "dislike"
  ) {
    try {
     if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      // await loginUser();

      // console.log("✅ login successfully");

      let typedData = null;
      if (type === "like") {
        typedData = await like_requestTypedData({
          request: {
            post: forId,
            reaction: PostReactionType.Upvote,
            // for: forId,
            // reaction: PublicationReactionType.Upvote,
          },
        });
        return typedData.addReaction;
      } else {
        typedData = await dislike_requestTypedData({
          request: {
            post: forId,
            reaction: PostReactionType.Upvote,
            // for: forId,
            // reaction: PublicationReactionType.Upvote,
          },
        });
        return typedData.undoReaction;
      }
    } catch (error) {
      // console.error("Follow error:", error);
      throw error;
    }
  }

  return reaction;
}
