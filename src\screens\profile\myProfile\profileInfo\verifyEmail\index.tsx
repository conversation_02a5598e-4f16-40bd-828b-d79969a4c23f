"use client";
import { MoreVertical } from "react-feather";
import { useEffect, useState } from "react";
import useAuth from "@/hook";
// import useProfile from "@/hook/profileData";
import {
  isEmailVerified,
  refreshUser,
  sendVerificationEmail,
} from "@/services/usersServices";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import SendVerificationModal from "@/components/modals/SendVerificationModal";
const ProfileInfoVerifyEmail = (props: any) => {
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // fetch data from firebase
  const auth = useAuth(); // Used for authentication context

  const maskEmail = (email: string): string => {
    if (!email || !email.includes("@")) return email;

    const [name] = email.split("@");

    // Keep first 3 characters of name
    const visibleName = name.substring(0, 3);

    // Use exactly 4 dots
    const maskedName = "....";

    // Get last character of name
    const lastChar = name.charAt(name.length - 1);

    // Format as "tes....l.com"
    return `${visibleName}${maskedName}${lastChar}.com`;
  };

  const sendVerificationCode = async () => {
    setIsLoading(true);
    try {
      const response = await sendVerificationEmail();
      setIsLoading(false);
      return response.success;
    } catch (error) {
      console.error("Error sending verification code:", error);
      setIsLoading(false);
      return false;
    }
  };

  const handleOpenVerificationModal = () => {
    setIsVerificationModalOpen(true);
    setIsDropdownOpen(false); // Close the dropdown when opening the modal
  };

  const checkVerification = async () => {
    await refreshUser(); // Refresh user info before checking
    const response = isEmailVerified();
    if (response?.verified) {
      setIsVerified(true);
    }
  };

  useEffect(() => {
    checkVerification();
  }, []);

  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">
                {!isVerified ? "Email Not Verified" : "Email Verified"}
              </span>

              <p className="text-primary max-md:text-sm">
                {maskEmail(props?.email)}
              </p>
            </div>
            {!props.isOtherProfile && !isVerified && (
              <div className="cursor-pointer">
                <DropdownMenu
                  open={isDropdownOpen}
                  onOpenChange={setIsDropdownOpen}
                >
                  <DropdownMenuTrigger asChild>
                    <MoreVertical className="max-md:h-[20px]" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-60 rounded-3xl">
                    <DropdownMenuLabel
                      className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                      onClick={handleOpenVerificationModal}
                    >
                      Send verification code
                    </DropdownMenuLabel>
                    <DropdownMenuLabel
                      className="text-center font-normal text-base cursor-pointer"
                      onClick={() => setIsDropdownOpen(false)}
                    >
                      Close
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Verification Code Modal */}
      <SendVerificationModal
        isOpen={isVerificationModalOpen}
        onClose={() => setIsVerificationModalOpen(false)}
        onSend={sendVerificationCode}
        email={props?.email}
      />
    </>
  );
};

export default ProfileInfoVerifyEmail;
