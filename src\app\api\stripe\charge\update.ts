import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeUpdateChargeRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      paymentId,
      orderId,
      description,
      accountId,
      isUS
    }: StripeUpdateChargeRequest = req.body;

    if (!paymentId || !accountId) {
      return res.status(400).json({ 
        error: 'Payment ID and account ID are required' 
      });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const charge = await stripeService.charges.update(
      paymentId,
      {
        metadata: { order_id: orderId },
        description
      },
      { stripeAccount: accountId }
    );

    res.status(200).json({
      charge,
      success: true,
    });

  } catch (error) {
    console.error('Error updating charge:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
