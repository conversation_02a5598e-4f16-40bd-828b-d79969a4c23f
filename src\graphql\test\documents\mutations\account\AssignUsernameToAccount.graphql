mutation AssignUsernameToAccount($request: AssignUsernameToAccountRequest!) {
  assignUsernameToAccount(request: $request) {
    ... on AssignUsernameResponse {
      hash
    }
    ... on SelfFundedTransactionRequest {
      ...SelfFundedTransactionRequest
    }
    ... on SponsoredTransactionRequest {
      ...SponsoredTransactionRequest
    }
    ... on TransactionWillFail {
      ...TransactionWillFail
    }
  }
}
