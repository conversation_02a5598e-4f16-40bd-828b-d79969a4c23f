import { themes } from "../../../../theme";
import React, { useEffect, useState, useMemo, useCallback, useRef } from "react";
import Link from "next/link";
import BrowseLensUserCard from "./browseLensUserCard";
import { useFollow } from "@/lib/useFollow";
import { useUnFollow } from "@/lib/useUnfollow";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperType } from "swiper/types";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { AccountsBulkQuery, PostsQuery } from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>ogHeader,
  AlertDialog<PERSON>rigger,
} from "@/components/ui/alert-dialog";

import { X } from "react-feather";
import { useAccount } from "wagmi";
import LazyMedia from "@/components/LazyMedia";
import { useSignInStore } from "@/components/GlobalSignInButton";

type Status = "follow" | "unfollow";

const BrowseLensUser = ({
  postData,
  userData: profileData,
  postId,
  loadNextPage,
  categoryName,
  isLens,
  userPost,
  refetch,
}: {
  postData: PostsQuery["posts"]["items"];
  postId: string;
  loadNextPage: any;
  categoryName: any;
  isLens: boolean;
  userData: AccountsBulkQuery["accountsBulk"][0];
  userPost: PostsQuery | undefined;
  refetch: any;
}) => {
  const length = postData?.length;
  const txnIdRef = useRef<string | null>(null);
  const [currentStatus, setCurrentStatus] = useState<Status>("follow");
  const swiperRef = useRef<SwiperType | null>(null);
  const [isAlreadySlided, setAlreadySlided] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingUserId, setLoadingUserId] = useState<string | null>(null);

  const unfollow = useUnFollow();
  const { mutateAsync: followUser, isSuccess, data: followData } = useFollow();

  const profileImage = useMemo(
    () => profileData?.metadata?.picture || "/assets/profileAvatar.svg",
    [profileData]
  );
  const { address } = useAccount();
  const [isSigninOpen, setIsSigninOpen] = useState(false);

  // Add a local state to track follow status
  const [localFollowStatuses, setLocalFollowStatuses] = useState<Record<string, boolean>>({});

  // Initialize from profileData when it loads
  useEffect(() => {
    if (profileData?.operations) {
      setLocalFollowStatuses((prev) => ({
        ...prev,
        [profileData.address]: profileData.operations?.isFollowedByMe,
      }));
    }
  }, [profileData]);

  // Slide to specific post
  useEffect(() => {
    if (swiperRef.current && postId && !isAlreadySlided) {
      const postIndex = postData.findIndex((post: any) => post.id === postId);
      if (postIndex !== -1) {
        setAlreadySlided(true);
        const slidesPerView = swiperRef.current.params.slidesPerView as number;
        const centerIndex = Math.max(postIndex - Math.floor(slidesPerView / 4), 0);
        swiperRef.current.slideTo(centerIndex);
      }
    }
  }, [postId, postData]);

  const [currentIndex, setCurrentIndex] = useState({
    isStart: true,
    isEnd: false,
  });

  useEffect(() => {
    if (swiperRef.current) {
      setCurrentIndex({
        isStart: swiperRef.current.activeIndex === 0,
        isEnd: swiperRef.current.isEnd,
      });
    }
  }, [length]);

  const handleSlideChange = () => {
    if (swiperRef.current) {
      setCurrentIndex({
        isStart: swiperRef.current.activeIndex === 0,
        isEnd: swiperRef.current.isEnd,
      });
      if (swiperRef.current && swiperRef.current.isEnd) {
        loadNextPage();
      }
    }
  };

  const buttonStyles = {
    fontSize: "14px",
    height: "25px",
    width: "80px",
    borderRadius: "20px",
    padding: "0",
    color: "white",
  };

  const shouldShowNextButton = () => {
    if (window.innerWidth >= 1280) return length > 4;
    if (window.innerWidth >= 1024) return length > 3;
    if (window.innerWidth >= 768) return length > 2;
    return false;
  };

  const buttons = document.querySelectorAll(".tw-connect-wallet");
  useEffect(() => {
    buttons.forEach((button) => {
      const btn = button as HTMLElement;
      btn.style.setProperty("background-color", "#333333", "important");
      btn.style.setProperty("color", "white", "important");
      btn.style.setProperty("height", "25px", "important");
      btn.style.setProperty("cursor", "pointer", "important");
      btn.style.setProperty("justify-content", "center", "important");
      btn.style.setProperty("border-radius", "9999px", "important");
      btn.style.setProperty("border-color", "rgb(51 51 51 / 1)", "important");
      btn.style.setProperty("padding-left", "1.5rem", "important");
      btn.style.setProperty("padding-right", "1.5rem", "important");
      btn.style.setProperty("font-size", "14px", "important");
      btn.style.setProperty("font-weight", "500", "important");
      btn.style.setProperty("display", "flex", "important");
      btn.style.setProperty("flex-direction", "row", "important");
      btn.style.setProperty("align-items", "center", "important");
      btn.style.setProperty("min-width", "82px", "important");
      btn.innerText = "Follow";
    });
  }, [buttons]);

  return (
    <>
      <div>
        {length > 0 && (
          <>
            {Object.entries(themes).map(([themeName, themeProperties]) => {
              if (themeProperties.title !== categoryName) return null;
              return (
                <div key={themeName}>
                  <div className="flex flex-col relative overflow-x-hidden">
                    <div className="row justify-between sticky left-0 max-md:hidden">
                      <div className="row mb-3 gap-3 justify-between min-w-[360px]">
                        <div className="row gap-2">
                          <Link href={`/profile/lens/${profileData.username?.localName}`}>
                            <LazyMedia
                              src={
                                sanitizeDStorageUrl(profileData?.metadata?.picture) ||
                                "/assets/profileAvatar.svg"
                              }
                              alt="Profile Avatar"
                              type="image"
                              className="w-[40px] h-[40px] rounded-full object-cover"
                              placeholderClassName="bg-gray-200 rounded-full"
                            />
                          </Link>
                          <div>
                            <Link href={`/profile/lens/${profileData.username?.localName}`}>
                              <p className="font-bold text-nowrap">
                                {profileData.metadata?.name || "Profile Name"}
                              </p>
                            </Link>
                            <p className="text-[#616770] -mt-1">
                              {getLocation(profileData.metadata?.attributes) ||
                                profileData.username?.localName}
                            </p>
                          </div>
                        </div>

                        <div className="row gap-3">
                          {isLens && (
                            <LazyMedia
                              src="/assets/lens.png"
                              alt="Lens Logo"
                              type="image"
                              className="h-7 w-9"
                            />
                          )}
                          {address ? (
                            <div>
                              {!(
                                localFollowStatuses[profileData.address] ??
                                profileData?.operations?.isFollowedByMe
                              ) ? (
                                <Badge
                                  className={`btn-xs text-white min-w-20 w-20 ${
                                    isLoading && loadingUserId === profileData.address
                                      ? "opacity-70 pointer-events-none"
                                      : ""
                                  }`}
                                  onClick={async () => {
                                    if (isLoading && loadingUserId === profileData.address) return;
                                    try {
                                      setIsLoading(true);
                                      setLoadingUserId(profileData.address);
                                      await followUser(profileData.address);
                                      // Update local state immediately
                                      setLocalFollowStatuses((prev) => ({
                                        ...prev,
                                        [profileData.address]: true,
                                      }));
                                      refetch(); // Refetch to update server state
                                    } catch (error) {
                                      console.error("Follow failed:", error);
                                    } finally {
                                      setIsLoading(false);
                                      setLoadingUserId(null);
                                    }
                                  }}
                                >
                                  {isLoading && loadingUserId === profileData.address ? (
                                    <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                                  ) : (
                                    "Follow"
                                  )}
                                </Badge>
                              ) : (
                                <Badge
                                  className={`btn-xs font-normal font-sf border-primary btn min-w-20 w-20 ${
                                    isLoading && loadingUserId === profileData.address
                                      ? "opacity-70 pointer-events-none"
                                      : ""
                                  }`}
                                  variant="outline"
                                  onClick={async () => {
                                    if (isLoading && loadingUserId === profileData.address) return;
                                    try {
                                      setIsLoading(true);
                                      setLoadingUserId(profileData.address);
                                      const resp = await unfollow(profileData.address);
                                      // Update local state immediately
                                      setLocalFollowStatuses((prev) => ({
                                        ...prev,
                                        [profileData.address]: false,
                                      }));
                                      refetch(); // Refetch to update server state
                                    } catch (error) {
                                      console.error("Unfollow failed:", error);
                                    } finally {
                                      setIsLoading(false);
                                      setLoadingUserId(null);
                                    }
                                  }}
                                  style={{ fontWeight: 400 }}
                                >
                                  {isLoading && loadingUserId === profileData.address ? (
                                    <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                                  ) : (
                                    "Unfollow"
                                  )}
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <Badge
                              className="btn-xs font-normal font-sf text-white min-w-20 w-20"
                              onClick={() => {
                                // Use the global sign-in store to open the wallet connect modal
                                useSignInStore.getState().setIsOpen(true);
                              }}
                            >
                              Follow
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="row justify-between sticky left-0 md:hidden">
                      <div className="row mb-3 gap-3">
                        <div className="row gap-2">
                          <div>
                            <Link href={`/profile/lens/${profileData.username?.localName}`}>
                              <div>
                                <LazyMedia
                                  src={
                                    sanitizeDStorageUrl(profileData?.metadata?.picture) ||
                                    "/assets/profileAvatar.svg"
                                  }
                                  alt="Profile Avatar"
                                  type="image"
                                  className="w-[40px] h-[40px] rounded-full object-cover"
                                  placeholderClassName="bg-gray-200 rounded-full"
                                />
                              </div>
                            </Link>
                          </div>
                          <div>
                            <Link href={`/profile/lens/${profileData.username?.localName}`}>
                              <p className="font-bold text-nowrap">
                                {profileData.metadata?.name || "Profile Name"}
                              </p>
                            </Link>
                            <p className="text-[#616770] -mt-1">
                              {" "}
                              {getLocation(profileData.metadata?.attributes) ||
                                profileData.username?.localName}
                            </p>
                          </div>
                        </div>
                        {address ? (
                          <div>
                            {!(
                              localFollowStatuses[profileData.address] ??
                              profileData?.operations?.isFollowedByMe
                            ) ? (
                              <Badge
                                className={`btn-xs text-white min-w-20 w-20 ${
                                  isLoading && loadingUserId === profileData.address
                                    ? "opacity-70 pointer-events-none"
                                    : ""
                                }`}
                                onClick={async () => {
                                  if (isLoading && loadingUserId === profileData.address) return;
                                  try {
                                    setIsLoading(true);
                                    setLoadingUserId(profileData.address);
                                    await followUser(profileData.address);
                                    // Update local state immediately
                                    setLocalFollowStatuses((prev) => ({
                                      ...prev,
                                      [profileData.address]: true,
                                    }));
                                    refetch(); // Refetch to update server state
                                  } catch (error) {
                                    console.error("Follow failed:", error);
                                  } finally {
                                    setIsLoading(false);
                                    setLoadingUserId(null);
                                  }
                                }}
                              >
                                {isLoading && loadingUserId === profileData.address ? (
                                  <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                                ) : (
                                  "Follow"
                                )}
                              </Badge>
                            ) : (
                              <Badge
                                className={`btn-xs font-normal font-sf border-primary btn min-w-20 w-20 ${
                                  isLoading && loadingUserId === profileData.address
                                    ? "opacity-70 pointer-events-none"
                                    : ""
                                }`}
                                variant="outline"
                                onClick={async () => {
                                  if (isLoading && loadingUserId === profileData.address) return;
                                  try {
                                    setIsLoading(true);
                                    setLoadingUserId(profileData.address);
                                    const resp = await unfollow(profileData.address);
                                    // Update local state immediately
                                    setLocalFollowStatuses((prev) => ({
                                      ...prev,
                                      [profileData.address]: false,
                                    }));
                                    refetch(); // Refetch to update server state
                                  } catch (error) {
                                    console.error("Unfollow failed:", error);
                                  } finally {
                                    setIsLoading(false);
                                    setLoadingUserId(null);
                                  }
                                }}
                                style={{ fontWeight: 400 }}
                              >
                                {isLoading && loadingUserId === profileData.address ? (
                                  <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                                ) : (
                                  "Unfollow"
                                )}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <Badge
                            className="btn-xs font-normal font-sf text-white min-w-20 w-20"
                            onClick={() => {
                              // Use the global sign-in store to open the wallet connect modal
                              useSignInStore.getState().setIsOpen(true);
                            }}
                          >
                            Follow
                          </Badge>
                        )}
                      </div>
                      <div className=" row gap-3">
                        <LazyMedia
                          src="/assets/lens.png"
                          alt="Lens Logo"
                          type="image"
                          className="h-7 w-9"
                        />
                      </div>
                    </div>

                    <div
                      className={`absolute top-0 right-0 bottom-0 w-[80px] bg-gradient-to-l ${
                        !currentIndex.isEnd && shouldShowNextButton() ? "from-white" : ""
                      } pointer-events-none max-md:hidden z-50`}
                    />
                    <div
                      className={`absolute top-12 left-0 bottom-0  w-[80px] bg-gradient-to-r ${
                        !currentIndex.isStart ? "from-white" : ""
                      } pointer-events-none max-md:hidden z-50`}
                    />
                    <div className="relative w-full">
                      <Swiper
                        modules={[Navigation]}
                        onSwiper={(swiper) => (swiperRef.current = swiper)}
                        onSlideChange={handleSlideChange}
                        loop={false}
                        slidesPerView={4.2}
                        spaceBetween={20}
                        mousewheel={true}
                        breakpoints={{
                          1280: { slidesPerView: 4.2, spaceBetween: 15 },
                          1024: { slidesPerView: 3.3, spaceBetween: 15 },
                          768: { slidesPerView: 2.5, spaceBetween: 15 },
                          480: { slidesPerView: 1.7, spaceBetween: 20 },
                          320: { slidesPerView: 1.2, spaceBetween: 20 },
                        }}
                        className="transition-all duration-300 ease-in-out"
                      >
                        {postData.map(
                          (post, index) =>
                            post?.__typename === "Post" && (
                              <SwiperSlide key={post.id}>
                                <BrowseLensUserCard
                                  cardData={post}
                                  border={themeProperties.backgroundColor}
                                  category={themeProperties.title}
                                  userID={profileData.address}
                                  location={
                                    getLocation(profileData.metadata?.attributes) ||
                                    profileData.username?.localName ||
                                    ""
                                  }
                                  profileAvatar={
                                    sanitizeDStorageUrl(profileData?.metadata?.picture) ||
                                    "/assets/profileAvatar.svg"
                                  }
                                  profileName={profileData.metadata?.name || "Profile Name"}
                                  localName={
                                    getLocation(profileData.metadata?.attributes) ||
                                    profileData.username?.localName
                                  }
                                />
                              </SwiperSlide>
                            )
                        )}
                      </Swiper>
                      {window.innerWidth >= 480 && (
                        <>
                          {!currentIndex.isStart && (
                            <button
                              className={`absolute top-[45%] left-0 h-[100%] w-[84px] z-[999]  transform -translate-y-1/2 text-black p-2 rounded-md`}
                              onClick={() => swiperRef.current?.slidePrev()}
                              style={{
                                backgroundColor: "transparent",
                              }}
                            >
                              <LazyMedia
                                src="/assets/ChevronsDown.svg"
                                alt="Previous"
                                type="image"
                                className="h-[50px] w-[50px] rotate-90"
                              />
                            </button>
                          )}

                          {!currentIndex.isEnd && shouldShowNextButton() && (
                            <button
                              className="absolute top-[45%] right-0 h-[100%] w-[84px] z-[999] transform -translate-y-1/2 text-black p-2 rounded-md"
                              onClick={() => swiperRef.current?.slideNext()}
                              style={{
                                backgroundColor: "transparent",
                              }}
                            >
                              <LazyMedia
                                src="/assets/ChevronsDown.svg"
                                alt="Next"
                                type="image"
                                className="h-[50px] w-[50px] -rotate-90"
                              />
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        )}
      </div>
    </>
  );
};

export default BrowseLensUser;
