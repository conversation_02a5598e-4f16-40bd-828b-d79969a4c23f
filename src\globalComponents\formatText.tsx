import React from "react";

interface FormattedTextProps {
  text: string;
  className?: string;
  linkClassName?: string;
  preserveWhitespace?: boolean;
}

const FormattedText: React.FC<FormattedTextProps> = ({
  text,
  className = "text-subtitle break-words",
  linkClassName = "text-blue-600 underline hover:text-blue-800 transition-colors",
  preserveWhitespace = true,
}) => {
  // If no text is provided, return null
  if (!text) return null;

  // Enhanced URL regex that handles more URL formats
  const urlRegex =
    /(https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.\-~!$&'()*+,;=:@])*)?(?:\?(?:[\w&=%.\-~!$'()*+,;=:@])*)?(?:\#(?:[\w.\-~!$&'()*+,;=:@])*)?)/g;

  const preprocessText = (inputText: string): string => {
    // Handle URLs that are broken across lines

    // Pattern 1: URL ending with / followed by path on next line
    let result = inputText.replace(
      /(https?:\/\/[^\s]*\/)\s*\n\s*([a-zA-Z0-9\/_.\-~!$&'()*+,;=:@#?%]+)/g,
      "$1$2"
    );

    // Pattern 2: URL without trailing / followed by path on next line
    result = result.replace(
      /(https?:\/\/[^\s\/]+)\s*\n\s*([a-zA-Z0-9\/_.\-~!$&'()*+,;=:@#?%]+)/g,
      (match, urlStart, urlEnd) => {
        // Only join if the second part starts with / or looks like a continuation
        if (urlEnd.startsWith("/") || urlEnd.startsWith("?") || urlEnd.startsWith("#")) {
          return urlStart + urlEnd;
        }
        // For paths without leading /, add the / separator
        if (/^[a-zA-Z0-9\-_]+/.test(urlEnd)) {
          return urlStart + "/" + urlEnd;
        }
        return match;
      }
    );

    // Pattern 3: Handle query parameters or fragments split across lines
    result = result.replace(/(https?:\/\/[^\s]*[?&])\s*\n\s*([a-zA-Z0-9=&%.\-_]+)/g, "$1$2");

    return result;
  };

  // Preprocess text to handle broken URLs, then split by URLs
  const preprocessedText = preprocessText(text);
  const parts = preprocessedText.split(urlRegex);

  const elements = parts.map((part, index) => {
    // Check if this part is a URL
    if (part.match(urlRegex)) {
      return (
        <a
          key={index}
          href={part}
          className={linkClassName}
          target="_blank"
          rel="noopener noreferrer"
        >
          {part}
        </a>
      );
    }

    // Return regular text
    return <span key={index}>{part}</span>;
  });

  const baseClassName = preserveWhitespace ? `${className} whitespace-pre-wrap` : className;

  return <div className={baseClassName}>{elements}</div>;
};

// Legacy component for backward compatibility
const ServiceDescription = ({ description }: any) => {
  return (
    <FormattedText text={description} className="text-subtitle mt-3 break-words max-md:text-sm" />
  );
};

export default ServiceDescription;
export { FormattedText };
