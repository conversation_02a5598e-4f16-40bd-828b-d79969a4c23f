import { orderBy, Timestamp, collection, query, getDocs, where } from "firebase/firestore";
import { Post } from "./postService";
import { initFirebase } from "../../firebaseConfig";
import { Event } from "./eventsServices";
import { Service } from "./serviceService";
import { User } from "./UserInterface";

export enum PostSearchBy {
  ABOUT_PROJECT = "about_project", // description
  HASHTAGS = "hashtags",
  GEOTAGS = "geotags", // location
}

export enum ProfileSearchBy {
  PROFILE_NAME = "profile_name",
  HASHTAGS = "hashtags",
  LOCATION = "location",
}

export enum ServiceSearchBy {
  DESCRIPTION = "description",
  TITLE = "title",
}

export enum EventSearchBy {
  DESCRIPTION = "description",
  NAME = "name", // title
}

export enum PublishingDateFilter { 
  LAST_24_HOUR = "Past 24 hours" , 
  LAST_WEEK  = "Past week" , 
  LAST_MONTH  = "Past month",
}


interface GetFiltersInput {
  searchTerm: string;
  filterBy: PostSearchBy | EventSearchBy | ServiceSearchBy | ProfileSearchBy;
  limit?: number;
  startAfterAddedAt?: Timestamp | Date;
  filters ?: {
      category?:string[] , 
      user_id?:string[] , 
      location?:string[] , 
      date_of_publishing?:PublishingDateFilter
  }
}

interface BaseFilterResult {
  nextStartAfterAddedAt?: Timestamp | Date;
  hasMore: boolean;
}

interface GetPostByFiltersResult extends BaseFilterResult {
  posts: Post[];
}
interface GetEventByFiltersResult extends BaseFilterResult {
  events: Event[];
}
interface GetServiceByFiltersResult extends BaseFilterResult {
  services: Service[];
}
interface GetProfileByFiltersResult extends BaseFilterResult {
  profiles: User[];
}

export class FilterSearchManager {
  private POST_COLLECTION = "posts";
  private USER_COLLECTION = "users";
  private EVENT_COLLECTION = "events";
  private SERVICE_COLLECTION = "services";

  static instance: FilterSearchManager | null = null;

  private constructor() { }

  static getInstance() {
    if (!this.instance) {
      this.instance = new FilterSearchManager();
    }
    return this.instance;
  }

  filterPostsByPublishingDate(
  array:Array< Post | Event | Service | User>,
  dateFilter?: PublishingDateFilter,
  entity?:"post" | "events" | "services" | "users"
): Array< Post | Event | Service | User> {
  if (!dateFilter) return array; 

  const now = Date.now();
  let threshold: number;

  switch (dateFilter) {
    case PublishingDateFilter.LAST_24_HOUR:
      threshold = now - 24 * 60 * 60 * 1000;
      break;
    case PublishingDateFilter.LAST_WEEK:
      threshold = now - 7 * 24 * 60 * 60 * 1000;
      break;
    case PublishingDateFilter.LAST_MONTH:
      threshold = now - 30 * 24 * 60 * 60 * 1000;
      break;
    default:
      return array;
  }

  let resp:any = [];
  
  if(entity === "post") { 
  resp = array.filter(post => {
    // @ts-ignore
    const addedAtMillis = post?.added_at?.toMillis?.();
    return addedAtMillis !== undefined && addedAtMillis >= threshold;
  })
  } else if(entity === "events") { 
  resp = array.filter(post => {
    // @ts-ignore
    const addedAtMillis = post?.created_at?.toMillis?.();
    return addedAtMillis !== undefined && addedAtMillis >= threshold;
  })    
  }else if(entity === "services") { 
  resp = array.filter(post => {
    // @ts-ignore
    const addedAtMillis = post?.created_at?.toMillis?.();
    return addedAtMillis !== undefined && addedAtMillis >= threshold;
  })    
  }else if(entity === "users") { 
  resp = array.filter(post => {
    // @ts-ignore
    const addedAtMillis = post?.created_at?.toMillis?.();
    return addedAtMillis !== undefined && addedAtMillis >= threshold;
  })    
  }


  return resp;
}
  
  
  GetPostByFilters = async ({
    payload,
  }: {
    payload: GetFiltersInput;
  }): Promise<GetPostByFiltersResult> => {
    try {
      const { searchTerm, filterBy, limit = 10, startAfterAddedAt } = payload;
      if (searchTerm.length === 0) {
        return {
          posts: [],
          nextStartAfterAddedAt: undefined,
          hasMore: false,
        };
      }
      const { db } = await initFirebase();

      const postsQuery = query(collection(db, this.POST_COLLECTION), orderBy("added_at", "desc"));

      // Get posts
      const postsSnap = await getDocs(postsQuery);

      const lowerSearch = searchTerm.trim().toLowerCase();
      const matchedPosts: Post[] = [];

      for (const doc of postsSnap.docs) {
        const post = doc.data() as Post;
        post.id = doc.id;

        let match = false;

        if (filterBy === PostSearchBy.ABOUT_PROJECT) {
          match = post.about_project?.toLowerCase().includes(lowerSearch);
        } else if (filterBy === PostSearchBy.HASHTAGS) {
          match =
            Array.isArray(post.hashtags) &&
            post.hashtags.some((tag) => tag.toLowerCase().includes(lowerSearch));
        } else if (filterBy === PostSearchBy.GEOTAGS) {
          match =
            Array.isArray(post.geotags) &&
            post.geotags.some((tag) => tag.toLowerCase().includes(lowerSearch));
        }

        if (match) {
          matchedPosts.push(post);
        }
      }

      let startIndex = 0;

      if (startAfterAddedAt) {
        startIndex =
          matchedPosts.findIndex(
            //@ts-ignore
            (post) => post.added_at?.toMillis() === startAfterAddedAt?.toMillis()
          ) + 1; // +1 to exclude the last one
      }

      let enrichedPosts: Post[] = await Promise.all(
        matchedPosts.map(async (post) => {
          const userQuery = query(
            collection(db, "users"),
            where("posts", "array-contains", post.id)
          );

          const userSnap = await getDocs(userQuery);

          if (!userSnap.empty) {
            const userDoc = userSnap.docs[0];
            const userData = userDoc.data();

            return {
              ...post,
              profile_pic:userData?.avatar
            };
          }

          return post;
        })
      );      
      let finalResp:Post[] = [];
      let filterApplied = false;

      //--------------------- check if there is filter 

      if(payload?.filters?.date_of_publishing) { 
        enrichedPosts = this.filterPostsByPublishingDate(enrichedPosts,payload?.filters?.date_of_publishing,"post") as Post[];
      }      

      if(payload?.filters?.category?.length || payload?.filters?.location?.length || payload?.filters?.user_id?.length
      ) { 
        filterApplied = true;
        for(let i = 0 ; i < enrichedPosts?.length; i++) { 
          let current = enrichedPosts?.[i];
          
        // category filter
        if(payload?.filters?.category?.length) {
          if(payload?.filters?.category?.includes("Literature")){
            payload.filters.category = [...payload?.filters?.category , "Storytelling"];
          } 
          if(payload?.filters?.category?.includes(current?.category)) { 
              finalResp?.push(current);
          }
        }

        // location filter
        if(payload?.filters?.location?.length) { 
          if(payload?.filters?.location.find((c)=>current?.geotags.includes(c))) { 
              finalResp?.push(current);
          }
        }

        // user_id
 
        if(payload?.filters?.user_id?.length) { 
          if(current?.id && payload?.filters?.user_id?.includes(current?.id)) { 
             finalResp?.push(current);
          }
        }

        }
      }      
      
      if(!filterApplied) { 
        finalResp = enrichedPosts;
      }

      //--------------------- check if there is filter 

      const paginated = finalResp.slice(startIndex, startIndex + limit);
      const lastPost = paginated[paginated.length - 1];
      const hasMore = finalResp.length > startIndex + limit;


      return {
        posts: paginated?.map((current)=>{
          return {
            ...current , 
            category:current.category === "Storytelling" ? "Literature" : current.category
          }
        }),
        nextStartAfterAddedAt: lastPost?.added_at,
        hasMore: hasMore,
      };
    } catch (error) {
      console.error("Error in GetPostByFilters:", error);
      throw new Error("GetPostByFiltersFailed");
    }
  };

  GetEventByFilters = async ({
    payload,
  }: {
    payload: GetFiltersInput;
  }): Promise<GetEventByFiltersResult> => {
    try {
      const { searchTerm, filterBy, limit = 10, startAfterAddedAt } = payload;

      if (searchTerm.length === 0) {
        return {
          events: [],
          nextStartAfterAddedAt: undefined,
          hasMore: false,
        };
      }
      const { db } = await initFirebase();

      const eventsQuery = query(
        collection(db, this.EVENT_COLLECTION),
        orderBy("created_at", "desc")
      );

      // Get events
      const eventsSnap = await getDocs(eventsQuery);

      const lowerSearch = searchTerm.trim().toLowerCase();
      const matchedEvents: Event[] = [];
      
      for (const doc of eventsSnap.docs) {
        const event = doc.data() as Event;
        event.id = doc.id;

        let match = false;


        if (filterBy === EventSearchBy.DESCRIPTION) {
          match = event.description?.toLowerCase().includes(lowerSearch);
        } else if (filterBy === EventSearchBy.NAME) {
          match = event.name?.toLowerCase().includes(lowerSearch);
        }
        
        if (match) {
          matchedEvents.push(event);
        }
      }

      let startIndex = 0;

      if (startAfterAddedAt) {
        startIndex = matchedEvents.findIndex((post) => post.created_at === startAfterAddedAt) + 1; // +1 to exclude the last one
      }


      let enrichedEvents: Event[] = await Promise.all(
        matchedEvents.map(async (event) => {
          const userQuery = query(
            collection(db, "users"),
            where("events", "array-contains", event.id)
          );

          const userSnap = await getDocs(userQuery);

          if (!userSnap.empty) {
            const userDoc = userSnap.docs[0];
            const userData = userDoc.data();

            return {
              ...event,
              user_id: userDoc.id,
              category: userData.categories?.[0],
              profile_name: userData.profile_name,
              profile_pic:userData?.avatar, 
              location:userData?.location
            };
          }

          return event;
        })
      );

      let finalResp:Event[] = [];
      let filterApplied = false;

      //--------------------- check if there is filter 

      if(payload?.filters?.date_of_publishing) { 
        enrichedEvents = this.filterPostsByPublishingDate(enrichedEvents,payload?.filters?.date_of_publishing,"events") as Event[];
      }

      if(payload?.filters?.category?.length || payload?.filters?.location?.length || payload?.filters?.user_id?.length 
      ) { 
        filterApplied = true;
        for(let i = 0 ; i < enrichedEvents?.length; i++) { 
          let current = enrichedEvents?.[i];
          
        // category filter
        if(payload?.filters?.category?.length) {
          if(payload?.filters?.category?.includes("Literature")){
            payload.filters.category = [...payload?.filters?.category , "Storytelling"];
          } 
          // @ts-ignore
          if(payload?.filters?.category?.includes(current?.category)) { 
              finalResp?.push(current);
          }
        }

        // location filter ❌ not applied in events
        if(payload?.filters?.location?.length) { 
          // @ts-ignore
          if(payload?.filters?.location.find((c)=>current?.location?.includes(c))) { 
              finalResp?.push(current);
          }
        }

        // user_id
        if(payload?.filters?.user_id?.length) { 
          if(current?.id && payload?.filters?.user_id?.includes(current?.id)) { 
             finalResp?.push(current);
          }
        }

        }
      }      
      
      if(!filterApplied) { 
        finalResp = enrichedEvents;
      }

      
   
      
      

      const paginated = finalResp.slice(startIndex, startIndex + limit);
      const lastPost = paginated[paginated.length - 1];
      const hasMore = finalResp.length > startIndex + limit;

   

      return {
        events: paginated?.map((current)=>{
          return {
            ...current , 
            //@ts-ignore
            category:current.category === "Storytelling" ? "Literature" : current.category
          }
        }),
        nextStartAfterAddedAt: lastPost?.created_at,
        hasMore: hasMore,
      };
    } catch (error) {
      console.error("Error in GetPostByFilters:", error);
      throw new Error("GetPostByFiltersFailed");
    }
  };

  GetServiceByFilters = async ({
    payload,
  }: {
    payload: GetFiltersInput;
  }): Promise<GetServiceByFiltersResult> => {
    try {
      const { searchTerm, filterBy, limit = 10, startAfterAddedAt } = payload;
      if (searchTerm.length === 0) {
        return {
          services: [],
          nextStartAfterAddedAt: undefined,
          hasMore: false,
        };
      }
      const { db } = await initFirebase();

      const serviceQuery = query(
        collection(db, this.SERVICE_COLLECTION),
        orderBy("created_at", "desc")
      );

      // Get service
      const serviceSnap = await getDocs(serviceQuery);

      const lowerSearch = searchTerm.trim().toLowerCase();
      const matchedServices: Service[] = [];

      for (const doc of serviceSnap.docs) {
        const service = doc.data() as Service;
        service.id = doc.id;

        let match = false;

        if (filterBy === ServiceSearchBy.DESCRIPTION) {
          match = service.description?.toLowerCase().includes(lowerSearch);
        } else if (filterBy === ServiceSearchBy.TITLE) {
          match = service.title?.toLowerCase().includes(lowerSearch);
        }

        if (match) {
          matchedServices.push(service);
        }
      }

      let startIndex = 0;

      if (startAfterAddedAt) {
        startIndex =
          matchedServices.findIndex((service) => service.created_at === startAfterAddedAt) + 1; // +1 to exclude the last one
      }

     let enrichedServices: Service[] = await Promise.all(
        matchedServices.map(async (service) => {
          const userQuery = query(
            collection(db, "users"),
            where("services", "array-contains", service.id)
          );

          const userSnap = await getDocs(userQuery);

          if (!userSnap.empty) {
            const userDoc = userSnap.docs[0];
            return {
              ...service,
              user_id: userDoc.id,
              profile_name: userDoc.data().profile_name,
              profile_pic:userDoc.data()?.avatar,
              location:userDoc?.data()?.location
            };
          }

          return service;
        })
      );      

      let finalResp:Service[] = [];
      let filterApplied = false;

      //--------------------- check if there is filter 

      if(payload?.filters?.date_of_publishing) { 
        enrichedServices = this.filterPostsByPublishingDate(enrichedServices,payload?.filters?.date_of_publishing,"services") as Service[];
      }

      if(payload?.filters?.category?.length || payload?.filters?.location?.length || payload?.filters?.user_id?.length ) { 
        filterApplied = true;
        for(let i = 0 ; i < enrichedServices?.length; i++) { 
          let current = enrichedServices?.[i];
          
        // category filter
        if(payload?.filters?.category?.length) {
          if(payload?.filters?.category?.includes("Literature")){
            payload.filters.category = [...payload?.filters?.category , "Storytelling"];
          } 
          if(payload?.filters?.category?.includes(current?.category)) { 
              finalResp?.push(current);
          }
        }

        // location filter ❌ not applied
        if(payload?.filters?.location?.length) { 
          // @ts-ignore
          if(payload?.filters?.location.find((c)=>current?.location?.includes(c))) { 
              finalResp?.push(current);
          }
        }

        // user_id
 
        if(payload?.filters?.user_id?.length) { 
          if(current?.id && payload?.filters?.user_id?.includes(current?.id)) { 
             finalResp?.push(current);
          }
        }

        }
      }      
      
      if(!filterApplied) { 
        finalResp = enrichedServices;
      }


      //--------------------- check if there is filter 

      
      

      const paginated = finalResp.slice(startIndex, startIndex + limit);
      const lastService = paginated[paginated.length - 1];
      const hasMore = finalResp.length > startIndex + limit;

 

      return {
          services: paginated?.map((current)=>{
          return {
            ...current , 
            //@ts-ignore
            category:current.category === "Storytelling" ? "Literature" : current.category
          }
        }),        
        nextStartAfterAddedAt: lastService?.created_at,
        hasMore: hasMore,
      };
    } catch (error) {
      console.error("Error in GetPostByFilters:", error);
      throw new Error("GetPostByFiltersFailed");
    }
  };

  GetProfileByFilters = async ({
    payload,
  }: {
    payload: GetFiltersInput;
  }): Promise<GetProfileByFiltersResult> => {
    try {
      const { searchTerm, filterBy, limit = 10, startAfterAddedAt } = payload;
      if (searchTerm.length === 0) {
        return {
          profiles: [],
          nextStartAfterAddedAt: undefined,
          hasMore: false,
        };
      }
      const { db } = await initFirebase();

      const userQuery = query(collection(db, this.USER_COLLECTION), orderBy("created_at", "desc"));

      // Get profile
      const profileSnap = await getDocs(userQuery);
      

      const lowerSearch = searchTerm.trim().toLowerCase();
      let matchedServices: User[] = [];

      for (const doc of profileSnap.docs) {
        const user = doc.data() as User;
        user.id = doc.id;

        let match = false;

        
        if (filterBy === ProfileSearchBy.PROFILE_NAME) {
          match = user.profile_name?.toLowerCase().includes(lowerSearch);
        } else if (filterBy === ProfileSearchBy.HASHTAGS) {
          match =
            Array.isArray(user.hashtags) &&
            user.hashtags.some((tag) => tag.toLowerCase().includes(lowerSearch));
        } else if (filterBy === ProfileSearchBy.LOCATION) {
          match = user.location?.toLowerCase().includes(lowerSearch);
        }

        if (match) {
          matchedServices.push(user);
        }
      }

      let startIndex = 0;

      if (startAfterAddedAt) {
        startIndex = matchedServices.findIndex((user) => user.created_at === startAfterAddedAt) + 1; // +1 to exclude the last one
      }

          let finalResp:User[] = [];
      let filterApplied = false;  
      
      //--------------------- check if there is filter 

      // let enrichedPosts:User[] = []
      
      if(payload?.filters?.date_of_publishing) { 
        matchedServices = this.filterPostsByPublishingDate(matchedServices,payload?.filters?.date_of_publishing,"users") as User[];
      }

      if(payload?.filters?.category?.length || payload?.filters?.location?.length || payload?.filters?.user_id?.length 
      ) { 
        filterApplied = true;
        for(let i = 0 ; i < matchedServices?.length; i++) { 
          let current = matchedServices?.[i];
          
        // category filter
        if(payload?.filters?.category?.length) {
          if(payload?.filters?.category?.includes("Literature")){
            payload.filters.category = [...payload?.filters?.category , "Storytelling"];
          } 
          let filterCat:string[] = payload.filters.category;
          let userCat:string[] = current?.categories ?? [];

          let mp:{[k:string]:number} = {};

          filterCat?.forEach((curr)=>mp[curr] ? mp[curr]+=1 : mp[curr] = 1);

          userCat?.forEach((curr)=>mp[curr] ? mp[curr]+=1 : mp[curr] = 1);
          
          if(Object.values(mp).sort()?.[Object.values(mp)?.length-1] > 1) {
              finalResp?.push(current);
          }
        }

        // location filter
        if(payload?.filters?.location?.length) { 
          if(payload?.filters?.location.find((c)=>current?.location.includes(c))) { 
              finalResp?.push(current);
          }
        }

        // user_id
        if(payload?.filters?.user_id?.length) { 
          if(current?.id && payload?.filters?.user_id?.includes(current?.id)) { 
             finalResp?.push(current);
          }
        }

        }
      }      
      
      if(!filterApplied) { 
        finalResp = matchedServices;
      }

      //--------------------- check if there is filter 
      
      
      

      const paginated = finalResp.slice(startIndex, startIndex + limit);
      const lastService = paginated[paginated.length - 1];
      const hasMore = finalResp.length > startIndex + limit;

      return {
        profiles: paginated,
        nextStartAfterAddedAt: lastService?.created_at,
        hasMore: hasMore,
      };
    } catch (error) {
      console.error("Error in GetPostByFilters:", error);
      throw new Error("GetPostByFiltersFailed");
    }
  };

  
}
