import { useState, useEffect } from "react";
import { GlobalCard } from "@/globalComponents/globalCard";
import { DollarSign } from "react-feather";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getServiceById } from "@/services/serviceService";
import CardSkeleton from "@/components/CardSkeleton";
import EmptyState from "@/components/EmptyState";

const ServicesCard = ({ onSelectService, activeColor }: any) => {
  const auth = useAuth();
  const profile = useProfile(auth?.userData?.uid);

  // State to store fetched services
  const [services, setServices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true); // Add loading state

  useEffect(() => {
    const fetchAllServices = async () => {
      setLoading(true); // Set loading to true when fetching starts
      try {
        if (profile?.profileData?.services) {
          const fetchedServices = await Promise.all(
            profile.profileData.services.map(async (serviceId: string) => {
              const response = await getServiceById(serviceId);
              // console.log(response);

              return response?.success ? response.service : null;
            })
          );
          setServices(fetchedServices.filter((service) => service !== null)); // Filter out invalid services
        }
      } catch (error) {
        console.error("Error fetching services:", error);
      } finally {
        setLoading(false); // Set loading to false when fetching completes
      }
    };

    fetchAllServices();
  }, [profile?.profileData?.services]);

  return (
    <>
      <div>
        <div className="w-full bg-white sticky top-[7.2rem]">
          <div className="row justify-between">
            <p className="text-primary text-xl font-bold">My Services</p>
            <DollarSign className="text-primary" />
          </div>
        </div>
        <div className="flex flex-row w-full overflow-y-scroll gap-3 hide-scroll-custom h-[calc(100vh-280px)] bg-white pt-4">
          {loading ? (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              <CardSkeleton count={3} />
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-3">
              {services.length > 0 ? (
                services.map((service, index) => (
                  <div
                    className="cursor-pointer"
                    key={index}
                    onClick={() => onSelectService(service.id)}
                  >
                    <GlobalCard
                      border={activeColor}
                      title={service.title} // Pass service data as props if needed
                      description={service.description} // Example props
                      price={service.price}
                      duration={service.duration}
                      currency={service.currency || "GBP"}
                    />
                  </div>
                ))
              ) : (
                <EmptyState
                  type="services"
                  title="No Services Yet"
                  message="Create your first service to start offering your skills"
                  isOwnProfile={true}
                  actionLabel="Create Service"
                  onAction={() => onSelectService("new")}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ServicesCard;
