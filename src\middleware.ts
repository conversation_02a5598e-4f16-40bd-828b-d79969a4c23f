import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the response
  const response = NextResponse.next();

  // Add security headers to prevent clickjacking
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  
  // Add Content-Security-Policy with frame-ancestors directive as an additional layer of protection
  // This is more flexible and supported by modern browsers
  response.headers.set(
    'Content-Security-Policy',
    "frame-ancestors 'self';"
  );

  return response;
}

// Configure the middleware to run on all routes
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
