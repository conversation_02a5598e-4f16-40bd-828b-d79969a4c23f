/**
 * Payment handler utility to manage the transition from hosted to custom checkout
 */

import { redirectToCustomCheckout } from './checkout';

/**
 * Handle payment response from API - supports both old hosted and new custom checkout
 * @param {Object} response - API response from checkout endpoints
 * @param {Function} onSuccess - Callback for successful payment initiation
 * @param {Function} onError - Callback for errors
 */
export async function handlePaymentResponse(response, onSuccess = null, onError = null) {
  try {
    const data = await response.json();
    
    if (!response.ok) {
      const error = data.error || 'Payment initiation failed';
      console.error('Payment API error:', error);
      if (onError) onError(error);
      return;
    }

    // Check if API returned custom checkout data
    if (data.useCustomCheckout && data.checkoutData) {
      console.log('Using custom checkout flow');
      redirectToCustomCheckout(data.checkoutData);
      if (onSuccess) onSuccess(data);
      return;
    }

    // Legacy support - if API still returns hosted checkout URL
    if (data.checkoutUrl || data.url) {
      console.log('Using legacy hosted checkout flow');
      const checkoutUrl = data.checkoutUrl || data.url;
      window.location.href = checkoutUrl;
      if (onSuccess) onSuccess(data);
      return;
    }

    // If neither custom nor hosted checkout data is available
    const error = 'No checkout method available in API response';
    console.error(error, data);
    if (onError) onError(error);
    
  } catch (error) {
    console.error('Error handling payment response:', error);
    if (onError) onError(error.message);
  }
}

/**
 * Generic payment initiator that works with any checkout API endpoint
 * @param {string} apiEndpoint - API endpoint to call
 * @param {Object} paymentData - Payment data to send
 * @param {Object} options - Additional options
 */
export async function initiatePayment(apiEndpoint, paymentData, options = {}) {
  const { onSuccess, onError, onLoading } = options;
  
  try {
    if (onLoading) onLoading(true);
    
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    await handlePaymentResponse(response, onSuccess, onError);
    
  } catch (error) {
    console.error('Error initiating payment:', error);
    if (onError) onError(error.message);
  } finally {
    if (onLoading) onLoading(false);
  }
}

/**
 * Specific helper for regular checkout
 */
export async function initiateRegularCheckout(checkoutData, options = {}) {
  return initiatePayment('/api/checkout', checkoutData, options);
}

/**
 * Specific helper for escrow checkout
 */
export async function initiateEscrowCheckout(escrowData, options = {}) {
  return initiatePayment('/api/escrow/create', escrowData, options);
}

/**
 * Specific helper for seller-to-seller payments
 */
export async function initiateSellerPayment(sellerData, options = {}) {
  return initiatePayment('/api/checkout-to-seller', sellerData, options);
}

/**
 * Direct custom checkout helper - bypasses API and goes straight to custom checkout
 * Use this when you want to skip the API layer entirely
 */
export function directCustomCheckout(checkoutData) {
  console.log('Direct custom checkout initiated');
  redirectToCustomCheckout(checkoutData);
}

/**
 * Migration helper - converts old payment component calls to new system
 * @param {string} oldApiEndpoint - The old API endpoint being called
 * @param {Object} oldPaymentData - The old payment data format
 * @returns {Promise} - Promise that resolves when payment is initiated
 */
export async function migratePaymentCall(oldApiEndpoint, oldPaymentData) {
  // Map old API endpoints to new handlers
  const endpointMap = {
    '/api/checkout': initiateRegularCheckout,
    '/api/checkout-escrow': initiateEscrowCheckout,
    '/api/escrow/create': initiateEscrowCheckout,
    '/api/checkout-to-seller': initiateSellerPayment,
  };

  const handler = endpointMap[oldApiEndpoint];
  
  if (handler) {
    return handler(oldPaymentData);
  } else {
    // Fallback to generic payment initiator
    return initiatePayment(oldApiEndpoint, oldPaymentData);
  }
}

export default {
  handlePaymentResponse,
  initiatePayment,
  initiateRegularCheckout,
  initiateEscrowCheckout,
  initiateSellerPayment,
  directCustomCheckout,
  migratePaymentCall,
};
