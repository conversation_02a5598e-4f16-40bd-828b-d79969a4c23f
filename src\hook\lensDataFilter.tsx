import { AccountsBulkQuery, MainContentFocus, PageSize, PostType, useAccountsBulkQuery, usePostsQuery } from "@/graphql/test/generated";
import { getLensProfilesById } from "@/services/lensService";
import { useEffect, useState } from "react";

const useLensDataFilter = (categoryName: string) => {
  const [profiles, setProfiles] = useState<string[]>(["lens/chaoticmonk"]);
  const [profilesIds, setProfilesIds] = useState<string[]>([
    "0x89dc",
    "0x326c",
  ]);

  // console.log(categoryName);

  const [filteredData, setFilteredData] = useState<
    { handle: string; data: any[] }[]
  >([]);
  const [testData, setTestData] = useState<any[]>([]);
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);

  // const { data: profileData } = useProfilesQuery({
  //   request: { where: { handles: profiles } },
  // });
    const {
      data:profileData,
      isLoading: loadingProfile,
      error: profileError,
    } = useAccountsBulkQuery({
      request:{
         addresses:profiles
      }
    },{
      refetchOnWindowFocus:false,
      enabled:!!profiles
    })

  // const { data: publicationsData } = usePublicationsQuery({
  //   request: {
  //     where: {
  //       from: profilesIds,
  //       publicationTypes: [PublicationType.Post],
  //       metadata: {
  //         mainContentFocus: [
  //           PublicationMetadataMainFocusType.Image,
  //           PublicationMetadataMainFocusType.Video,
  //         ],
  //       },
  //     },
  //     limit: LimitType.Fifty,
  //     cursor: currentCursor,
  //   },
  // });
    const {
      data: publicationsData,
      isLoading: loadingPublications,
      error: publicationsError,
    } = 
    // v3
    usePostsQuery({
      request:{
        pageSize:PageSize.Fifty,
        cursor:currentCursor,
        filter:{
          authors:profilesIds,
          postTypes:[PostType.Root],
          metadata:{
            mainContentFocus:[
              MainContentFocus.Image , MainContentFocus.Video
            ] , 
          },
        }
      }
    }, {
      refetchOnWindowFocus: false,
      enabled: profilesIds.length > 0,
    })

  useEffect(() => {
    const fetchProfiles = async (category: string) => {
      const resp = await getLensProfilesById(category);
      const lensProfiles: string[] = resp?.lens_ids?.map(
        (curr: any) => `lens/${curr}`
      );
      setProfiles(lensProfiles);
    };

    fetchProfiles(categoryName.toLowerCase());
  }, [categoryName]);

  useEffect(() => {
    if (profileData) {
      const lensIds: string[] = profileData?.accountsBulk?.map(
        (curr: AccountsBulkQuery["accountsBulk"][0]) => curr.address
      );
      setProfilesIds(lensIds);
    }
  }, [profileData]);

  useEffect(() => {
    if (publicationsData) {
      // console.log("Updated publicationsData:", publicationsData);
      setTestData((prevData) => [
        ...prevData,
        ...publicationsData?.posts?.items,
      ]);
    }
  }, [publicationsData]);

  useEffect(() => {
    if (testData.length > 0 && profiles.length > 0) {
      const groupedData = profiles
        .map((handle) => ({
          handle,
          data: testData.filter(
            (item: any) => item?.by?.handle?.fullHandle === handle
          ),
        }))
        .filter((group) => group.data.length > 0);

      setFilteredData(groupedData);
      // console.log("Filtered Data:", groupedData);
    }
  }, [testData, profiles]);

  return filteredData;
};

export default useLensDataFilter;
