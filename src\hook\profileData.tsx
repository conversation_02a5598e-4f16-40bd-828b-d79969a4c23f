import { User } from "@/services/UserInterface";
import { getUserById } from "@/services/usersServices";
import { useEffect, useState } from "react";
import { ensureUserId } from "@/utils/userUtils";

const useProfile = (userId: string | undefined) => {
  // Ensure userId is always a string, never undefined
  const safeUserId = ensureUserId(userId);

  const [profileData, setProfileData]: any = useState<any | null>(null); // State for storing user data
  const [loading, setLoading] = useState<boolean>(false); // Loading state
  const [error, setError] = useState<string | null>(null); // Error state

  useEffect(() => {
    const fetchUser = async () => {
      if (!safeUserId) {
        setError("User ID is required.");
        return;
      }

      setLoading(true); // Start loading state

      try {
        // console.log(`Fetching user with ID: ${safeUserId}`);

        const response: {
          success: boolean;
          user: User;
        } = await getUserById(safeUserId);

        // console.log(response);

        if (response.success) {
          // Ensure the response contains the necessary data
          setProfileData(response?.user); // Assuming response has a 'user' field
          setError(null); // Clear any previous errors
        } else {
          setError("Failed to fetch user data.");
        }
      } catch (err) {
        setError("An error occurred while fetching the data.");
        // console.error("Error fetching user data:", err);
      } finally {
        setLoading(false); // Stop loading state
      }
    };

    if (safeUserId) {
      fetchUser(); // Call the fetchUser function when safeUserId is present
    }
  }, [safeUserId]); // Run effect whenever `safeUserId` changes

  return { profileData, loading, error };
};

export default useProfile;
