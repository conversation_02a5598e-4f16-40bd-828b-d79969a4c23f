"use client";
import React, { useState } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";

interface CustomCalendarProps {
  activeDate: Date;
  setActiveDate: (date: Date) => void;
}

const CalendarCompEvent: React.FC<CustomCalendarProps> = ({
  activeDate,
  setActiveDate,
}) => {
  return (
    // <div className="overflow-y-scroll hide-scroll-custom bg-white h-[calc(100vh-280px)] max-md:h-[calc(100vh-107px)]">
    <div className="custom-calendar">
      <Calendar
        onActiveStartDateChange={({ activeStartDate }) => {
          if (activeStartDate) {
            setActiveDate(activeStartDate);
          }
        }}
        value={activeDate}
        view="month"
        showNavigation={true}
        next2Label={null}
        prev2Label={null}
        className="hidden-calendar"
      />
    </div>
    // </div>
  );
};

export default CalendarCompEvent;
