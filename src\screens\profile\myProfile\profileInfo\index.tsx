"use client";
import {
  MoreHorizontal,
  Edit2,
  X,
  ChevronLeft,
  Copy,
  Search,
  Check,
  Facebook,
  Instagram,
  Twitter,
  CheckCircle,
  Circle,
} from "react-feather";

import ProfileInfoAbout from "./aboutMe";
import ProfileInfoHashtags from "./hashtags";
import ProfileInfoPersonalMotto from "./personalMotto";
import ProfileInfoSocialMedia from "./socialMedia";
import { Label } from "@radix-ui/react-label";
import { Textarea } from "@/components/ui/textarea";
import { useCallback, useEffect, useState, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import languagesList from "@/const/language.json";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { useGoogleMaps } from "@/context/GoogleMapsContext";
import LocationDropdown from "@/components/LocationDropdown";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../../theme";
import { Sheet } from "@/components/ui/sheet";
import { SheetContentNoClose } from "@/components/ui/sheet-no-close";
// import { Progress } from "@/components/ui/progress";
import Link from "next/link";
import useAuth from "@/hook";
import {
  calculateProfileComplete,
  deleteUserDetails,
  getUserById,
  updateUser,
} from "@/services/usersServices";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { FollowerManager } from "@/services/followServices";
import { Progress } from "@heroui/progress";
import { getId } from "@/services/authBridgeService";
import { initFirebase } from "../../../../../firebaseConfig";
import ProfileInfoVerifyEmail from "./verifyEmail";
import { useRouter } from "next/navigation";
import ProfileInfoConnAccount from "./connectedAccount";
import AuthSignup from "@/screens/auth";
import { getUserId } from "@/utils/userUtils";
import { FraudSubreason, ReportingReason, ReportManager } from "@/services/reportServices";
import { ChatManager } from "@/services/chatService";

const ProfileInfo = (props: any) => {
  // const router = useRouter();
  // const { isOpen: opneModal, onOpen, onOpenChange } = useDisclosure();
  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    // Check if postFile is a valid string
    if (!postFile) {
      return undefined; // Return undefined if postFile is null, undefined, or an empty string
    }

    // If the postFile already includes the baseUrl, return it as-is
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }
    if (postFile.includes("https://ik.imagekit.io")) {
      return postFile;
    }

    // Otherwise, construct the URL and return it
    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const [isOpen, setIsOpen] = useState(false);
  const [isOpenEditPhoto, setIsOpenEditPhoto] = useState(false);
  const [isOpenViewPhoto, setIsOpenViewPhoto] = useState(false);

  const [isOpenChangeCategories, setIsOpenChangeCategories] = useState(false);

  // Function to handle opening the change categories modal
  const handleOpenChangeCategories = () => {
    // Set the initial categories from the user profile if available
    if (profile?.categories?.length > 0) {
      setMainCat(profile.categories[0] || "");
      setSecondCat(profile.categories[1] || "");
    }
    setIsOpenChangeCategories(true);
  };
  const [isOpenDelete, setIsOpenDelete] = useState(false);
  const [isOpenOtherDelete, setIsOpenOtherDelete] = useState(false);
  const [isOpenDeleteReason, setIsOpenDeleteReason] = useState(false);
  const [triggerEffect, setTriggerEffect] = useState(false);
  const [profile, setProfile]: any = useState([]);
  const [toggle, setToggle] = useState(true);
  const [mainCat, setMainCat] = useState("");
  const [secondCat, setSecondCat] = useState("");
  const [isSavingCategories, setIsSavingCategories] = useState(false);
  const [categorySaveSuccess, setCategorySaveSuccess] = useState(false);
  const [toggleVisibleAll, setToggleVisibleAll] = useState(false);

  const [firstName, setFistName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [dob, setDob] = useState("");
  const [lensId, setLensId] = useState("");
  const [walletId, setWalletId] = useState("");
  const [lensUser, setLensUser] = useState("");
  const [lensUserLocanName, setLensUserLocanName] = useState("");

  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isOpenShare, setIsOpenShare] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMainCategoryDropdownOpen, setIsMainCategoryDropdownOpen] = useState(false);
  const [isSecondaryCategoryDropdownOpen, setIsSecondaryCategoryDropdownOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [isPersonalInfoSaving, setIsPersonalInfoSaving] = useState(false);
  const [personalInfoSaveSuccess, setPersonalInfoSaveSuccess] = useState(false);
  const [isPhotoSaving, setIsPhotoSaving] = useState(false);
  const [photoSaveSuccess, setPhotoSaveSuccess] = useState(false);
  const [isDeleteProcessing, setIsDeleteProcessing] = useState(false);
  const [deleteSuccess, setDeleteSuccess] = useState(false);

  //
  const [deleteReason, setDeleteReason] = useState("");

  //   for Category
  const [SelectedCategory, setSelectedCategory] = useState(null);

  const resonData = ["Inapropriate content", "Legal issue", "Explicit Content"];

  const handleItemClickCategory = (index: any) => {
    setSelectedCategory(index); // Set the clicked item as the selected one
    const themeEntries = Object.entries(themes);

    if (themeEntries[index]) {
      const selectedTheme = themeEntries[index][1]; // Extract the themeProperties
      setMainCat(selectedTheme.title);
    }
  };

  const handleItemClickCategorySecond = (index: any) => {
    setSelectedCategory(index); // Set the clicked item as the selected one
    const themeEntries = Object.entries(themes);

    if (themeEntries[index]) {
      const selectedTheme = themeEntries[index][1]; // Extract the themeProperties
      setSecondCat(selectedTheme.title);
    }
  };

  let bgColor: any;

  {
    Object.entries(themes)
      .filter(([_, themeProperties]) => themeProperties.title === props?.Category)
      .map(([themeName, themeProperties]) => (bgColor = themeProperties.backgroundColor));
  }

  const [isOpenSheet, setIsOpenSheet] = useState(false);

  // Function to toggle the sheet visibility
  const toggleSheet = () => {
    setIsOpenSheet(!isOpen);
  };

  const [isToggle, setIsToggle] = useState(false);
  useEffect(() => {
    const checkMobileView = () => {
      // Example: 768px is the breakpoint for mobile
      const isMobile = window.innerWidth <= 768;
      setIsToggle(isMobile);
    };

    checkMobileView(); // Initial check

    // Only update isToggle on resize if no input is focused
    // This prevents keyboard opening from triggering unwanted toggle
    const handleResize = () => {
      if (!document.activeElement?.tagName.match(/input|textarea/i)) {
        checkMobileView();
      }
    };

    window.addEventListener("resize", handleResize); // On resize

    // Clean up the event listener on unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const [progress, setProgress] = React.useState(0);

  // fetch data from firebase
  const auth = useAuth();
  // const profile = useProfile(
  //   props.isOtherProfile ? props.otherUserID : auth?.userData?.uid
  // );

  const handleCheck = async () => {
    try {
      const response = await getUserById(
        props.isOtherProfile ? props.otherUserID : auth?.userData?.uid
      );
      const resp = response.user;
      if (response.success) {
        // console.log(resp);

        setProfile(resp);
      }
    } catch (error) {
      alert(error || "Something went wrong");
    }
  };

  // console.log(profile);

  // for edit modal

  const [hashtags, setHashtags] = useState<string[]>([]);
  const [profileName, setProfileName]: any = useState(profile?.profile_name || "");
  const [location, setLocation]: any = useState(profile?.location || "");
  const [locationInput, setLocationInput] = useState(profile?.location || "");
  const [placePredictions, setPlacePredictions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showPlacesDropdown, setShowPlacesDropdown] = useState(false);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceLoaded = useRef(false);
  const locationInputRef = useRef<HTMLInputElement>(null);

  const [languageInput, setLanguageInput] = useState("");
  const [filteredLanguages, setFilteredLanguages] = useState<typeof languagesList>([]);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);

  const addHashtag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && e.currentTarget.value.trim()) {
      e.preventDefault();
      setHashtags([...hashtags, e.currentTarget.value.trim()]);
      e.currentTarget.value = ""; // Clear input
      setLanguageInput("");
      setShowLanguageDropdown(false);
    }
  };

  const handleLanguageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLanguageInput(value);

    if (value.trim() === "") {
      setShowLanguageDropdown(false);
      return;
    }

    const filtered = languagesList
      .filter((lang) => lang.name.toLowerCase().includes(value.toLowerCase()))
      .slice(0, 10); // Limit to 10 results for better performance

    setFilteredLanguages(filtered);
    setShowLanguageDropdown(true);
  };

  const selectLanguage = (language: string) => {
    if (!hashtags.includes(language)) {
      setHashtags([...hashtags, language]);
    }
    setLanguageInput("");
    setShowLanguageDropdown(false);
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter((hashtag) => hashtag !== tag));
  };

  const handleSubmit = async () => {
    if (profileName && location && hashtags) {
      try {
        setIsPersonalInfoSaving(true);
        setPersonalInfoSaveSuccess(false);

        const updatedData = {
          profile_name: profileName,
          location: location,
          languages: hashtags,
        };

        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setPersonalInfoSaveSuccess(true);

          // Wait for success animation
          setTimeout(() => {
            // After saving "Visible to all" info, switch to "Visible only to you" section
            setToggleVisibleAll(true);

            // Reset the success state after showing it
            setIsPersonalInfoSaving(false);
            setPersonalInfoSaveSuccess(false);

            // Refresh the profile data
            setToggle(true);
          }, 1500);
        } else {
          alert(response?.error ?? "something went wrong")
          console.error("Error updating personal info:", response.error);
          setIsPersonalInfoSaving(false);
        }
      } catch (error) {
        console.error("Error updating personal info:", error);
        setIsPersonalInfoSaving(false);
      }
    }
  };

  const handleSubmitVisibleToAll = async () => {
    const formattedDate = new Date(dob)
      .toLocaleDateString("de-DE") // Formats to "13.03.2025"
      .replace(/\//g, "."); // Ensure dot-separated format

    if (firstName && lastName && dob) {
      try {
        setIsPersonalInfoSaving(true);
        setPersonalInfoSaveSuccess(false);

        const updatedData = {
          full_name: firstName + " " + lastName,
          date_of_birth: formattedDate,
        };

        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setPersonalInfoSaveSuccess(true);

          // Wait for success animation
          setTimeout(() => {
            // Refresh the profile data
            setToggle(true);
            // Close the modal
            setIsOpen(false);
            // Reset the toggle for next time the modal is opened
            setToggleVisibleAll(false);
            // Reset form fields
            setFistName("");
            setLastName("");
            setDob("");
          }, 1500);
        } else {
          console.error("Error updating personal info:", response.error);
          setIsPersonalInfoSaving(false);
        }
      } catch (error) {
        console.error("Error updating personal info:", error);
        setIsPersonalInfoSaving(false);
      }
    }
  };

  // profile upload

  const [media, setMedia]: any = useState<File | null>(null);
  const [isMedia, setIsMedia] = useState(false);

  const handleEditAvatarSubmit = async () => {
    try {
      setIsPhotoSaving(true);
      setPhotoSaveSuccess(false);

      const { storage } = await initFirebase();

      const storageRef = ref(storage, `images/${Date.now()}_${media.name}`);
      const uploadTaskSnapshot = await uploadBytes(storageRef, media);
      const mediaURL = await getDownloadURL(uploadTaskSnapshot.ref);

      if (mediaURL) {
        const updatedData = {
          avatar: mediaURL,
          avatarSmall: mediaURL,
        };
        const response = await updateUser(auth?.userData?.uid, updatedData);
        if (response.success) {
          setPhotoSaveSuccess(true);

          // Wait for success animation
          setTimeout(() => {
            // Refresh the profile data
            setToggle(true);
            // Close the modal
            setIsOpenEditPhoto(false);
            // Reset form data
            setMedia(null);
            // Reset states
            setIsPhotoSaving(false);
            setPhotoSaveSuccess(false);
          }, 1500);
        } else {
          console.error("Failed to update user profile:", response.error);
          setIsPhotoSaving(false);
        }
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      setIsPhotoSaving(false);
    }
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setMedia(event.target.files[0]);
      setIsMedia(true);
    }
  };

  // handle count of follow and un-follow user
  const [followingList, setFollowingList]: any = useState([]);
  const [followingCheck, setFollowingCheck]: any = useState(false);
  const [followersList, setFollowersList]: any = useState([]);

  const [loading, setLoading] = useState(true);
  const [loadingOnClick, setLoadingOnClick] = useState<{
    [key: string]: boolean;
  }>({});

  const handleFollowing = async (userId: string) => {
    setLoading(true);
    try {
      const resp = await FollowerManager.getInstance().GetFollowingsByUserId(userId);
      // console.log({ userId, resp });

      setFollowingList(resp || []);
    } catch (error) {
      console.error("Error fetching followings:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowers = async (userId: string) => {
    setLoading(true);
    try {
      const resp = await FollowerManager.getInstance().GetFollowersByUserId(userId);
      setFollowersList(resp || []);
    } catch (error) {
      console.error("Error fetching followings:", error);
    } finally {
      setLoading(false);
    }
  };

  //check is followed by my me of not
  const handleFollowingList = async (userId: string) => {
    setLoading(true);
    // console.log(auth?.userId);

    try {
      const resp = await FollowerManager.getInstance().GetFollowingsByUserId(auth?.userId);

      for (let index = 0; index < resp.length; index++) {
        if (resp[index].id == props.otherUserID) {
          setFollowingCheck(true);
          // console.log(props);
        }
      }
    } catch (error) {
      console.error("Error fetching followings:", error);
    } finally {
      setLoading(false);
    }
  };

  // Follow un-follow user

  // Handle follow action
  const handleFollow = useCallback(
    async (id: string) => {
      try {
        if (!auth?.isLogin) {
          setIsSigninOpen(true);

          return;
        }

        const userId = getUserId(auth?.userData);

        setLoadingOnClick((prev) => ({ ...prev, [id]: true })); // Set loading only for the clicked user
        const resp = await FollowerManager.getInstance().FollowByUserId({
          src_id: userId,
          dest_id: id,
        });
        // console.log({ resp });
        if (resp == "success") {
          setFollowingCheck(true); // Update UI immediately
          setTriggerEffect((prev) => !prev);
          handleFollowers(props.otherUserID);
          handleFollowing(props.otherUserID);
          setLoadingOnClick((prev) => ({ ...prev, [id]: false })); // Set loading only for the clicked user
        }
      } catch (error) {
        setLoadingOnClick((prev) => ({ ...prev, [id]: false })); // Set loading only for the clicked user
      }
    },
    [auth]
  );

  // Handle follow action
  const handleUnFollow = useCallback(
    async (id: string) => {
      if (!auth?.isLogin) {
        setIsSigninOpen(true);

        return;
      }
      const userId = getUserId(auth?.userData);

      setLoadingOnClick((prev) => ({ ...prev, [id]: true })); // Set loading only for the clicked user
      const resp = await FollowerManager.getInstance().UnfollowByUserId({
        src_id: userId,
        dest_id: id,
      });
      // console.log(resp);
      if (resp == "success") {
        setFollowingCheck(false); // Update UI immediately
        setTriggerEffect((prev) => !prev);
        handleFollowers(props.otherUserID);
        handleFollowing(props.otherUserID);
        setLoadingOnClick((prev) => ({ ...prev, [id]: false })); // Set loading only for the clicked user
      }
    },
    [auth]
  );

  const [value, setValue] = React.useState(0);

  const handleSubmitChangeCategory = async () => {
    if (mainCat && secondCat) {
      try {
        setIsSavingCategories(true);
        setCategorySaveSuccess(false);

        const updatedData = {
          categories: [mainCat, secondCat],
        };

        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setCategorySaveSuccess(true);

          // Wait for success animation
          setTimeout(() => {
            // Trigger parent component to refresh data
            props.setToggleMain(true);
            // Refresh the profile data
            setToggle(true);
            // Reset form data
            setMainCat("");
            setSecondCat("");
            // Close the modal
            setIsOpenChangeCategories(false);
            // Reset states
            setIsSavingCategories(false);
            setCategorySaveSuccess(false);

            // Refresh the page to show updated categories
            window.location.reload();
          }, 1500);
        } else {
          console.error("Error updating categories:", response.error);
          setIsSavingCategories(false);
        }
      } catch (error) {
        console.error("Error updating categories:", error);
        setIsSavingCategories(false);
      }
    }
  };

  // handle calculateProfileComplete
  const handelProfileComplete = async () => {
    try {
      const response = await calculateProfileComplete({
        user_id: auth.userId,
      });
      const resp = response;
      resp?.completionPercentage && setProgress(resp?.completionPercentage);
    } catch (error) {
      alert(error || "Something went wrong");
    }
  };

  useEffect(() => {
    const fetchProfileCompletion = async () => {
      await handelProfileComplete();
    };

    fetchProfileCompletion();

    // getLensUserId();
  }, [props, auth?.userId]); // fix

  // React.useEffect(() => {
  //   const timer = setTimeout(() => setProgress(0), 500);
  //   return () => clearTimeout(timer);
  // }, []);

  useEffect(() => {
    handleCheck();
    handelProfileComplete();
    setToggle(false);
  }, [props.isOtherProfile, auth?.userId, toggle]);

  // Initialize categories from profile data when it changes
  useEffect(() => {
    if (profile?.categories?.length > 0) {
      // Only initialize if not already in a modal
      if (!isOpenChangeCategories) {
        setMainCat(profile.categories[0] || "");
        setSecondCat(profile.categories[1] || "");
      }
    }
  }, [profile, isOpenChangeCategories]);

  useEffect(() => {
    if (props.isOtherProfileStatus) {
      handleFollowers(props.otherUserID);
      handleFollowing(props.otherUserID);
    } else {
      handleFollowers(auth?.userId);
      handleFollowing(auth?.userId);
    }
  }, [props.isOtherProfileStatus, auth?.userId]);

  useEffect(() => {
    handleFollowingList(auth?.userId);
  }, [props.isOtherProfileStatus, triggerEffect, auth?.userId]);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setValue((v) => (v >= 100 ? 0 : v + 10));
    }, 500);

    return () => clearInterval(interval);
  }, []);

  const getLensUserId = async (otherUserID: any) => {
    try {
      const resp = await getId({
        id: props.isOtherProfile ? otherUserID : auth?.userData?.uid,
      });
      // console.log({ resp });
      if (resp) {
        // console.log({ resp });

        setLensId(resp?.lens_code);
        setWalletId(resp?.wallet_id);
        setLensUser(resp?.lens_id);
        setLensUserLocanName(resp?.lens_id?.split('/')[1] || '');
      }
    } catch (error) {
      console.log({ error });
    }
  };

  useEffect(() => {
    getLensUserId(props?.otherUserID);
  }, [props, props?.otherUserID, auth?.userId]);

  function copyProfileLink(id: string) {
    const url = `https://www.amuzn.app/profile/amuzn/${id}`;
    navigator.clipboard
      .writeText(url)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy link: ", err);
      });
  }

  const [activeIcon, setActiveIcon] = useState("amuzn");
  const toggleIcon = () => {
    setActiveIcon(activeIcon === "amuzn" ? "lens" : "amuzn");
  };

  // Use Google Maps context instead of loading the script directly
  const { isLoaded, placesService } = useGoogleMaps();

  useEffect(() => {
    handelProfileComplete();

    // When the Google Maps API is loaded via the context, set up the autocomplete service
    if (isLoaded && placesService && !placesServiceLoaded.current) {
      placesServiceLoaded.current = true;
      autocompleteService.current = placesService;
    }
  }, [isLoaded, placesService]);

  // Handle location input change and fetch place predictions
  const handleLocationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocationInput(value);
    setLocation(value);

    if (value.trim() === "") {
      setPlacePredictions([]);
      setShowPlacesDropdown(false);
      return;
    }

    if (autocompleteService.current && placesServiceLoaded.current) {
      autocompleteService.current.getPlacePredictions(
        {
          input: value,
          types: ["(cities)"],
        },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setPlacePredictions(predictions);
            setShowPlacesDropdown(true);
          } else {
            setPlacePredictions([]);
            setShowPlacesDropdown(false);
          }
        }
      );
    }
  };

  // Select a place from the dropdown
  const selectPlace = (place: google.maps.places.AutocompletePrediction) => {
    setLocation(place.description);
    setLocationInput(place.description);
    setShowPlacesDropdown(false);
  };

  // ************************** report on post  ***************************
  const [isResonPost, setIsResonPost] = useState(false);
  const [reportReason, setReportReason] = useState("");
  const [report, setReport] = useState("");
  const [isCheck, setIsCheck] = useState(Array(0).fill(false));

  const handleIsCheck = (id: number) => {
    const newState = [...isCheck];
    newState[id] = !newState[id]; // Toggle the specific icon's state
    setIsCheck(newState);
  };

  const handlePostReport = async () => {
    // console.log(reportReason, report);

    if (reportReason && report) {
      const resp = await ReportManager.getInstance().reportPost({
        description: report,
        reason: ReportingReason.Fraud,
        sub_reason: FraudSubreason.Scam,
        post_id: props.cardData.id,
        reported_user_id: auth.userId,
      });
      // console.log(resp);
      if (resp == "success") {
        // alert("Your report has been submitted successfully!");
        setIsResonPost(false);
      }
    }
    // setIsOpen(false);
  };

  // initialize chat
  const initChat = async () => {
    await ChatManager.getInstance().CreateChatIfNotExists({
      fromProfile: auth.userId,
      fromProfileName: auth.userData?.profile_name || "",
      toProfile: props.otherUserID,
      toProfileName: profile?.profile_name || "",
    });
  };

  const router = useRouter();

  const handleMessageClick = async () => {
    router.push(`${window.location.pathname}?sidebar=chat`, { scroll: false });
    window.dispatchEvent(new Event("openSidebar")); // Ensure sidebar opens
    await initChat();
  };

  return (
    <>
      <div
        className="text-primary px-3 shadow-2xl"
        style={{
          boxShadow: `var(--sds-size-depth-0) var(--sds-size-depth-100) var(--sds-size-depth-100) var(--sds-size-depth-negative-100) var(--sds-color-black-100)`,
        }}
      >
        <div className="row mb-3 gap-3">
          <div className="flex flex-row  gap-2">
            {props.isOtherProfile && (
              <div onClick={() => window.history.back()} className="">
                <ChevronLeft className="mt-2 cursor-pointer max-md:hidden" size={30} />
                <ChevronLeft className="mt-2 cursor-pointer md:hidden -mx-[6px]" size={24} />
              </div>
            )}
            <div
              onClick={() => props.isOtherProfile && setIsOpenViewPhoto(true)}
              className=" relative"
            >
              <img
                src={generateFileUrl(profile?.avatar) || "/assets/profileAvatar.svg"}
                alt=""
                className="w-[120px] h-[120px] min-w-[120px] min-h-[120px] max-md:w-[74px] max-md:h-[74px] max-md:min-w-[74px] max-md:min-h-[74px]  rounded-full object-cover"
                style={{
                  border: "4px solid",
                  borderColor: bgColor,
                }}
              />
              {!props.isOtherProfile && (
                <div className=" absolute bottom-0 right-1 w-7 h-7 row justify-center items-center rounded-full bg-white border-2 border-[#DEDEDE]">
                  <Edit2
                    color={bgColor}
                    className="cursor-pointer h-4 "
                    onClick={() => !props.isOtherProfile && setIsOpenEditPhoto(true)}
                    strokeWidth="3px"
                  />
                </div>
              )}
            </div>
          </div>
          <div className="w-full">
            <div className=" row justify-between">
              <div>
                <div>
                  <p className="font-bold text-xl max-md:text-base">
                    {profile?.profile_name || "Profile Name*"}
                    {/* {lensId || "lens id*"} */}
                  </p>
                  <p className="text-[#616770] -mt-[4px] max-md:-mt-[6px] max-md:text-[12px]">
                    {profile?.location || "location*"}
                  </p>
                </div>
              </div>
              <div className="flex flex-col justify-between items-end">
                <div className="pr-0 hidden row gap-3">
                  <MoreHorizontal className=" cursor-pointer md:hidden" onClick={toggleSheet} />

                  {!props.isOtherProfile ? (
                    <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                      <DropdownMenuTrigger asChild>
                        <MoreHorizontal className=" cursor-pointer max-md:hidden" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-60 rounded-3xl">
                        <DropdownMenuLabel
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          onClick={() => {
                            handleOpenChangeCategories();
                            setIsDropdownOpen(false);
                          }}
                        >
                          Categories request change
                        </DropdownMenuLabel>
                        <DropdownMenuLabel
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          // onClick={() => copyProfileLink(auth?.userData?.uid)}
                          onClick={() => {
                            setIsOpenShare(true);
                            setIsDropdownOpen(false);
                          }}
                        >
                          Share Profile
                        </DropdownMenuLabel>
                        <DropdownMenuLabel
                          className="text-center font-normal text-base cursor-pointer"
                          onClick={() => {
                            setIsOpenDelete(true);
                            setIsDropdownOpen(false);
                          }}
                        >
                          Delete profile
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                      <DropdownMenuTrigger asChild>
                        <MoreHorizontal className=" cursor-pointer max-md:hidden" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-60 rounded-3xl">
                        <DropdownMenuLabel
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          // onClick={() => copyProfileLink(props.otherUserID)}
                          onClick={() => {
                            setIsOpenShare(true);
                            setIsDropdownOpen(false);
                          }}
                        >
                          Share Profile
                        </DropdownMenuLabel>
                        <DropdownMenuLabel
                          className="text-center font-normal text-base cursor-pointer"
                          // onClick={() => setIsOpenDelete(true)}

                          onClick={() => {
                            if (auth.isLogin) {
                              setIsDropdownOpen(false);
                              setIsResonPost(true);
                            } else {
                              setIsSigninOpen(true);
                            }
                          }}
                        >
                          Report Profile
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                  {!props.isOtherProfile && (
                    <Edit2
                      color={bgColor}
                      className="cursor-pointer max-md:h-[20px]"
                      onClick={() => {
                        // Always start with "Visible to all" section when opening the modal
                        setToggleVisibleAll(false);

                        // Reset loading and success states
                        setIsPersonalInfoSaving(false);
                        setPersonalInfoSaveSuccess(false);

                        // Initialize form values from profile data
                        setProfileName(profile?.profile_name || "");
                        setLocation(profile?.location || "");
                        setLocationInput(profile?.location || "");
                        setHashtags(profile?.languages || []);
                        setFistName(profile?.full_name?.split(" ")[0] || "");
                        setLastName(profile?.full_name?.split(" ")[1] || "");
                        setDob(profile?.date_of_birth || "");

                        // Open the modal
                        setIsOpen(true);
                      }}
                    />
                  )}
                </div>
              </div>
            </div>

            <div className=" row justify-between max-md:-mt-1">
              <div className="row gap-3">
                <div
                  className=" cursor-pointer"
                  onClick={() => {
                    props.setSelectedTabs("Profiles"),
                      props.setSelectedProfileTabs("Following"),
                      props.setSelectedTabsStatus((e: boolean) => !e);
                  }}
                >
                  <p className="font-bold max-md:text-sm">
                    {(followingList && followingList.length) || 0}
                  </p>
                  <p className="text-[#616770] -mt-[4px] max-md:-mt-[6px]  max-md:text-[12px]">
                    Following
                  </p>
                </div>{" "}
                <div
                  className=" cursor-pointer"
                  onClick={() => {
                    props.setSelectedTabs("Profiles"),
                      props.setSelectedProfileTabs("Followers"),
                      props.setSelectedTabsStatus((e: boolean) => !e);
                  }}
                >
                  <p className="font-bold max-md:text-sm">
                    {(followersList && followersList.length) || 0}
                  </p>
                  <p className="text-[#616770] -mt-[4px] max-md:-mt-[6px]  max-md:text-[12px]">
                    Followers
                  </p>
                </div>
              </div>

              <div className="flex flex-col justify-between items-end">
                <div className="flex flex-col items-center p-0">
                  <div className="rounded-full p-0 flex items-center border">
                    {/* AMuzn Button */}
                    <button
                      onClick={() => setActiveIcon("amuzn")}
                      className={`flex items-center justify-center px-4 py-2 max-md:py-1 rounded-full transition-all duration-300  max-w-[55px] w-[55px] ${
                        activeIcon === "amuzn"
                          ? "grayscale-0 text-black border"
                          : "grayscale text-gray-600"
                      }`}
                      style={activeIcon === "amuzn" ? { backgroundColor: `${bgColor}20` } : {}}
                    >
                      <img
                        src="/assets/logo/Default.png"
                        alt=""
                        height={17}
                        className="items-center h-[17px] mix-blend-multiply"
                      />
                    </button>
                    {/* Lens Button */}
                    {lensId && (
                      <Link href={`/profile/lens/${lensUserLocanName}`}>
                        <button
                          onClick={() => setActiveIcon("lens")}
                          className={`flex items-center justify-center px-4 py-2 max-md:py-1 rounded-full transition-all duration-300 max-w-[55px] w-[55px] ${
                            activeIcon === "lens"
                              ? "grayscale-0 text-black border"
                              : "grayscale text-gray-600"
                          }`}
                          style={activeIcon === "lens" ? { backgroundColor: `${bgColor}20` } : {}}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28"
                            height="17"
                            fill={activeIcon === "lens" ? "black" : "currentColor"}
                            viewBox="0 0 28 17"
                          >
                            <g id="Group 3">
                              <path
                                id="Subtract"
                                fill={activeIcon === "lens" ? "black" : "currentColor"}
                                fillOpacity={activeIcon === "lens" ? "1" : "0.5"}
                                fillRule="evenodd"
                                d="M18.957 5.217a4.76 4.76 0 0 1 3.25-1.29 5.066 5.066 0 0 1 5.063 5.068c0 2.422-2.396 4.493-2.996 4.97-2.801 2.23-6.45 3.535-10.389 3.535s-7.588-1.305-10.39-3.536C2.9 13.488.5 11.414.5 8.994c0-2.799 2.267-5.067 5.062-5.067 1.26 0 2.39.493 3.25 1.29l.09-.044C9.098 2.561 11.222.5 13.884.5s4.786 2.06 4.984 4.673zm1.01 5.04c.386.384.661.848.815 1.355h.003a.41.41 0 0 1-.327.514.41.41 0 0 1-.456-.29 2.4 2.4 0 0 0-.606-1.004 2.44 2.44 0 0 0-1.736-.719q-.038 0-.075.004l-.075.003a1.076 1.076 0 1 1-1.45.586q-.072.06-.14.126c-.286.286-.49.63-.605 1.005a.407.407 0 0 1-.457.29.41.41 0 0 1-.327-.515c.154-.508.43-.97.814-1.356a3.25 3.25 0 0 1 2.312-.957c.871 0 1.695.34 2.31.957m-10-.137.075-.003.075-.004c.657 0 1.273.256 1.736.719.286.286.49.63.606 1.005a.41.41 0 0 0 .456.29.41.41 0 0 0 .327-.515 3.23 3.23 0 0 0-.814-1.356 3.25 3.25 0 0 0-2.311-.957c-.872 0-1.696.34-2.312.957-.384.385-.66.848-.813 1.356a.41.41 0 0 0 .327.514.407.407 0 0 0 .456-.29 2.43 2.43 0 0 1 .745-1.13 1.076 1.076 0 1 0 2.077.388c0-.432-.259-.803-.627-.974zm3.922 3.93a2.45 2.45 0 0 0 1.634-.626.415.415 0 0 1 .551-.007c.**************.01.6a3.25 3.25 0 0 1-4.391 0 .405.405 0 0 1 .01-.6.41.41 0 0 1 .552.007 2.44 2.44 0 0 0 1.633.627"
                                clipRule="evenodd"
                              ></path>
                            </g>
                          </svg>
                        </button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {!props.isOtherProfile && (
          <div className="w-full prgress">
            <p className="text-titleLabel mb-1 text-lg max-md:text-sm">{progress}% complete</p>
            <Progress
              classNames={{
                track: "drop-shadow-md border border-default h-[10px]",
                indicator: `[background-color:${bgColor}] h-[10px]`, // Tailwind arbitrary style
                label: "tracking-wider font-medium text-default-600",
                value: "text-foreground/60",
              }}
              style={
                {
                  "--active-bg-color-indicator": bgColor,
                } as React.CSSProperties
              } // Set CSS variable
              color="default"
              // radius="sm"
              // size="sm"
              value={progress}
            />
          </div>
        )}

        {props.isOtherProfile && (
          <div>
            {followingCheck ? (
              <div className="row w-full gap-3">
                <Badge
                  className=" btn-xs h-[40px] w-full py-3 border-primary btn"
                  variant="outline"
                  style={{ fontSize: "16px" }}
                  onClick={handleMessageClick}
                >
                  Message
                </Badge>
                <Badge
                  className=" btn-xs h-[40px] w-full  py-3 border-primary btn"
                  variant="outline"
                  style={{ fontSize: "16px" }}
                  onClick={() => handleUnFollow(props?.otherUserID)}
                >
                  {loadingOnClick[props?.otherUserID] ? (
                    <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  ) : (
                    "Unfollow"
                  )}
                </Badge>
              </div>
            ) : (
              <div className="row w-full gap-3">
                <Badge
                  className=" btn-xs h-[40px] w-full py-3 border-primary btn"
                  variant="outline"
                  style={{ fontSize: "16px" }}
                  onClick={handleMessageClick}
                >
                  Message
                </Badge>
                <Badge
                  className=" btn-xs h-[40px] w-full  py-3 text-white btn"
                  // variant="outline"
                  style={{ fontSize: "16px" }}
                  onClick={() => handleFollow(props?.otherUserID)}
                >
                  {loadingOnClick[props?.otherUserID] ? (
                    <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  ) : !auth.isLogin ? (
                    "Follow"
                  ) : (
                    "Follow"
                  )}
                </Badge>
              </div>
            )}
          </div>
        )}
        {/* data */}
        <ProfileInfoAbout
          isOtherProfile={props.isOtherProfile}
          bgColor={bgColor}
          isToggle={isToggle}
          setIsToggle={setIsToggle}
          about={profile?.about_me || ""}
          onClickAction={setToggle} // Pass function to child
        />

        {!isToggle && (
          <ProfileInfoPersonalMotto
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            personalMotto={profile?.personal_moto || ""}
            onClickAction={setToggle} // Pass function to child
          />
        )}

        {!isToggle && (
          <ProfileInfoHashtags
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            hashtags={profile?.hashtags || ""}
            onClickAction={setToggle} // Pass function to child
          />
        )}

        {!isToggle && (
          <ProfileInfoSocialMedia
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            facebookLink={profile?.facebookLink || ""}
            instagramLink={profile?.instagramLink || ""}
            twitterLink={profile?.twitterLink || ""}
            websiteLink={profile?.websiteLink || ""}
            youtubeLink={profile?.youtubeLink || ""}
            onClickAction={setToggle} // Pass function to child
          />
        )}

        {!isToggle && lensId && (
          <ProfileInfoConnAccount
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            email={profile?.email || ""}
            onClickAction={setToggle} // Pass function to child
            connectedAccountId={lensId}
            wallet_id={walletId}
            lens_id={lensUser}
          />
        )}

        {!isToggle && !props.isOtherProfile && (
          <ProfileInfoVerifyEmail
            isOtherProfile={props.isOtherProfile}
            bgColor={bgColor}
            email={profile?.email || ""}
            onClickAction={setToggle} // Pass function to child
          />
        )}

        {/* <div>
          <p>Confirm Your Email</p>
          <p>email sent for confirmation</p>
          <p>email confirmed</p>
        </div> */}
      </div>

      {/* About Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={setIsOpen}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div
                      onClick={() => {
                        setIsOpen(false), setToggleVisibleAll(false);
                      }}
                      className=" cursor-pointer"
                    >
                      <X />
                    </div>
                    <p className="font-bold text-primary">Edit Personal Info</p>

                    {!toggleVisibleAll ? (
                      <div
                        className={`flex items-center ${
                          isPersonalInfoSaving
                            ? "text-primary cursor-wait"
                            : personalInfoSaveSuccess
                              ? "text-green-500 cursor-default"
                              : profileName && location && hashtags
                                ? "text-primary cursor-pointer"
                                : "text-borderColor cursor-not-allowed"
                        }`}
                        onClick={
                          profileName &&
                          location &&
                          hashtags &&
                          !isPersonalInfoSaving &&
                          !personalInfoSaveSuccess
                            ? handleSubmit
                            : undefined
                        }
                      >
                        {isPersonalInfoSaving ? (
                          <div className="flex items-center">
                            <svg
                              className="animate-spin h-4 w-4 mr-1"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Saving
                          </div>
                        ) : personalInfoSaveSuccess ? (
                          <div className="flex items-center">
                            <svg
                              className="h-4 w-4 mr-1"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            Saved
                          </div>
                        ) : (
                          "Save"
                        )}
                      </div>
                    ) : (
                      <div
                        className={`flex items-center ${
                          isPersonalInfoSaving
                            ? "text-primary cursor-wait"
                            : personalInfoSaveSuccess
                              ? "text-green-500 cursor-default"
                              : firstName && lastName && dob
                                ? "text-primary cursor-pointer"
                                : "text-borderColor cursor-not-allowed"
                        }`}
                        onClick={
                          firstName &&
                          lastName &&
                          dob &&
                          !isPersonalInfoSaving &&
                          !personalInfoSaveSuccess
                            ? handleSubmitVisibleToAll
                            : undefined
                        }
                      >
                        {isPersonalInfoSaving ? (
                          <div className="flex items-center">
                            <svg
                              className="animate-spin h-4 w-4 mr-1"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Saving
                          </div>
                        ) : personalInfoSaveSuccess ? (
                          <div className="flex items-center">
                            <svg
                              className="h-4 w-4 mr-1"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            Saved
                          </div>
                        ) : (
                          "Save"
                        )}
                      </div>
                    )}
                  </div>

                  {!toggleVisibleAll ? (
                    <div className="max-md:text-start">
                      <p className="mt-6 font-bold text-start">Visible to all</p>
                      <div className="grid w-full items-center gap-1.5 mt-3">
                        <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                          Profile name*
                        </Label>
                        <Input
                          placeholder="Profile name"
                          className={`resize-none h-[40px] outline-none text-lg text-primary ${
                            isPersonalInfoSaving || personalInfoSaveSuccess
                              ? "opacity-70 cursor-not-allowed"
                              : ""
                          }`}
                          value={profileName}
                          onChange={(e) => setProfileName(e.target.value.replace(/[-/]/g, ""))}
                          disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                          aria-label="Profile name"
                        />
                      </div>

                      {/* Languages */}
                      <div className="grid w-full items-center gap-1.5 my-3">
                        <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                          Languages*
                        </Label>
                        <div className="border-2 rounded-lg p-3">
                          <div className="flex flex-wrap gap-2 mb-2 ">
                            {hashtags.map((tag, index) => (
                              <span
                                key={index}
                                className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                              >
                                {tag}
                                <button
                                  type="button"
                                  onClick={() =>
                                    !isPersonalInfoSaving &&
                                    !personalInfoSaveSuccess &&
                                    removeHashtag(tag)
                                  }
                                  className={`text-primary pt-[2px] pl-2 ${
                                    isPersonalInfoSaving || personalInfoSaveSuccess
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                  }`}
                                  disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                                >
                                  <X size={15} strokeWidth="3px" />
                                </button>
                              </span>
                            ))}
                          </div>
                          <div className="relative">
                            <input
                              type="text"
                              placeholder="Enter languages here"
                              className={`w-full pt-2 border-none border-gray-300 rounded-md outline-none ${
                                isPersonalInfoSaving || personalInfoSaveSuccess
                                  ? "opacity-70 cursor-not-allowed"
                                  : ""
                              }`}
                              onKeyDown={
                                !isPersonalInfoSaving && !personalInfoSaveSuccess
                                  ? addHashtag
                                  : undefined
                              }
                              value={languageInput}
                              onChange={
                                !isPersonalInfoSaving && !personalInfoSaveSuccess
                                  ? handleLanguageInputChange
                                  : undefined
                              }
                              disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                            />
                            {!isPersonalInfoSaving &&
                              !personalInfoSaveSuccess &&
                              showLanguageDropdown &&
                              filteredLanguages.length > 0 && (
                                <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto">
                                  {filteredLanguages.map((language, index) => (
                                    <div
                                      key={index}
                                      className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                                      onClick={() => selectLanguage(language.name)}
                                    >
                                      {language.name}
                                    </div>
                                  ))}
                                </div>
                              )}
                          </div>
                        </div>
                      </div>

                      <div className="grid w-full items-center gap-1.5 my-3">
                        <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                          Location *
                        </Label>
                        <div className="relative">
                          <Input
                            ref={locationInputRef}
                            placeholder="Location"
                            className={`resize-none h-[40px] outline-none text-lg text-primary ${
                              isPersonalInfoSaving || personalInfoSaveSuccess
                                ? "opacity-70 cursor-not-allowed"
                                : ""
                            }`}
                            value={locationInput}
                            onChange={
                              !isPersonalInfoSaving && !personalInfoSaveSuccess
                                ? handleLocationInputChange
                                : undefined
                            }
                            disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                            aria-label="location"
                          />
                          <LocationDropdown
                            predictions={placePredictions}
                            onSelect={selectPlace}
                            inputRef={locationInputRef}
                            isVisible={
                              !isPersonalInfoSaving &&
                              !personalInfoSaveSuccess &&
                              showPlacesDropdown &&
                              placePredictions.length > 0
                            }
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="max-md:text-start">
                      <p className="mt-6 font-bold text-start">Visible to you only</p>
                      <p className="mt-2 text-start text-subtitle text-sm">
                        This section is required in order to sell or buy services
                      </p>
                      <div className="grid w-full items-center gap-1.5 mt-3">
                        <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                          First name*
                        </Label>
                        <Input
                          placeholder="First name"
                          className={`resize-none h-[40px] outline-none text-lg text-primary ${
                            isPersonalInfoSaving || personalInfoSaveSuccess
                              ? "opacity-70 cursor-not-allowed"
                              : ""
                          }`}
                          value={firstName}
                          onChange={
                            !isPersonalInfoSaving && !personalInfoSaveSuccess
                              ? (e) => setFistName(e.target.value)
                              : undefined
                          }
                          disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                          aria-label="First name"
                        />
                      </div>
                      <div className="grid w-full items-center gap-1.5 mt-3">
                        <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                          Last name*
                        </Label>
                        <Input
                          placeholder="Last name"
                          className={`resize-none h-[40px] outline-none text-lg text-primary ${
                            isPersonalInfoSaving || personalInfoSaveSuccess
                              ? "opacity-70 cursor-not-allowed"
                              : ""
                          }`}
                          value={lastName}
                          onChange={
                            !isPersonalInfoSaving && !personalInfoSaveSuccess
                              ? (e) => setLastName(e.target.value)
                              : undefined
                          }
                          disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                          aria-label="Last name"
                        />
                      </div>

                      <p className="mt-2 text-start text-subtitle text-sm">
                        First Name and Last Name is a legal name and it should match your bank info,
                        also it is not viewable by other users.
                      </p>

                      <div className="grid w-full items-center gap-1.5 mt-3">
                        <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                          Date of birth*
                        </Label>
                        <Input
                          type="date"
                          placeholder="dob"
                          className={`resize-none h-[40px] outline-none text-lg text-primary ${
                            isPersonalInfoSaving || personalInfoSaveSuccess
                              ? "opacity-70 cursor-not-allowed"
                              : ""
                          }`}
                          value={dob}
                          onChange={
                            !isPersonalInfoSaving && !personalInfoSaveSuccess
                              ? (e) => {
                                  setDob(e.target.value);
                                }
                              : undefined
                          }
                          disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                          aria-label="Select your date of birth"
                        />
                      </div>
                      <p className="mt-2 text-start text-subtitle text-sm">
                        Not viewable by other users.
                      </p>
                      <Button
                        variant="outline"
                        className="w-full h-[48px] mt-6 rounded-xl font-medium border-gray-300 text-gray-500 hover:bg-gray-100 transition-colors"
                        onClick={() => {
                          setIsOpen(false);
                          setToggleVisibleAll(false);
                        }}
                      >
                        Complete later
                      </Button>
                    </div>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Delete Modal */}

      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenDelete}
          placement="auto"
          onOpenChange={setIsOpenDelete}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    {profile?.profile_name || "Profile Name*"}, we're sorry to see you go. Just a
                    quick reminder, closing your account means you'll lose all info. Are you sure
                    you would like to delete your profile?
                  </p>
                  <div className="px-12">
                    <Badge
                      className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-5"
                      variant="outline"
                      onClick={() => {
                        setIsOpenDelete(false), setIsOpenDeleteReason(true);
                      }}
                    >
                      Yes, delete
                    </Badge>
                    <Badge
                      className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-3"
                      variant="outline"
                      onClick={() => setIsOpenDelete(false)}
                    >
                      No, cancel
                    </Badge>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Delete open other Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenOtherDelete}
          placement="auto"
          onOpenChange={setIsOpenOtherDelete}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    You can not delete your profile while you have open orders.
                  </p>
                  <div className="px-12">
                    <Badge
                      className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-5"
                      variant="outline"
                      onClick={() => setIsOpenOtherDelete(false)}
                    >
                      Ok
                    </Badge>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Delete Reson Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenDeleteReason}
          placement="auto"
          onOpenChange={setIsOpenDeleteReason}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div onClick={() => setIsOpenDeleteReason(false)} className=" cursor-pointer">
                      <X />
                    </div>
                    <p className="font-bold text-primary">Delete Profile</p>
                    <p
                      className={
                        deleteReason
                          ? "font-bold text-primary cursor-pointer"
                          : "font-bold text-borderColor"
                      }
                      onClick={async () => {
                        try {
                          const resp = await deleteUserDetails({
                            user_id: auth?.userId,
                            comment: deleteReason,
                          });

                          if (resp.success) {
                            // Remove user data from localStorage
                            localStorage.removeItem("user");

                            // Reload the page
                            window.location.reload();
                          } else {
                            console.error("Delete failed:", resp.message);
                          }
                        } catch (error) {
                          console.error("Error deleting user details:", error);
                        }
                      }}
                    >
                      Confirm
                    </p>
                  </div>

                  <div>
                    <p className="mt-3 text-[16px] text-primary">
                      {profile?.profile_name || "Profile Name*"}, we're sorry to see you go.
                    </p>
                    <p className="mt-2 text-primary">
                      Just a quick reminder, closing your account means you'll lose all info. Are
                      you sure you would like to delete your profile?
                    </p>
                    <div className="grid w-full items-center gap-1.5 mt-6">
                      <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                        Reason*
                      </Label>
                      <Textarea
                        placeholder="Please give your reason"
                        className="resize-none h-40 outline-none text-lg text-primary"
                        value={deleteReason}
                        onChange={(e) => setDeleteReason(e.target.value)}
                      />
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Change Categories Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenChangeCategories}
          placement="auto"
          onOpenChange={setIsOpenChangeCategories}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content py-3 mx-auto">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="flex items-center justify-between border-b pb-3 mb-2">
                    <div
                      onClick={() => setIsOpenChangeCategories(false)}
                      className="p-1.5 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                    >
                      <X size={20} />
                    </div>
                    <h3 className="font-bold text-lg text-primary">Change Categories</h3>
                    <div
                      className={`p-1.5 rounded-full transition-colors ${
                        isSavingCategories
                          ? "text-primary cursor-wait"
                          : categorySaveSuccess
                            ? "text-green-500 cursor-default"
                            : mainCat && secondCat
                              ? "text-primary hover:bg-gray-100 cursor-pointer"
                              : "text-gray-300 cursor-not-allowed"
                      }`}
                      onClick={
                        mainCat && secondCat && !isSavingCategories && !categorySaveSuccess
                          ? handleSubmitChangeCategory
                          : undefined
                      }
                    >
                      {isSavingCategories ? (
                        <svg
                          className="animate-spin"
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M12 22c5.523 0 10-4.477 10-10h-2c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8v2M12 6v-4" />
                        </svg>
                      ) : categorySaveSuccess ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M20 6 9 17l-5-5" />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M20 6 9 17l-5-5" />
                        </svg>
                      )}
                    </div>
                  </div>

                  <div>
                    {/* <p className="mt-4 text-[28px] text-primary text-3xl max-md:text-xl font-bold text-center">
                      Change categories
                    </p> */}

                    <DropdownMenu
                      open={isMainCategoryDropdownOpen}
                      onOpenChange={setIsMainCategoryDropdownOpen}
                    >
                      <DropdownMenuTrigger asChild>
                        <div className="w-full mt-6">
                          <Label
                            htmlFor="main-category"
                            className="text-base font-semibold text-titleLabel mb-2 block"
                          >
                            Main Category*
                          </Label>
                          <div className="relative">
                            <Input
                              id="main-category"
                              placeholder="Select main category"
                              className={`resize-none h-[48px] pl-${
                                mainCat ? "10" : "4"
                              } pr-10 outline-none text-lg text-primary rounded-xl border-2 border-gray-200 focus:border-primary transition-colors cursor-pointer`}
                              value={mainCat}
                              readOnly
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <path d="m6 9 6 6 6-6" />
                              </svg>
                            </div>
                            {mainCat && (
                              <div
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center"
                                style={{ opacity: mainCat ? 1 : 0 }}
                              >
                                {Object.entries(themes).map(([themeName, themeProperties]: any) => {
                                  if (themeProperties.title === mainCat) {
                                    return (
                                      <img
                                        key={themeName}
                                        src={themeProperties.icon}
                                        alt=""
                                        className="w-5 h-5"
                                      />
                                    );
                                  }
                                  return null;
                                })}
                              </div>
                            )}
                          </div>
                        </div>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-[calc(100vw-48px)] max-w-[400px] rounded-xl shadow-lg border border-gray-200 p-2 max-h-[320px] overflow-y-auto"
                        sideOffset={4}
                      >
                        <div className="py-2">
                          <h4 className="text-sm font-medium text-gray-500 px-3 mb-2">
                            Select Main Category
                          </h4>
                          <div className="space-y-1">
                            {Object.entries(themes).map(
                              ([themeName, themeProperties]: any, index) => {
                                if (themeProperties.title === "My Feed") return null;

                                const isSelected = mainCat === themeProperties.title;

                                return (
                                  <div
                                    key={index}
                                    className={`
                                      flex items-center gap-3 px-3 py-2.5 rounded-xl transition-colors
                                      ${
                                        isSelected
                                          ? `bg-primary cursor-pointer text-white font-medium`
                                          : `hover:bg-gray-100 text-gray-700 cursor-pointer bg-[#F2F2F2]`
                                      }
                                    `}
                                    onClick={() => {
                                      handleItemClickCategory(index);
                                      setIsMainCategoryDropdownOpen(false);
                                    }}
                                  >
                                    <div
                                      className={`
                                      w-8 h-8 rounded-full flex items-center justify-center
                                      ${isSelected ? `` : `bg-gray-100`}
                                    `}
                                    >
                                      <img
                                        src={themeProperties?.icon}
                                        alt=""
                                        className={`w-5 h-5 ${
                                          isSelected ? "grayscale invert" : "opacity-70"
                                        }`}
                                      />
                                    </div>
                                    <span className="flex-1">{themeProperties.title}</span>
                                    {isSelected && (
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="white"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      >
                                        <path d="M20 6 9 17l-5-5" />
                                      </svg>
                                    )}
                                  </div>
                                );
                              }
                            )}
                          </div>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu
                      open={isSecondaryCategoryDropdownOpen}
                      onOpenChange={setIsSecondaryCategoryDropdownOpen}
                    >
                      <DropdownMenuTrigger asChild>
                        <div className="w-full mt-4">
                          <Label
                            htmlFor="secondary-category"
                            className="text-base font-semibold text-titleLabel mb-2 block"
                          >
                            Secondary Category*
                          </Label>
                          <div className="relative">
                            <Input
                              id="secondary-category"
                              placeholder="Select secondary category"
                              className={`resize-none h-[48px] pl-${
                                secondCat ? "10" : "4"
                              } pr-10 outline-none text-lg text-primary rounded-xl border-2 border-gray-200 focus:border-primary transition-colors cursor-pointer`}
                              value={secondCat}
                              readOnly
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <path d="m6 9 6 6 6-6" />
                              </svg>
                            </div>
                            {secondCat && (
                              <div
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center"
                                style={{ opacity: secondCat ? 1 : 0 }}
                              >
                                {Object.entries(themes).map(([themeName, themeProperties]: any) => {
                                  if (themeProperties.title === secondCat) {
                                    return (
                                      <img
                                        key={themeName}
                                        src={themeProperties.icon}
                                        alt=""
                                        className="w-5 h-5"
                                      />
                                    );
                                  }
                                  return null;
                                })}
                              </div>
                            )}
                          </div>
                        </div>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-[calc(100vw-48px)] max-w-[400px] rounded-xl shadow-lg border border-gray-200 p-2 max-h-[320px] overflow-y-auto"
                        sideOffset={4}
                      >
                        <div className="py-2">
                          <h4 className="text-sm font-medium text-gray-500 px-3 mb-2">
                            Select Secondary Category
                          </h4>
                          <div className="space-y-1">
                            {Object.entries(themes).map(
                              ([themeName, themeProperties]: any, index) => {
                                if (themeProperties.title === "My Feed") return null;

                                const isSelected = secondCat === themeProperties.title;

                                return (
                                  <div
                                    key={index}
                                    className={`
                                      flex items-center gap-3 px-3 py-2.5 rounded-xl transition-colors
                                      ${
                                        isSelected
                                          ? `bg-primary cursor-pointer text-white font-medium`
                                          : `hover:bg-gray-100 text-gray-700 cursor-pointer bg-[#F2F2F2]`
                                      }
                                    `}
                                    onClick={() => {
                                      handleItemClickCategorySecond(index);
                                      setIsSecondaryCategoryDropdownOpen(false);
                                    }}
                                  >
                                    <div
                                      className={`
                                      w-8 h-8 rounded-full flex items-center justify-center
                                      ${isSelected ? `` : `bg-gray-100`}
                                    `}
                                    >
                                      <img
                                        src={themeProperties?.icon}
                                        alt=""
                                        className={`w-5 h-5 ${
                                          isSelected ? "grayscale invert" : "opacity-70"
                                        }`}
                                      />
                                    </div>
                                    <span className="flex-1">{themeProperties.title}</span>
                                    {isSelected && (
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="white"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      >
                                        <path d="M20 6 9 17l-5-5" />
                                      </svg>
                                    )}
                                  </div>
                                );
                              }
                            )}
                          </div>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <button
                      className={`w-full h-[48px] mt-6 rounded-xl font-medium transition-colors relative ${
                        isSavingCategories
                          ? "bg-primary text-white"
                          : categorySaveSuccess
                            ? "bg-green-500 text-white"
                            : mainCat && secondCat
                              ? "bg-primary text-white hover:bg-primary/90"
                              : "bg-gray-200 text-gray-400 cursor-not-allowed"
                      }`}
                      onClick={handleSubmitChangeCategory}
                      disabled={!mainCat || !secondCat || isSavingCategories || categorySaveSuccess}
                    >
                      {isSavingCategories ? (
                        <div className="flex items-center justify-center">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Saving...
                        </div>
                      ) : categorySaveSuccess ? (
                        <div className="flex items-center justify-center">
                          <svg
                            className="w-5 h-5 mr-2"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Saved Successfully
                        </div>
                      ) : (
                        "Save Changes"
                      )}
                    </button>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Edit Photo  Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenEditPhoto}
          placement="auto"
          onOpenChange={setIsOpenEditPhoto}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div
                      onClick={() => {
                        setIsOpenEditPhoto(false), setMedia(null);
                      }}
                      className=" cursor-pointer"
                    >
                      <X />
                    </div>
                    <p className="font-bold text-primary">Edit Photo</p>
                    <div
                      className={`flex items-center ${
                        isPhotoSaving
                          ? "text-primary cursor-wait"
                          : photoSaveSuccess
                            ? "text-green-500 cursor-default"
                            : isMedia
                              ? "font-bold text-primary cursor-pointer"
                              : "font-bold text-borderColor cursor-not-allowed"
                      }`}
                      onClick={
                        isMedia && !isPhotoSaving && !photoSaveSuccess
                          ? handleEditAvatarSubmit
                          : undefined
                      }
                    >
                      {isPhotoSaving ? (
                        <div className="flex items-center">
                          <svg
                            className="animate-spin h-4 w-4 mr-1"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Saving
                        </div>
                      ) : photoSaveSuccess ? (
                        <div className="flex items-center">
                          <svg
                            className="h-4 w-4 mr-1"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Saved
                        </div>
                      ) : (
                        "Save"
                      )}
                    </div>
                  </div>
                  <div className="px-12 max-md:px-4">
                    {media ? (
                      <div className="row justify-center mt-6 mb-4">
                        <img
                          src={URL.createObjectURL(media)}
                          alt="Media Preview"
                          className="w-[200px] h-[200px]"
                        />
                      </div>
                    ) : (
                      <div className="row justify-center mt-6 mb-4">
                        <img
                          src={generateFileUrl(profile?.avatar) || "/assets/profileAvatar.svg"}
                          alt=""
                          className="w-[200px] h-[200px]"
                        />
                      </div>
                    )}

                    <label htmlFor="media-upload" className="row gap-4 cursor-pointer">
                      <Badge
                        className=" btn-xs h-[40px] py-4 border-primary w-full"
                        variant="outline"
                        style={{ fontSize: "16px" }}
                      >
                        Upload a Photo
                      </Badge>
                      <input
                        type="file"
                        id="media-upload"
                        className="hidden"
                        accept="image/*,video/*"
                        onChange={handleMediaUpload}
                      />
                    </label>
                    <p className="mt-5 text-lg text-center">
                      Please upload a headshot photo, as required for all content providers.
                    </p>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* view Photo  Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenViewPhoto}
          placement="auto"
          onOpenChange={setIsOpenViewPhoto}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div
                      onClick={() => {
                        setIsOpenViewPhoto(false), setMedia(null);
                      }}
                      className=" cursor-pointer"
                    >
                      <X />
                    </div>
                  </div>
                  <div className="px-12 max-md:px-4">
                    {media ? (
                      <div className="row justify-center mt-6 mb-4">
                        <img
                          src={URL.createObjectURL(media)}
                          alt="Media Preview"
                          className="w-[200px] h-[200px]"
                        />
                      </div>
                    ) : (
                      <div className="row justify-center mt-6 mb-4">
                        <img
                          src={generateFileUrl(profile?.avatar) || "/assets/profileAvatar.svg"}
                          alt=""
                          className="w-[300px] h-[300px]"
                        />
                      </div>
                    )}
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Bottom Icon  */}
      <Sheet open={isOpenSheet} onOpenChange={setIsOpenSheet}>
        <SheetContentNoClose side="bottom" className="bg-transparent border-none p-0">
          {!props.isOtherProfile ? (
            <div className="bg-white rounded-[14px]">
              <button
                className="bg-white text-primary w-full h-[50px] rounded-tr-[14px] rounded-tl-[14px] border-b"
                onClick={() => {
                  setIsOpenSheet(false);
                  handleOpenChangeCategories();
                }}
              >
                Categories request change
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] border-b"
                onClick={() => {
                  setIsOpenSheet(false);
                  setIsOpenShare(true);
                }}
              >
                Share Profile
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] rounded-[14px]"
                onClick={() => {
                  setIsOpenSheet(false);
                  setIsOpenDelete(true);
                }}
              >
                Delete Profile
              </button>
            </div>
          ) : (
            <div className="bg-white rounded-[14px]">
              <button
                className="bg-white text-primary w-full h-[50px] rounded-tr-[14px] rounded-tl-[14px] border-b"
                onClick={() => {
                  setIsOpenSheet(false);
                  setIsOpenShare(true);
                }}
              >
                Share Profile
              </button>
              <button
                className="bg-white text-primary w-full h-[50px] rounded-[14px]"
                onClick={() => {
                  if (auth.isLogin) {
                    setIsOpenSheet(false);
                    setIsResonPost(true);
                  } else {
                    setIsSigninOpen(true);
                  }
                }}
              >
                Report Profile
              </button>
            </div>
          )}
          <div className="mt-4">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px]"
              onClick={() => setIsOpenSheet(false)}
            >
              Cancel
            </button>
          </div>
        </SheetContentNoClose>
      </Sheet>

      <div className="max-md:h-full px-5">
        <Modal
          isDismissable={false}
          isOpen={isSigninOpen}
          placement="auto"
          onOpenChange={setIsSigninOpen}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {() => (
              <>
                <ModalBody>
                  <div
                    className="absolute top-6 left-6 cursor-pointer"
                    onClick={() => {
                      sessionStorage.removeItem("input");
                      sessionStorage.removeItem("openPost");
                      setIsSigninOpen(false);
                    }}
                  >
                    <X />
                  </div>
                  <AuthSignup onClose={() => setIsSigninOpen(false)} />
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Shere Post Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenShare}
          placement="auto"
          onOpenChange={setIsOpenShare}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {() => (
              <>
                <ModalBody>
                  <div>
                    <div>
                      <div className="flex items-center justify-between">
                        <div
                          onClick={() => setIsOpenShare(false)}
                          className="p-1.5 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                        >
                          <X size={20} />
                        </div>
                        <h3 className="font-bold text-lg text-primary">Share Profile</h3>
                        <div className="w-8"></div>
                      </div>

                      <div className="mt-6">
                        <div className="flex items-center justify-between border border-gray-200 rounded-lg p-3 bg-gray-50">
                          <div className="truncate mr-2 text-gray-700">
                            https://www.amuzn.app/profile/amuzn/
                            {profile?.profile_name?.replace(/\s+/g, '-')}
                          </div>
                          <div
                            className={`cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-all ${
                              isCopied ? "bg-green-100 text-green-600" : ""
                            }`}
                            onClick={() => copyProfileLink(profile?.profile_name?.replace(/\s+/g, '-'))}
                          >
                            {isCopied ? <Check size={18} /> : <Copy size={18} />}
                          </div>
                        </div>

                        <p className="text-gray-500 text-sm mt-4 mb-2">Share on social media</p>

                        <div className="grid grid-cols-4 gap-3 mt-2">
                          <a
                            href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                              `https://www.amuzn.app/profile/${
                                props?.otherUserID === "my-profile"
                                  ? auth?.userId
                                  : props?.otherUserID
                              }`
                            )}&text=${encodeURIComponent("Check out this profile on Amuzn!")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Twitter size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Twitter</span>
                          </a>

                          <a
                            href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                              `https://www.amuzn.app/profile/${
                                props?.otherUserID === "my-profile"
                                  ? auth?.userId
                                  : props?.otherUserID
                              }`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Facebook size={24} className="fill-primary border-0 outline-0" />
                            </div>
                            <span className="text-xs">Facebook</span>
                          </a>

                          <div
                            onClick={() => {
                              // Instagram doesn't have a direct share URL, so we'll copy the link to clipboard
                              const profileUrl = `https://www.amuzn.app/profile/${
                                props?.otherUserID === "my-profile"
                                  ? auth?.userId
                                  : props?.otherUserID
                              }`;
                              navigator.clipboard.writeText(profileUrl);
                              setIsCopied(true);
                              setTimeout(() => setIsCopied(false), 2000);
                              // Open Instagram in a new tab
                              window.open("https://www.instagram.com/", "_blank");
                            }}
                            className="flex flex-col items-center justify-center cursor-pointer"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              <Instagram size={24} className="border-0 outline-0" />
                            </div>
                            <span className="text-xs">Instagram</span>
                          </div>

                          {/* WhatsApp share button (replaces Telegram) */}
                          <a
                            href={`https://wa.me/?text=${encodeURIComponent(
                              `Check out this profile on Amuzn! https://www.amuzn.app/profile/${
                                props?.otherUserID === "my-profile"
                                  ? auth?.userId
                                  : props?.otherUserID
                              }`
                            )}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex flex-col items-center justify-center"
                          >
                            <div className="bg-[#EEEEEE] p-3 rounded-full w-fit mb-1">
                              {/* WhatsApp SVG icon */}
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className="fill-primary">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.966-.94 1.164-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.007-.372-.009-.571-.009-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.099 3.2 5.077 4.363.71.306 1.263.489 1.694.625.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/>
                                <path d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.341 4.997l-1.409 5.151 5.283-1.389c1.475.807 3.153 1.238 4.782 1.238h.001c5.514 0 9.997-4.483 9.997-9.997 0-2.669-1.04-5.178-2.929-7.067-1.889-1.889-4.398-2.93-7.069-2.93zm0 17.995c-1.462 0-2.892-.393-4.13-1.137l-.295-.174-3.134.823.837-3.057-.192-.314c-.822-1.346-1.257-2.899-1.257-4.486 0-4.411 3.588-7.999 7.999-7.999 2.137 0 4.146.833 5.656 2.344 1.511 1.511 2.344 3.52 2.344 5.656 0 4.411-3.588 7.999-7.999 7.999z"/>
                              </svg>
                            </div>
                            <span className="text-xs">WhatsApp</span>
                          </a>
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        className="rounded-full w-full mt-6 border-black text-black border-2 py-5 text-base"
                        onClick={() => setIsOpenShare(false)}
                      >
                        Close
                      </Button>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
      {/* Report Post */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isResonPost}
          placement="auto"
          onOpenChange={setIsResonPost}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="">
                    <div className=" relative w-full">
                      <div className="row justify-between mb-5">
                        <X
                          onClick={() => setIsResonPost(false)}
                          className="text-primary cursor-pointer"
                        />
                        <p className="font-bold  text-titleLabel ">Report Post</p>
                        <p className=" opacity-0">Test</p>
                      </div>
                      <div className="row max-md:justify-center  max-md:items-start">
                        <div className="min-w-full pb-5 ">
                          <div className="grid w-full gap-1.5 px-3 ">
                            <div className="grid w-full md:max-w-sm items-center gap-1 mt-4 max-md:text-start">
                              <Label
                                htmlFor="message"
                                className="text-primary text-base font-bold max-md:text-start"
                              >
                                Reason
                              </Label>
                              {resonData.map((item, indexs) => (
                                <div className="grid grid-cols-1 mt-1" key={indexs}>
                                  <div className="row gap-3">
                                    {isCheck[indexs] ? (
                                      <CheckCircle
                                        className="text-primary w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                        size={18}
                                        onClick={() => {
                                          handleIsCheck(indexs), setReportReason(item);
                                        }}
                                      />
                                    ) : (
                                      <Circle
                                        className="text-subtitle w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                        // size={18}
                                        onClick={() => {
                                          handleIsCheck(indexs), setReportReason(item);
                                        }}
                                      />
                                    )}
                                    <p
                                      className={
                                        isCheck[indexs]
                                          ? "text-primary cursor-pointer"
                                          : "text-subtitle cursor-pointer"
                                      }
                                      onClick={() => {
                                        handleIsCheck(indexs), setReportReason(item);
                                      }}
                                    >
                                      {item}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                            <div className="grid items-center gap-1.5 mt-4 max-md:text-start ">
                              <Label htmlFor="message" className="text-primary text-base font-bold">
                                Report Сomment
                              </Label>

                              <Textarea
                                placeholder="Report..."
                                id="message"
                                className="text-primary text-base w-full"
                                value={report}
                                onChange={(e) => setReport(e.target.value)}
                              />
                            </div>
                            <Badge
                              className=" btn-xs btn text-center mt-5 ounded-full w-[150px] min-h-[30px]"
                              variant="outline"
                              onClick={handlePostReport}
                            >
                              Confirm
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfo;
