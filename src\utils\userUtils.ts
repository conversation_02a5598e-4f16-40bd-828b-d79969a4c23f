/**
 * Utility functions for handling user data
 */

/**
 * Ensures a user ID is always a string, never undefined
 * 
 * @param userId - The user ID that might be undefined
 * @param fallback - Optional fallback value if userId is undefined (default: '')
 * @returns A string user ID
 */
export const ensureUserId = (userId: string | undefined, fallback: string = ''): string => {
  return userId || fallback;
};

/**
 * Safely accesses the uid property from userData
 * 
 * @param userData - The user data object that might be null
 * @returns A string user ID or empty string if not available
 */
export const getUserId = (userData: any): string => {
  if (!userData) return '';
  return userData.uid || userData.id || '';
};
