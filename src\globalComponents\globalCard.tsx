import * as React from "react";
import {
  Card,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { formatDuration } from "@/services/serviceService";
import { getCurrencySymbol } from "@/services/currencyService";

interface GlobalCardProps {
  title?: string;
  description?: string;
  duration: string;
  price?: number;
  border?: string;
  currency?: string; // Added currency prop
}

export function GlobalCard(props: GlobalCardProps) {
  return (
    <Card
      className="rounded-md border-l-[10px] shadow-none min-h-[150px] max-h-[150px] w-full"
      style={{
        borderLeftColor: props.border, // Dynamic left border color
        borderTop: "1px solid #E5E5E5", // Top border in gray
        borderRight: "1px solid #E5E5E5", // Right border in gray
        borderBottom: "1px solid #E5E5E5", // Bottom border in gray
      }}
    >
      <CardHeader className="px-4 py-3">
        <CardTitle>
          <p className="text-lg font-bold line-clamp-1  max-md:text-base">
            {props.title ? props.title : ""}
          </p>
          <div className="row justify-between">
            <p className="font-normal text-base max-md:text-sm">
              {formatDuration(props.duration, {
                dayLabel: "day",
                hourLabel: "hour",
                handlePlural: true,
              })}
            </p>
            <p
              style={{ color: props.border }}
              className="font-[600] text-base max-md:text-sm"
            >
              {props.currency
                ? getCurrencySymbol(props.currency)
                : getCurrencySymbol("gbp")}

              {props?.price ? (props?.price / (1 - 0.16)).toFixed(2) : "0"}
            </p>
          </div>
        </CardTitle>
        <CardDescription className="flex flex-row items-end justify-start">
          <div className="text-subtitle text-base max-md:text-sm line-clamp-3">
            {props.description ? props.description : ""}
          </div>
        </CardDescription>
      </CardHeader>
    </Card>
  );
}
