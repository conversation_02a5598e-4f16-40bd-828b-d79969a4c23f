"use client";
import { useState, useEffect, useRef } from "react";
import { Play } from "lucide-react";

interface LazyMediaProps {
  src: string | undefined;
  alt?: string;
  type: "image" | "video";
  className?: string;
  style?: React.CSSProperties;
  placeholderClassName?: string;
  placeholderStyle?: React.CSSProperties;
  showPlayIcon?: boolean;
  playIconClassName?: string;
  controls?: boolean;
  autoPlay?: boolean;
  muted?: boolean;
  poster?: string;
  onLoad?: () => void;
  onError?: () => void;
  enableLightbox?: boolean;
  onLightboxOpen?: (src: string, type: "image" | "video") => void;
  generateThumbnail?: boolean; // New prop to enable auto thumbnail generation
}

/**
 * LazyMedia component for optimized image and video loading
 * Uses Intersection Observer to only load media when it's about to enter the viewport
 */
const LazyMedia: React.FC<LazyMediaProps> = ({
  src,
  alt = "",
  type,
  className = "",
  style = {},
  placeholderClassName = "",
  placeholderStyle = {},
  showPlayIcon = false,
  playIconClassName = "",
  controls = false,
  autoPlay = false,
  muted = true,
  poster,
  onLoad,
  onError,
  enableLightbox = false,
  onLightboxOpen,
  generateThumbnail = true, // Default to true for auto thumbnail generation
}) => {
  const mediaRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [generatedPoster, setGeneratedPoster] = useState<string | null>(null);
  const [posterError, setPosterError] = useState(false);

  // Set up Intersection Observer to detect when the media is about to enter the viewport
  useEffect(() => {
    const options = {
      root: null, // Use the viewport as the root
      rootMargin: "100px", // Start loading when media is 100px from entering the viewport
      threshold: 0.1, // Trigger when at least 10% of the media is visible
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Once the media is visible, we don't need to observe it anymore
          if (mediaRef.current) {
            observer.unobserve(mediaRef.current);
          }
        }
      });
    }, options);

    if (mediaRef.current) {
      observer.observe(mediaRef.current);
    }

    return () => {
      if (mediaRef.current) {
        observer.unobserve(mediaRef.current);
      }
    };
  }, []);

  // Generate thumbnail from video
  const generateVideoThumbnail = (video: HTMLVideoElement) => {
    const canvas = canvasRef.current;
    if (!canvas || !video) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth || 320;
    canvas.height = video.videoHeight || 240;

    // Draw video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to data URL
    try {
      const dataURL = canvas.toDataURL("image/jpeg", 0.8);
      setGeneratedPoster(dataURL);
    } catch (error) {
      console.warn("Failed to generate video thumbnail:", error);
    }
  };

  // Handle video metadata loaded (when we can generate thumbnail)
  const handleVideoLoadedMetadata = () => {
    const video = videoRef.current;
    if (video && generateThumbnail && !poster && !generatedPoster) {
      // Set video to first frame and generate thumbnail
      video.currentTime = 0.1; // Slightly ahead to avoid black frame
    }
  };

  // Handle video seeked event (when currentTime is set)
  const handleVideoSeeked = () => {
    const video = videoRef.current;
    if (video && generateThumbnail && !poster && !generatedPoster) {
      generateVideoThumbnail(video);
    }
  };

  // Handle media load event
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // Handle media error event
  const handleError = () => {
    setHasError(true);
    if (onError) onError();
  };

  // Handle poster image error
  const handlePosterError = () => {
    setPosterError(true);
    // If poster fails and we have a video, try to generate thumbnail
    if (generateThumbnail && videoRef.current) {
      const video = videoRef.current;
      if (video.readyState >= 1) {
        // HAVE_METADATA
        video.currentTime = 0.1;
      }
    }
  };

  // Handle lightbox click
  const handleLightboxClick = (event: React.MouseEvent) => {
    if (enableLightbox && onLightboxOpen && src) {
      event.preventDefault();
      event.stopPropagation();
      onLightboxOpen(src, type);
    }
  };

  // Combine placeholder styles
  const combinedPlaceholderStyle = {
    ...style,
    ...placeholderStyle,
  };

  // Determine effective poster (provided poster or generated thumbnail)
  const effectivePoster = poster && !posterError ? poster : generatedPoster;

  // Determine what to render based on visibility and media type
  const renderMedia = () => {
    if (!isVisible) {
      // Simple placeholder for all media types
      if (type === "video") {
        // Video placeholder with play icon
        return (
          <div className="relative">
            <div
              className={`bg-gray-800 ${className} ${placeholderClassName}`}
              style={combinedPlaceholderStyle}
            ></div>
            {showPlayIcon && (
              <div className={`absolute top-2 right-2 ${playIconClassName}`}>
                <Play className="text-white h-4 w-4" />
              </div>
            )}
          </div>
        );
      }

      // Default placeholder for images
      return (
        <div
          className={`bg-gray-100 ${className} ${placeholderClassName}`}
          style={combinedPlaceholderStyle}
        />
      );
    }

    if (type === "image") {
      // Render image with lazy loading attribute
      return (
        <img
          src={src}
          alt={alt}
          className={`${className} ${!isLoaded ? placeholderClassName : ""} ${
            enableLightbox ? "cursor-pointer" : ""
          }`}
          style={style}
          loading="lazy"
          onLoad={handleLoad}
          onError={handleError}
          onClick={handleLightboxClick}
        />
      );
    } else if (type === "video") {
      // Render video with preload="metadata" to get the first frame
      return (
        <div className="relative">
          <video
            ref={videoRef}
            src={src}
            className={`${className} ${!isLoaded ? placeholderClassName : ""} ${
              enableLightbox ? "cursor-pointer" : ""
            }`}
            style={style}
            preload="metadata"
            poster={effectivePoster}
            muted={muted}
            controls={controls}
            autoPlay={autoPlay}
            playsInline={true} // Important for iPhone
            {...({ "webkit-playsinline": "true" } as any)} // Legacy iPhone support
            onLoadedMetadata={handleVideoLoadedMetadata}
            onSeeked={handleVideoSeeked}
            onLoadedData={handleLoad}
            onError={handleError}
            onPosterError={handlePosterError}
            onClick={handleLightboxClick}
          />
          {showPlayIcon && (
            <div className={`absolute top-2 right-2 ${playIconClassName}`}>
              <Play className="text-white h-4 w-4" />
            </div>
          )}
          {/* Hidden canvas for thumbnail generation */}
          <canvas ref={canvasRef} style={{ display: "none" }} aria-hidden="true" />
        </div>
      );
    }

    return null;
  };

  return <div ref={mediaRef}>{renderMedia()}</div>;
};

export default LazyMedia;
