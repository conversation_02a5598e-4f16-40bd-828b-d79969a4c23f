import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeSetupIntentRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      customerId,
      paymentMethodTypes,
      isUS
    }: StripeSetupIntentRequest = req.body;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    if (!paymentMethodTypes || !Array.isArray(paymentMethodTypes)) {
      return res.status(400).json({ error: 'Payment method types are required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    // Creates a temporary secret key linked with the customer 
    const ephemeralKey = await stripeService.ephemeralKeys.create(
      { customer: customerId },
      { apiVersion: '2020-08-27' }
    );

    const setupIntent = await stripeService.setupIntents.create({
      customer: customerId,
      payment_method_types: paymentMethodTypes,
    });

    res.status(200).json({
      clientSecret: setupIntent.client_secret,
      ephemeralKey: ephemeralKey.secret,
      success: true,
    });

  } catch (error) {
    console.error('Error creating setup intent:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
