import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import { useUnfollowMutation } from "@/graphql/test/generated";
import { useAccount } from "wagmi";
import { GetWalletId } from "@/services/authBridgeService";

export function useUnFollow() {
  const { mutateAsync: requestTypedData } = useUnfollowMutation();

  const { address,isConnected } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();
    const isAuthW3 = GetWalletId();

  async function unfollow(userId: string) {
    try {
     if (!address && !isConnected && !isAuthW3) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected && !isAuthW3)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      // Validate userId format
      if (!userId || !userId.match(/^0x[0-9a-fA-F]+$/)) {
        throw new Error("Invalid profile ID format");
      }

      const typedData = await requestTypedData({
        request: {
          account: userId,
        },
      });

      return typedData;
    } catch (error) {
      // console.error("Unfollow error:", error);
      throw error;
    }
  }

  return unfollow;
}
