import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useState, useEffect } from "react";
import {
  SideSheetContainer,
  SideSheetHeader,
  SideSheetDescription,
} from "@/components/ui/sidebarSheet";
import { closeEvent } from "@/lib/eventEmmiter";
import * as Tabs from "@radix-ui/react-tabs";
import { TabsContent, TabsTrigger } from "@/components/ui/tabs";
import OrderCard from "./orderCard";

export function Orders({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [isSheetOpen, setSheetOpen] = useState(false);
  useEffect(() => {
    const closeChat = () => {
      setSheetOpen(false);
      onOpenChange(false);
    };

    closeEvent.on("close", closeChat);

    return () => {
      closeEvent.off("close", closeChat);
    };
  }, [onOpenChange]);
  return (
    <SideSheetContainer
      className="left-[22rem] max-lg:left-0"
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <SideSheetHeader className="px-8">
        {/* Header */}
        <div
          className="cursor-pointer text-base font-normal row gap-2"
          onClick={() => onOpenChange(false)} // Close only when Back button is clicked
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">My Orders</p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>
      {/* Tabs */}
      <SideSheetDescription className="overflow-y-auto w-[90%] pr-4 chat-scroll-custom">
        <Tabs.Root
          defaultValue="All"
          className=" h-full w-full hide-scroll max-md:overflow-x-hidden mt-4"
        >
          <div className="sticky top-0 bg-white z-50 pb-2 ">
            <Tabs.List
              className="TabsListBg w-full"
              aria-label="Manage your account"
              style={
                {
                  "--active-bg-color": "#BDBDBD",
                } as React.CSSProperties
              }
            >
              <Tabs.Trigger className="TabsTriggerBg" value="All">
                All
              </Tabs.Trigger>
              <Tabs.Trigger className="TabsTriggerBg" value="Placed">
                Placed
              </Tabs.Trigger>
              <Tabs.Trigger className="TabsTriggerBg" value="Received">
                Received
              </Tabs.Trigger>
            </Tabs.List>
          </div>

          <div className="pb-12">
            <TabsContent value="All">
              <OrderCard />
            </TabsContent>
            <TabsContent value="Placed">
              <p>Placed</p>
            </TabsContent>
            <TabsContent value="Received">
              <p>Received</p>
            </TabsContent>
          </div>
        </Tabs.Root>
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
