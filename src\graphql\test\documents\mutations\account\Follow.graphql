mutation Follow($request: CreateFollowRequest!) {
  follow(request: $request) {
    ... on FollowResponse {
      hash
      __typename
    }
    ... on AccountFollowOperationValidationFailed {
      reason
    }
    ... on SelfFundedTransactionRequest {
      ...SelfFundedTransactionRequest
    }
    ... on SponsoredTransactionRequest {
      ...SponsoredTransactionRequest
    }
    ... on TransactionWillFail {
      ...TransactionWillFail
    }
  }
}
