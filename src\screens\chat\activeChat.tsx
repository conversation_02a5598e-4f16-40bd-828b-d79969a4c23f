import { useState } from "react";

export default function ActiveChat() {
  const [messages, setMessages] = useState([
    { type: "received", text: "Hey <PERSON>!", time: "17:02" },
    { type: "received", text: "How are you?", time: "17:02" },
    { type: "sent", text: "I am great, thanks!", time: "17:02" },
    { type: "sent", text: "What about you?", time: "17:02" },
    { type: "received", text: "I am great, thanks!", time: "17:02" },
    {
      type: "system",
      mode: "sent",
      orderUpdate: {
        status: "In progress",
        details: "<PERSON> has started working on order #453324",
      },
      reply: "I started to work",
      time: "17:02",
    },
    { type: "received", text: "That's great", time: "17:03" },
    { type: "received", text: "What's your progress?", time: "17:03" },
  ]);

  const [inputMessage, setInputMessage] = useState("");

  const handleSendInfo = () => {
    if (inputMessage.trim() === "") return;

    const newMessage = {
      type: "sent",
      text: inputMessage,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false }),
    };

    setMessages([...messages, newMessage]);
    setInputMessage("");
  };

  return (
    <div className="min-h-screen flex flex-col justify-between py-5" style={{ width: '23.7rem'}}>
      <div className="flex-1 chat-scroll-custom space-y-3 overflow-y-auto p-2">
        {messages.map((msg, index) => (
          <div key={index} className="flex flex-col">

            {/* Received Messages */}
            {msg.type === "received" && (
              <div className="max-w-xs flex gap-3 items-center bg-[#F2F2F2] text-black px-4 py-[13px] rounded-full self-start">
                <div>{msg.text}</div>
                <div className="text-xs text-[#7C7C7C] block text-right">{msg.time}</div>
              </div>
            )}

            {/* Sent Messages */}
            {msg.type === "sent" && (
              <div className="max-w-xs flex gap-3 items-center bg-[#E2E2E2] text-black px-4 py-[13px] rounded-full self-end">
                {msg.text}
                <span className="text-xs text-[#7C7C7C] block text-right">{msg.time}</span>
              </div>
            )}

            
            {msg.type === "system" && (
              <div className={`max-w-xs p-3 rounded-[2vw] border relative text-left ${msg.mode === 'sent' ? 'self-end' : 'self-start'}`}>
                {msg.orderUpdate && (
                  <>
                    <p className="font-semibold">Order status update: {msg.orderUpdate.status}</p>
                    <p className="text-sm text-gray-600 pb-8">{msg.orderUpdate.details}</p>
                  </>
                )}

                
                {msg.reply && (
                  <div className={`absolute bottom-0 flex flex-col ${msg.mode === 'sent' ? 'right-0' : 'left-0'}`}>
                    <div className={`max-w-xs flex gap-3 items-center ${msg.mode === 'sent' ? 'bg-[#E2E2E2]' : 'bg-[#F2F2F2]'} text-black px-4 py-[13px] rounded-full self-end`}>
                      <div>{msg.reply}</div>
                      <div className="text-xs text-[#7C7C7C] block text-right">{msg.time}</div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
      <div className="h-[10vh]"></div> 
      <div className="bg-white flex items-center">
        <div className="p-4 border-t w-full flex items-center">
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-1 p-2 border rounded-lg text-black outline-none"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
          />
          <button
            className="ml-2 p-2 bg-white text-base font-bold text-black rounded-lg"
            onClick={handleSendInfo}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                handleSendInfo();
              }
            }}
          >
            Send
          </button>
        </div>
      </div>
      <div className="h-[4vh]"></div>
    </div>
  );
}
