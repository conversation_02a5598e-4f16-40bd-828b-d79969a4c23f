"use client";
import {
  DollarSign,
  MoreHorizontal,
  Edit2,
  Facebook,
  Instagram,
  Youtube,
  Globe,
  Twitter,
  X,
} from "react-feather";

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Link from "next/link";

const getSocialLink = (handle: string | undefined, baseUrl: string) => {
  if (!handle) return "#";
  return handle.startsWith("http") ? handle : `${baseUrl}${handle.replace("@", "")}`;
};
const ProfileInfoSocialMedia = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">
                Links to Social Media Accounts
              </span>
            </div>
            {/* {props.isOtherProfile && (
              <div onClick={() => setIsOpen(true)} className=" cursor-pointer">
                <Edit2 className="" color={props.bgColor} />
              </div>
            )} */}
          </div>
          <div className="mt-4 max-md:mt-2 flex flex-row gap-3">
            {props.facebookLink && (
              <Link
                href={getSocialLink(props.facebookLink, "https://www.facebook.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
              >
                <Facebook size={28} className="fill-primary border-0 outline-0" />
              </Link>
            )}
            {props.instagramLink && (
              <Link
                href={getSocialLink(props.instagramLink, "https://www.instagram.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
              >
                <Instagram size={28} className="border-0 outline-0" />
              </Link>
            )}
            {props.youtubeLink && (
              <Link
                href={getSocialLink(props.youtubeLink, "https://www.youtube.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
              >
                <Youtube size={28} className="border-0 outline-0" />
              </Link>
            )}
            {props.websiteLink && (
              <Link
                href={props.websiteLink}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
              >
                <Globe size={28} className="border-0 outline-0" />
              </Link>
            )}
            {props.twitterLink && (
              <Link
                href={getSocialLink(props.twitterLink, "https://twitter.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
              >
                <Twitter size={28} className="border-0 outline-0 fill-primary" />
              </Link>
            )}
          </div>
        </div>
      </div>
      {/* PersonalMotto Modal */}
      <div>
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogTrigger asChild>
            {/* Empty or hidden trigger since we're controlling externally */}
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent
            className="min-w-96 max-w-96 min-h-60 p-6 rounded-[30px]"
            style={{ borderRadius: "20px" }}
          >
            <AlertDialogHeader>
              <AlertDialogDescription>
                <div className="row justify-between">
                  <div onClick={() => setIsOpen(false)} className=" cursor-pointer">
                    <X />
                  </div>
                  <p className="font-bold text-primary">Links To Social Media </p>
                  <p className={false ? "font-bold text-primary" : "font-bold text-borderColor"}>
                    Save
                  </p>
                </div>

                <div>
                  <div className="grid w-full items-center gap-1.5 mt-6">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Link to Facebook Account
                    </Label>
                    <Input type="text" placeholder="Add link" className="h-[40px]" />
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Link to Instagram Account
                    </Label>
                    <Input type="text" placeholder="Add link" className="h-[40px]" />
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Link to Twitter Account
                    </Label>
                    <Input type="text" placeholder="Add link" className="h-[40px]" />
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Link to YouTube
                    </Label>
                    <Input type="text" placeholder="Add link" className="h-[40px]" />
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Link to Your Website
                    </Label>
                    <Input type="text" placeholder="Add link" className="h-[40px]" />
                  </div>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};

export default ProfileInfoSocialMedia;
