import { GlobalCard } from "@/globalComponents/globalCard";
import { themes } from "../../../../../../../theme";
import useProfile from "@/hook/profileData";
import { useEffect, useState } from "react";
import { getServiceById, getServicesByUserId } from "@/services/serviceService";
import useAuth from "@/hook";
import { getCurrencySymbol, initializeCurrency } from "@/services/currencyService";
import CardSkeleton from "@/components/CardSkeleton";

const OtherServicesCard = ({ onSelectService, activeColor, otherUserID }: any) => {
  const auth = useAuth();
  const profile = useProfile(otherUserID);

  // State to store fetched services
  const [services, setServices]: any = useState([]);
  const [currencySymbol, setCurrencySymbol] = useState("£"); // Default to GBP symbol
  const [loading, setLoading] = useState(true); // Add loading state

  // Initialize currency or get it from profile
  useEffect(() => {
    try {
      if (profile?.profileData?.currency) {
        // Use currency from profile if available
        setCurrencySymbol(getCurrencySymbol(profile.profileData.currency));
      } else {
        // Otherwise use the app default
        setCurrencySymbol(getCurrencySymbol(initializeCurrency()));
      }
    } catch (error) {
      console.error("Error getting currency symbol:", error);
      setCurrencySymbol("£"); // Default fallback
    }
  }, [profile?.profileData]);

  // Get services for the specified user
  useEffect(() => {
    const fetchAllServices = async () => {
      setLoading(true); // Set loading to true when fetching starts
      if (otherUserID) {
        const responsedan = await getServicesByUserId(otherUserID);

        if (responsedan.success && responsedan.services) {
          // Ensure all services have a currency
          const servicesWithCurrency = responsedan.services.map((service: any) => {
            if (!service.currency) {
              // Use the user's currency from profile, or default app currency
              const userCurrency = profile?.profileData?.currency || initializeCurrency();
              return {
                ...service,
                currency: userCurrency,
              };
            }
            return service;
          });

          const sortedServices = servicesWithCurrency.sort((a: any, b: any) => {
            // Handle Firebase timestamp objects
            const getTimestamp = (service: any) => {
              if (service.created_at) {
                // If it's a Firebase timestamp object with toDate() method
                if (typeof service.created_at.toDate === "function") {
                  return service.created_at.toDate().getTime();
                }
                // If it's already a date string or timestamp
                return new Date(service.created_at).getTime();
              }
              return 0; // Default for services without created_at
            };

            const timestampA = getTimestamp(a);
            const timestampB = getTimestamp(b);

            return timestampB - timestampA; // Descending order (newest first)
          });

          setServices(sortedServices);
        } else {
          setServices([]);
        }
      }
      setLoading(false); // Set loading to false when fetching completes
    };

    fetchAllServices();
  }, [otherUserID, profile?.profileData]);

  return (
    <>
      <div>
        <div className="w-full bg-white sticky top-[7.2rem] max-md:top-[5.6rem] z-50">
          <div className="row justify-between py-2">
            <p className="text-primary text-xl font-bold max-md:text-base">My Services</p>
            <p className="text-primary text-xl font-bold max-md:text-base px-2">{currencySymbol}</p>
          </div>
        </div>
        <div className="bg-white pt-2">
          {loading ? (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              <CardSkeleton count={3} columns={1} showGrid={true} />
            </div>
          ) : (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              {services.length > 0 ? (
                services.map((service: any, index: any) => (
                  <div
                    className="cursor-pointer"
                    key={index}
                    onClick={() => onSelectService(service.id)}
                  >
                    {/* <GlobalCard
                      border={activeColor}
                      title={service.title}
                      description={service.description}
                      price={service.price}
                      duration={service.duration}
                      currency={service.currency}
                    /> */}
                    {Object.entries(themes).map(([_, innerThemeProperties]) => (
                      <div key={innerThemeProperties.title}>
                        {(service.category === "Storytelling" ? "Literature" : service.category) ===
                          innerThemeProperties.title && (
                          <GlobalCard
                            border={innerThemeProperties.backgroundColor}
                            title={service.title}
                            description={service.description}
                            price={service.price}
                            duration={service?.duration}
                            currency={service?.currency || initializeCurrency()}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                ))
              ) : (
                <div className="text-lg text-gray-500">No services found</div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default OtherServicesCard;
