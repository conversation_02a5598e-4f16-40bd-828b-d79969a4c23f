"use client";
import ScrollButton from "@/components/bottomArrow";
import { useRef } from "react";
import { themes } from "../../../../theme";
import ProfileCardSC from "./profilesCardSC";

const ProfileHome = (props: any) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  const postsRef = useRef<HTMLDivElement>(null);

  return (
    <div className="relative p-0">
      <div className="max-md:hidden">
        <ScrollButton scrollRef={scrollRef} />
      </div>

      <div
        ref={scrollRef}
        className="overflow-y-auto p-0 md:h-[calc(100vh-290px)]  max-md:h-screen hide-scroll"
        // style={{ maxHeight: "100vh" }}
      >
        {/* Content */}
        <div className="flex flex-col gap-3 h-full bg-white w-full mb-[1px]">
          <div>
            {Object.entries(themes).map(
              ([themeName, themeProperties]) =>
                themeProperties.title === props.subcategory && (
                  <div className="row">
                    <ProfileCardSC
                      border={themeProperties.backgroundColor}
                      themeProperties={themeProperties}
                    />
                  </div>
                )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHome;
