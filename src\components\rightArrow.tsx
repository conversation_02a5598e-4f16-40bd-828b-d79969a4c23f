import { useState, useEffect } from "react";
import { ChevronsRight } from "react-feather";

interface ScrollButtonProps {
  scrollRef: React.RefObject<HTMLDivElement>;
}

const ScrollButtonRight = ({ scrollRef }: ScrollButtonProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkScrollability = () => {
      const container = scrollRef.current;
      if (container) {
        setIsVisible(
          container.scrollLeft + container.clientWidth < container.scrollWidth
        ); // Show if not at the end
      }
    };

    const container = scrollRef.current;
    if (container) {
      container.addEventListener("scroll", checkScrollability);
    }

    window.addEventListener("resize", checkScrollability);
    checkScrollability(); // Initial check

    return () => {
      if (container) {
        container.removeEventListener("scroll", checkScrollability);
      }
      window.removeEventListener("resize", checkScrollability);
    };
  }, [scrollRef]);

  const handleScroll = () => {
    const container = scrollRef.current;
    if (container) {
      container.scrollBy({ left: 350, behavior: "smooth" });
    }
  };

  if (!isVisible) return null;

  return (
    <div className="relative">
      <div
        onClick={handleScroll}
        className="cursor-pointer fixed bottom-0 right-0 top-48 w-[100px] bg-gradient-to-l from-white flex flex-row justify-center items-center z-[49]"
      >
        <button
          className="fixed right-10 top-2/3 transform translate-x-1/2 rounded z-[50]"
          style={{ height: "80px" }}
        >
          <img
            src="/assets/ChevronsDown.svg"
            className="h-[50px] w-[50px] -rotate-90"
          />
        </button>
      </div>
    </div>
  );
};

export default ScrollButtonRight;
