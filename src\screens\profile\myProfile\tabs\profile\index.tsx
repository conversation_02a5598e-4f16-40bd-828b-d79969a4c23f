import { Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Followers from "./followers";
import Following from "./following";
import * as Tabs from "@radix-ui/react-tabs";
import useAuth from "@/hook";
import { useEffect, useState } from "react";
import { FollowerManager } from "@/services/followServices";

type ProfileMyProfileProps = {
  activeBorderColor?: string; // New prop for dynamic active border color
  otherUserID?: any;
  isOtherProfileStatus: boolean;
  setSelectedProfileTabs: any;
  selectedProfileTabs: any;
};

export function ProfileMyProfile({
  activeBorderColor = "#333333",
  otherUserID,
  isOtherProfileStatus,
  setSelectedProfileTabs,
  selectedProfileTabs,
}: ProfileMyProfileProps) {
  const user = useAuth();
  const [followingList, setFollowingList]: any = useState([]);
  const [followersList, setFollowersList]: any = useState([]);
  const [selectedTabs, setSelectedTabs] = useState(selectedProfileTabs);

  const handleFollowing = async (userId: string) => {
    try {
      const resp = await FollowerManager.getInstance().GetFollowingsByUserId(userId);
      setFollowingList(resp || []);
    } catch (error) {
      console.error("Error fetching followings:", error);
    }
  };

  const handleFollowers = async (userId: string) => {
    try {
      const resp = await FollowerManager.getInstance().GetFollowersByUserId(userId);
      setFollowersList(resp || []);
    } catch (error) {
      console.error("Error fetching followings:", error);
    }
  };

  // console.log(isOtherProfileStatus);

  useEffect(() => {
    // Check if we have a valid otherUserID when isOtherProfileStatus is true
    if (
      isOtherProfileStatus &&
      otherUserID &&
      typeof otherUserID === "string" &&
      otherUserID.trim() !== ""
    ) {
      handleFollowers(otherUserID);
      handleFollowing(otherUserID);
    } else if (user?.userId) {
      // Otherwise use the logged-in user's ID if available
      handleFollowers(user.userId);
      handleFollowing(user.userId);
    } else {
      // If neither is available, don't make the API calls
      console.log("No valid user ID available for followers/following");
    }
  }, [user?.userId, otherUserID, isOtherProfileStatus]);

  useEffect(() => {
    setSelectedTabs(selectedProfileTabs ? selectedProfileTabs : "Following");
  }, [selectedProfileTabs]);

  return (
    <div className="mt-3">
      <Tabs.Root defaultValue="Following" value={selectedTabs} className="w-full bg-white">
        <Tabs.List
          className="TabsList pb-0 max-md:pb-1 sticky top-10 bg-white -mt-5 scroll-pt-2 max-md:-mt-7"
          aria-label="Manage your account"
          style={
            {
              "--active-border-color": activeBorderColor,
            } as React.CSSProperties
          } // Set CSS variable
        >
          <Tabs.Trigger
            className="TabsTrigger min-h-[70px] max-md:min-h-[50px] bg-red-600"
            value="Following"
            onClick={() => {
              setSelectedTabs("Following");
            }}
            style={
              {
                paddingTop: "10px",
              } as React.CSSProperties
            }
          >
            <div className="text-center pb-3">
              <p className="small-heading">{followingList.length}</p>
              <p className="small-subheading">Following</p>
            </div>
          </Tabs.Trigger>
          <Tabs.Trigger
            className="TabsTrigger min-h-[70px] max-md:min-h-[50px]"
            value="Followers"
            onClick={() => {
              setSelectedTabs("Followers");
            }}
            style={
              {
                paddingTop: "10px",
              } as React.CSSProperties
            }
          >
            <div className="text-center pb-3">
              <p className="small-heading">{followersList.length}</p>
              <p className="small-subheading">Followers</p>
            </div>
          </Tabs.Trigger>
        </Tabs.List>
        <TabsContent value="Followers" className="bg-red-400">
          <div className="w-full overflow-scroll hide-scroll bg-white h-full max-md:pt-3">
            <Followers otherUserID={otherUserID} />
          </div>
        </TabsContent>
        <TabsContent value="Following">
          <div className="max-md:pt-3">
            <Following otherUserID={otherUserID} />
          </div>
        </TabsContent>
      </Tabs.Root>
    </div>
  );
}
