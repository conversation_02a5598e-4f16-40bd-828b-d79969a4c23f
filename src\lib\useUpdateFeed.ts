import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import { useSetAccountMetadataMutation } from "@/graphql/test/generated";
import { v4 } from "uuid";
import { immutable, StorageClient } from "@lens-chain/storage-client";
import { useAccount } from "wagmi";

export function useUpdateFeed() {
  const { mutateAsync: requestTypedData } = useSetAccountMetadataMutation();

  const { address,isConnected } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();
  const storageClient = StorageClient.create();
  const acl = immutable(232);

  async function updateFeed({
    name,
    feed,
    location,
    image,
    image_url,
    upload_image = false,
  }: {
    name?: string;
    feed?: string;
    location?: string;
    image?: File;
    image_url?: string;
    upload_image?: boolean;
  }) {
    try {
      if (!name && !feed && !location && !image) {
        throw new Error("no argument found !!");
      }
     if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      //   const response = await storageClient.uploadFile(post, { acl });
      const payload: any = {
        $schema: "https://json-schemas.lens.dev/account/1.0.0.json",
        lens: {
          id: v4(),
        },
      };

      if (feed) {
        payload.lens = {
          ...payload.lens,
          bio: feed,
        };
      }

      if (name) {
        payload.lens = {
          ...payload.lens,
          name,
        };
      }
      if (location) {
        payload.lens = {
          ...payload.lens,
          attributes: [
            {
              key: "location",
              type: "String",
              value: location,
            },
            {
              key: "timestamp",
              type: "String",
              value: new Date().toISOString(),
            },
          ],
        };
      }

      if (image_url) {
        payload.lens = {
          ...payload.lens,
          picture: image_url,
        };
      }

      if (upload_image && image) {
        const response = await storageClient.uploadFile(image, { acl });
        payload.lens = {
          ...payload.lens,
          picture: response.uri,
        };
      }

      const { uri } = await storageClient.uploadAsJson(payload, {
        acl: immutable(232),
      });

      const typedData = await requestTypedData({
        request: {
          metadataUri: uri,
        },
      });

      return typedData;
    } catch (error) {
      throw error;
    }
  }
  return updateFeed;
}
