import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";

interface EventCardSkeletonProps {
  count?: number;
  columns?: number;
  showGrid?: boolean;
}

const EventCardSkeleton: React.FC<EventCardSkeletonProps> = ({
  count = 4,
  columns = 2,
  showGrid = true,
}) => {
  // Generate the appropriate grid class based on columns
  const getGridClass = () => {
    if (!showGrid) return "";

    switch (columns) {
      case 1:
        return "grid grid-cols-1 gap-2";
      case 2:
        return "grid grid-cols-2 max-md:grid-cols-1 gap-2";
      case 3:
        return "grid grid-cols-3 max-md:grid-cols-1 gap-2";
      case 4:
        return "grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-2";
      case 6:
        return "grid grid-cols-6 max-md:grid-cols-1 max-lg:grid-cols-3 gap-2";
      default:
        return "grid grid-cols-2 max-md:grid-cols-1 gap-2";
    }
  };

  return (
    <div className={getGridClass()}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`animate-pulse ${!showGrid ? "mb-2" : ""}`}>
          <Card
            className={`shadow-none border-0 p-0 ${
              showGrid ? "w-full" : "w-[350px]"
            } min-h-[140px] max-md:w-full`}
          >
            <CardHeader className="px-0 py-1">
              <CardTitle>
                <div className="flex flex-row gap-2 items-center">
                  {/* Color square skeleton */}
                  <span className="w-4 h-4 rounded-[2px] bg-gray-200"></span>
                  {/* Title skeleton */}
                  <div className="h-5 bg-gray-200 rounded-md w-3/4"></div>
                </div>
                <div className="row justify-between mt-2">
                  <div className="row gap-2 flex items-center">
                    {/* Clock icon skeleton */}
                    <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                    {/* Date skeleton */}
                    <div className="h-4 bg-gray-200 rounded-md w-40 mt-1"></div>
                  </div>
                </div>
              </CardTitle>
              {/* Description skeleton */}
              <div className="pl-6 pr-3 space-y-2 mt-2">
                <div className="h-4 bg-gray-200 rounded-md w-full"></div>
                <div className="h-4 bg-gray-200 rounded-md w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded-md w-4/6"></div>
              </div>
            </CardHeader>
          </Card>
        </div>
      ))}
    </div>
  );
};

export default EventCardSkeleton;
