import { doc, getDoc, collection, getDocs, query, where } from 'firebase/firestore';
import { initFirebase } from '../../firebaseConfig';
import { getUserById } from './usersServices';

export interface SellerInfo {
  id: string;
  stripeAccountId: string;
  email?: string;
  name?: string;
  onboardingComplete?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Get seller information by user ID
 */
export const getSellerByUserId = async (userId: string): Promise<{ success: boolean; seller?: SellerInfo; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const sellerRef = doc(db, 'sellers', userId);
    const sellerSnap = await getDoc(sellerRef);
    
    if (sellerSnap.exists()) {
      const sellerData = sellerSnap.data();
      const seller: SellerInfo = {
        id: sellerSnap.id,
        ...sellerData
      } as SellerInfo;
      
      return { success: true, seller };
    } else {
      return { success: false, error: "Seller not found" };
    }
  } catch (error) {
    console.error("Error getting seller by user ID:", error);
    return { success: false, error: "Failed to get seller information" };
  }
};

/**
 * Get complete seller information including user details
 */
export const getCompleteSellerInfo = async (userId: string): Promise<{ 
  success: boolean; 
  seller?: SellerInfo & { userEmail?: string; userName?: string }; 
  error?: string 
}> => {
  try {
    // Get seller info
    const sellerResult = await getSellerByUserId(userId);
    if (!sellerResult.success || !sellerResult.seller) {
      return { success: false, error: sellerResult.error || "Seller not found" };
    }

    // Get user info for email and name
    const userResult = await getUserById(userId);
    if (!userResult.success) {
      // Return seller info even if user details fail
      return { success: true, seller: sellerResult.seller };
    }

    const completeSeller = {
      ...sellerResult.seller,
      userEmail: userResult.user.email || undefined,
      userName: userResult.user.full_name || userResult.user.profile_name || undefined
    };

    return { success: true, seller: completeSeller };
  } catch (error) {
    console.error("Error getting complete seller info:", error);
    return { success: false, error: "Failed to get complete seller information" };
  }
};

/**
 * Check if a user is a registered seller
 */
export const isUserSeller = async (userId: string): Promise<boolean> => {
  try {
    const result = await getSellerByUserId(userId);
    return result.success && !!result.seller?.stripeAccountId;
  } catch (error) {
    console.error("Error checking if user is seller:", error);
    return false;
  }
};

/**
 * Get all sellers (admin function)
 */
export const getAllSellers = async (): Promise<{ success: boolean; sellers?: SellerInfo[]; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const sellersRef = collection(db, 'sellers');
    const sellersSnapshot = await getDocs(sellersRef);
    
    const sellers: SellerInfo[] = sellersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as SellerInfo));

    return { success: true, sellers };
  } catch (error) {
    console.error("Error getting all sellers:", error);
    return { success: false, error: "Failed to get sellers" };
  }
};

/**
 * Find seller by Stripe account ID
 */
export const getSellerByStripeAccountId = async (stripeAccountId: string): Promise<{ success: boolean; seller?: SellerInfo; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const sellersRef = collection(db, 'sellers');
    const q = query(sellersRef, where('stripeAccountId', '==', stripeAccountId));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const seller: SellerInfo = {
        id: doc.id,
        ...doc.data()
      } as SellerInfo;
      
      return { success: true, seller };
    } else {
      return { success: false, error: "Seller not found" };
    }
  } catch (error) {
    console.error("Error getting seller by Stripe account ID:", error);
    return { success: false, error: "Failed to get seller" };
  }
};
