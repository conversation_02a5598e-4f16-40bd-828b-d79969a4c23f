import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getEscrowTransactionByOrderId, releaseEscrowStage } from '@/services/transactionService';
import { stripe, stripeUS } from '@/lib/stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const defaultStripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Local helper function to get Stripe instance by currency
function getStripeInstanceByCurrency(currency: string): { stripeInstance: Stripe; isUS: boolean } {
  if (!currency) {
    console.warn('No currency provided, using default Stripe instance');
    return { stripeInstance: stripe, isUS: false };
  }

  const normalizedCurrency = currency.toUpperCase();
  const isUS = normalizedCurrency === 'USD';

  // If US key is not configured, always use default instance
  const hasUSKey = process.env.STRIPE_SECRET_KEY_US && process.env.STRIPE_SECRET_KEY_US !== process.env.STRIPE_SECRET_KEY;

  return {
    stripeInstance: (isUS && hasUSKey) ? stripeUS : stripe,
    isUS: isUS && hasUSKey
  };
}

// Helper function to validate charge and get currency
async function validateChargeAndGetCurrency(chargeId: string, transactionCurrency: string) {
  try {
    console.log(`🔍 Validating charge ${chargeId} with currency ${transactionCurrency}`);

    // Automatically select the correct Stripe instance based on transaction currency
    const { stripeInstance, isUS } = getStripeInstanceByCurrency(transactionCurrency);
    console.log(`🌍 Using ${isUS ? 'US' : 'International'} Stripe instance for ${transactionCurrency.toUpperCase()} currency`);

    const charge = await stripeInstance.charges.retrieve(chargeId);
    console.log(`✅ Charge retrieved successfully:`, {
      id: charge.id,
      amount: charge.amount,
      currency: charge.currency,
      status: charge.status,
      paid: charge.paid
    });

    // Also check the balance transaction currency
    let balanceTransactionCurrency = null;
    if (charge.balance_transaction) {
      try {
        const balanceTransaction = await stripeInstance.balanceTransactions.retrieve(charge.balance_transaction as string);
        balanceTransactionCurrency = balanceTransaction.currency;
        console.log('Balance transaction details:', {
          id: balanceTransaction.id,
          currency: balanceTransaction.currency,
          amount: balanceTransaction.amount,
          net: balanceTransaction.net
        });
      } catch (btError) {
        console.error('Error retrieving balance transaction:', btError);
      }
    }

    return {
      success: true,
      currency: charge.currency,
      amount: charge.amount,
      status: charge.status,
      balanceTransactionId: charge.balance_transaction,
      balanceTransactionCurrency
    };
  } catch (error) {
    console.error('Error retrieving charge:', error);

    // Check if it's a "No such charge" error
    if (error instanceof Error && error.message.includes('No such charge')) {
      return {
        success: false,
        error: `Charge ${chargeId} not found. This might be because the payment was created with a different Stripe account or the charge ID is incorrect.`,
        notFound: true
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      orderId,
      stage, // 'accept', 'delivered', 'completed'
      chargeId // Required for creating transfers
    } = await request.json();

    // Validate required fields
    if (!orderId || !stage || !chargeId) {
      return NextResponse.json({
        error: 'Missing required fields: orderId, stage, chargeId'
      }, { status: 400 });
    }

    // Validate stage
    if (!['accept', 'delivered', 'completed'].includes(stage)) {
      return NextResponse.json({
        error: 'Invalid stage. Must be: accept, delivered, or completed'
      }, { status: 400 });
    }

    // Get escrow transaction
    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // Check if this stage is already released
    const stageData = transaction.escrowStages?.find(s => s.stage === stage);
    if (!stageData) {
      return NextResponse.json({
        error: 'Stage not found in escrow transaction'
      }, { status: 400 });
    }

    if (stageData.status === 'released') {
      return NextResponse.json({
        error: 'This stage has already been released'
      }, { status: 400 });
    }

    // Get the original charge to ensure currency consistency
    const chargeValidation = await validateChargeAndGetCurrency(chargeId, transaction.currency);
    if (!chargeValidation.success) {
      return NextResponse.json({
        error: 'Failed to retrieve original charge information',
        details: chargeValidation.error
      }, { status: 500 });
    }

    const chargeCurrency = chargeValidation.currency;
    console.log('Original charge currency:', chargeCurrency);
    console.log('Transaction currency:', transaction.currency);
    console.log('Charge amount:', chargeValidation.amount);
    console.log('Charge status:', chargeValidation.status);
    console.log('Charge ID being used:', chargeId);
    console.log('Transaction session ID:', transaction.stripeSessionId);
    console.log('Balance transaction ID:', chargeValidation.balanceTransactionId);
    console.log('Balance transaction currency:', chargeValidation.balanceTransactionCurrency);

    // Ensure currency consistency
    if (chargeCurrency !== transaction.currency) {
      console.error('Currency mismatch:', { chargeCurrency, transactionCurrency: transaction.currency });
      return NextResponse.json({
        error: `Currency mismatch: charge is in ${chargeCurrency} but transaction is in ${transaction.currency}. Please ensure the escrow transaction was created with the same currency as the original payment.`
      }, { status: 400 });
    }

    // Check if balance transaction currency differs from charge currency
    if (chargeValidation.balanceTransactionCurrency && chargeValidation.balanceTransactionCurrency !== chargeCurrency) {
      console.error('Balance transaction currency mismatch:', {
        chargeCurrency,
        balanceTransactionCurrency: chargeValidation.balanceTransactionCurrency
      });
      return NextResponse.json({
        error: `Balance transaction currency mismatch: charge is in ${chargeCurrency} but balance transaction is in ${chargeValidation.balanceTransactionCurrency}. This may be due to currency conversion settings in your Stripe account.`,
        details: {
          chargeId,
          chargeCurrency,
          balanceTransactionCurrency: chargeValidation.balanceTransactionCurrency,
          balanceTransactionId: chargeValidation.balanceTransactionId,
          suggestion: 'Check your Stripe account currency conversion settings or use a charge that was processed in the same currency as your account default.'
        }
      }, { status: 400 });
    }

    // Validate charge amount vs transfer amount
    // Note: stageData.amount is already in smallest currency unit (cents/pence)
    const transferAmountCents = Math.round(stageData.amount);
    console.log('Transfer validation:', {
      transferAmountCents,
      chargeAmount: chargeValidation.amount,
      stageAmount: stageData.amount,
      stageAmountInMajorUnit: stageData.amount / 100,
      transactionTotalAmount: transaction.amount,
      transactionTotalInMajorUnit: transaction.amount / 100,
      stage,
      percentage: stageData.percentage
    });

    if (transferAmountCents > chargeValidation.amount!) {
      return NextResponse.json({
        error: `Transfer amount (${transferAmountCents} cents) exceeds charge amount (${chargeValidation.amount} cents)`,
        details: {
          transferAmount: stageData.amount,
          transferAmountInMajorUnit: stageData.amount / 100,
          transferAmountCents,
          chargeAmount: chargeValidation.amount,
          chargeAmountInMajorUnit: chargeValidation.amount / 100,
          chargeCurrency: chargeCurrency,
          transactionAmount: transaction.amount,
          transactionAmountInMajorUnit: transaction.amount / 100,
          transactionCurrency: transaction.currency,
          stage,
          percentage: stageData.percentage,
          suggestion: 'Please verify you are using the correct charge ID that matches this escrow transaction amount'
        }
      }, { status: 400 });
    }

    // Step 1: Check charge status and capture if needed
    console.log('🔄 Step 1: Checking charge status...');

    // Use the correct Stripe instance based on transaction currency
    const { stripeInstance: chargeStripeInstance } = getStripeInstanceByCurrency(transaction.currency);
    const charge = await chargeStripeInstance.charges.retrieve(chargeId);

    if (!charge.captured) {
      console.log('⚠️ Charge not captured yet. For escrow, we need to capture the full payment first.');

      // Get the payment intent to capture the full amount
      if (charge.payment_intent) {
        console.log('🔄 Capturing full payment via Payment Intent...');
        try {
          const capturedPaymentIntent = await chargeStripeInstance.paymentIntents.capture(charge.payment_intent as string);
          console.log('✅ Full payment captured via Payment Intent:', {
            id: capturedPaymentIntent.id,
            status: capturedPaymentIntent.status,
            amount_received: capturedPaymentIntent.amount_received
          });
        } catch (captureError) {
          console.error('❌ Failed to capture payment intent:', captureError);
          return NextResponse.json({
            error: 'Failed to capture payment',
            details: captureError instanceof Error ? captureError.message : 'Unknown capture error',
            chargeId,
            paymentIntentId: charge.payment_intent
          }, { status: 400 });
        }
      } else {
        return NextResponse.json({
          error: 'Cannot capture payment. No payment intent found.',
          details: 'This charge was not created via Payment Intent and cannot be captured for escrow.',
          chargeId
        }, { status: 400 });
      }
    }

    console.log('✅ Payment is captured and held in Stripe. Creating transfer for escrow stage...');

    // Step 2: Create direct transfer to seller (from captured payment held in Stripe)
    console.log('🔄 Step 2: Creating transfer to seller...');

    // Use the correct Stripe instance based on transaction currency
    const { stripeInstance: transferStripeInstance, isUS } = getStripeInstanceByCurrency(transaction.currency);
    console.log(`🌍 Using ${isUS ? 'US' : 'International'} Stripe instance for ${transaction.currency.toUpperCase()} currency`);

    try {
      const transfer = await transferStripeInstance.transfers.create({
        amount: transferAmountCents, // Amount is already in smallest currency unit
        currency: chargeCurrency, // Use the charge currency to ensure consistency
        source_transaction: chargeId,
        destination: transaction.sellerStripeAccountId!,
        metadata: {
          orderId,
          stage,
          transactionId: transaction.id,
          sellerId: transaction.sellerId!,
          escrowStage: stage,
          transferredAt: new Date().toISOString()
        }
      });

      // Update escrow stage status
      const releaseResult = await releaseEscrowStage(
        transaction.id,
        stage,
        transfer.id
      );

      if (!releaseResult.success) {
        return NextResponse.json({
          error: 'Failed to update escrow stage status'
        }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        transferId: transfer.id,
        amount: stageData.amount,
        amountInMajorUnit: stageData.amount / 100,
        currency: chargeCurrency,
        stage,
        orderId,
        message: `Successfully released ${stage} stage payment of ${(stageData.amount / 100).toFixed(2)} ${chargeCurrency.toUpperCase()}`
      });

    } catch (stripeError) {
      console.error('Stripe transfer error:', stripeError);

      // Provide specific error messages for common Stripe errors
      let errorMessage = 'Failed to create Stripe transfer';
      let statusCode = 500;

      if (stripeError && typeof stripeError === 'object' && 'type' in stripeError) {
        const error = stripeError as any;

        if (error.type === 'StripeInvalidRequestError') {
          statusCode = 400;
          if (error.code === 'account_invalid') {
            errorMessage = 'Seller account is invalid or not properly set up';
          } else if (error.param === 'source_transaction') {
            if (error.message.includes('currency')) {
              errorMessage = `Currency mismatch: The charge ID you provided is from a different currency transaction. ${error.message}. Please use the GET endpoint with ?findChargeId=true to find the correct charge ID for this escrow transaction.`;
            } else {
              errorMessage = `Invalid source transaction: ${error.message}`;
            }
          } else if (error.param === 'currency') {
            errorMessage = `Currency error: ${error.message}`;
          } else {
            errorMessage = `Invalid request: ${error.message}`;
          }
        } else if (error.type === 'StripePermissionError') {
          statusCode = 403;
          errorMessage = 'Insufficient permissions to transfer to this account';
        } else {
          errorMessage = error.message || errorMessage;
        }
      }

      return NextResponse.json({
        error: errorMessage,
        type: stripeError && typeof stripeError === 'object' && 'type' in stripeError ? (stripeError as any).type : 'unknown',
        chargeId,
        transferAmount: stageData.amount,
        currency: chargeCurrency
      }, { status: statusCode });
    }

  } catch (error) {
    console.error('Escrow release error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// GET endpoint to check escrow status and find correct charge ID
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');
    const findChargeId = searchParams.get('findChargeId') === 'true';
    const showTransfers = searchParams.get('showTransfers') === 'true';

    if (!orderId) {
      return NextResponse.json({
        error: 'orderId parameter is required'
      }, { status: 400 });
    }

    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // If requested, try to find the correct charge ID
    let suggestedChargeId = null;
    let chargeSearchResults = null;

    if (findChargeId && transaction.stripeSessionId) {
      try {
        // Use the correct Stripe instance based on transaction currency
        const { stripeInstance: searchStripeInstance, isUS } = getStripeInstanceByCurrency(transaction.currency);
        console.log(`🔍 Finding charge ID using ${isUS ? 'US' : 'International'} Stripe instance for ${transaction.currency.toUpperCase()} currency`);

        // Get the checkout session to find the payment intent
        const session = await searchStripeInstance.checkout.sessions.retrieve(transaction.stripeSessionId);
        if (session.payment_intent) {
          const paymentIntent = await searchStripeInstance.paymentIntents.retrieve(session.payment_intent as string);

          // Get charges for this payment intent
          const charges = await searchStripeInstance.charges.list({
            payment_intent: paymentIntent.id,
            limit: 1
          });

          if (charges.data && charges.data.length > 0) {
            const charge = charges.data[0];
            suggestedChargeId = charge.id;
            chargeSearchResults = {
              chargeId: charge.id,
              amount: charge.amount,
              currency: charge.currency,
              status: charge.status,
              amountMatches: charge.amount === transaction.amount,
              currencyMatches: charge.currency === transaction.currency
            };
          }
        }
      } catch (error) {
        console.error('Error finding charge ID:', error);
      }
    }

    // Get transfer details if requested
    let transferDetails = null;
    if (showTransfers && transaction.escrowStages) {
      transferDetails = [];
      for (const stage of transaction.escrowStages) {
        if (stage.stripeTransferId) {
          try {
            const transfer = await transferStripeInstance.transfers.retrieve(stage.stripeTransferId);
            transferDetails.push({
              transferId: transfer.id,
              amount: transfer.amount,
              amountInMajorUnit: transfer.amount / 100,
              currency: transfer.currency,
              destination: transfer.destination,
              created: new Date(transfer.created * 1000).toISOString(),
              status: transfer.reversed ? 'reversed' : 'completed',
              stage: stage.stage,
              description: transfer.description,
              metadata: transfer.metadata
            });
          } catch (error) {
            console.error(`Error retrieving transfer ${stage.stripeTransferId}:`, error);
            transferDetails.push({
              transferId: stage.stripeTransferId,
              error: 'Failed to retrieve transfer details',
              stage: stage.stage
            });
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      transaction: {
        id: transaction.id,
        orderId: transaction.orderId,
        currentStage: transaction.currentStage,
        totalAmount: transaction.amount,
        subtotal: transaction.subtotal,
        transactionFee: transaction.transactionFee,
        platformCommission: transaction.platformCommission,
        sellerAmount: transaction.sellerAmount,
        stripeSessionId: transaction.stripeSessionId,
        currency: transaction.currency,
        escrowStages: transaction.escrowStages?.map(stage => ({
          stage: stage.stage,
          percentage: stage.percentage,
          amount: stage.amount,
          status: stage.status,
          releasedAt: stage.releasedAt,
          stripeTransferId: stage.stripeTransferId
        }))
      },
      ...(suggestedChargeId && {
        suggestedChargeId,
        chargeSearchResults,
        message: 'Found suggested charge ID based on the transaction session'
      }),
      ...(transferDetails && { transferDetails })
    });

  } catch (error) {
    console.error('Escrow status error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
