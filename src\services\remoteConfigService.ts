import { getRemoteConfig, fetchAndActivate, getValue, getAll } from "firebase/remote-config";
import { initFirebase } from "../../firebaseConfig";

// For ESLint
/* global console, process */

// Initialize and fetch Remote Config
export const initRemoteConfig = async () => {
  try {
    const { app } = await initFirebase();
    const remoteConfig = getRemoteConfig(app);

    // Set minimum fetch interval for development
    remoteConfig.settings.minimumFetchIntervalMillis =
      process.env.NODE_ENV === "development" ? 0 : 3600000; // 1 hour in production

    // Fetch and activate the config
    const activated = await fetchAndActivate(remoteConfig);
    return { success: true, remoteConfig, activated };
  } catch (error) {
    console.error("Error initializing Remote Config:", error);
    return { success: false, error: "Failed to initialize Remote Config" };
  }
};

// Get a specific value from Remote Config
export const getRemoteConfigValue = async (key: string) => {
  try {
    const { success, remoteConfig, error } = await initRemoteConfig();

    if (!success || !remoteConfig) {
      return { success: false, error };
    }

    const value = getValue(remoteConfig, key);
    return { success: true, value: value.asString() };
  } catch (error) {
    console.error(`Error fetching Remote Config value for ${key}:`, error);
    return { success: false, error: "Failed to fetch Remote Config value" };
  }
};

// Get the "about" value from Remote Config
export const getAboutContent = async () => {
  return getRemoteConfigValue("about");
};

export const getTermsAndConditions = async () => {
  return getRemoteConfigValue("termsAndConditions");
};

export async function fetchHelpFAQGroup() {
  try {
    const { success, remoteConfig, error } = await initRemoteConfig();
    // console.log({remoteConfig});

    if (!remoteConfig) {
      return {};
    }

    const allParams = getAll(remoteConfig);
    // console.log({allParams});

    const helpFAQParams = Object.entries(allParams)
      .filter(([key]) => key.startsWith("faq"))
      .reduce(
        (acc, [key, val]) => {
          acc[key] = val.asString();
          return acc;
        },
        {} as Record<string, string>
      );

    return helpFAQParams;
  } catch (error) {
    console.error("Failed to fetch Help FAQ Remote Config group:", error);
    return {};
  }
}
