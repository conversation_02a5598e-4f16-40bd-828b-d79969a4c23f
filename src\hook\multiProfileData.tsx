import { User } from "@/services/UserInterface";
import { getUserById } from "@/services/usersServices";
import { useEffect, useState } from "react";

const useMultiProfiles = (userIds: string[]) => {
  const [profiles, setProfiles] = useState<any[] | null>(null); // State for storing user data
  const [loading, setLoading] = useState<boolean>(false); // Loading state
  const [error, setError] = useState<string | null>(null); // Error state

  useEffect(() => {
    const fetchUsers = async () => {
      if (!userIds || userIds.length === 0) {
        setError("User IDs are required.");
        return;
      }

      setLoading(true); // Start loading state

      try {
        // Fetch all users in parallel using Promise.all
        const responses = await Promise.all(
          userIds.map((id) => getUserById(id))
        );

        // Filter out successful responses
        const successfulUsers = responses
          .filter((res) => res.success)
          .map((res) => res.user);

        setProfiles(successfulUsers); // Update state with fetched users
        setError(null); // Clear any previous errors
      } catch (err) {
        setError("An error occurred while fetching the users.");
      } finally {
        setLoading(false); // Stop loading state
      }
    };

    if (userIds.length > 0) {
      fetchUsers(); // Call the function when userIds are available
    }
  }, [userIds]); // Run effect whenever `userIds` changes

  return { profiles, loading, error };
};

export default useMultiProfiles;
