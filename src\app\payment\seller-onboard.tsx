import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';

export default function SellerOnboard() {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const { user, loading: userLoading, isAuthenticated } = useCurrentUser();

  // Set default email when user loads
  useEffect(() => {
    if (user?.email) {
      setEmail(user.email);
    }
  }, [user?.email]);

  const handleOnboard = async () => {
    if (!user?.uid) {
      alert('Please log in first to become a seller');
      return;
    }

    if (!email.trim()) {
      alert('Please provide an email address');
      return;
    }

    setLoading(true);
    try {
      const res = await fetch('/api/connect/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
          email: email.trim()
        })
      });

      const data = await res.json();

      if (data.error) {
        alert(data.error);
        setLoading(false);
        return;
      }

      window.location.href = data.url; // redirect seller to Stripe onboarding
    } catch (error) {
      console.error('Error starting onboarding:', error);
      alert('Failed to start onboarding process');
      setLoading(false);
    }
  };

  if (userLoading) {
    return (
      <div>
        <h1>Become a Seller</h1>
        <p>Loading...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div>
        <h1>Become a Seller</h1>
        <p>Please log in to start the seller onboarding process.</p>
      </div>
    );
  }

  return (
    <div>
      <h1>Become a Seller</h1>
      <p>User ID: {user.uid}</p>

      <div style={{ marginBottom: '20px' }}>
        <label htmlFor="email" style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          Email for Stripe Account:
        </label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter email address"
          style={{
            width: '100%',
            maxWidth: '400px',
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '14px'
          }}
          disabled={loading}
        />
        <small style={{ color: '#666', fontSize: '12px' }}>
          This email will be used for your Stripe account. You can edit it before proceeding.
        </small>
      </div>

      <button onClick={handleOnboard} disabled={loading || !email.trim()}>
        {loading ? 'Redirecting...' : 'Connect with Stripe'}
      </button>
    </div>
  );
}
