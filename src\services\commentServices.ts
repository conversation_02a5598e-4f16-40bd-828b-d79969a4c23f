import { getAuth } from "firebase/auth";
import {
  collection,
  doc,
  documentId,
  getDoc,
  getDocs,
  getFirestore,
  query,
  serverTimestamp,
  setDoc,
  updateDoc,
  where,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { NotificationEvents, NotificationManager } from "./notificationService";
import { GetPostDetailsByPostId } from "./postService";

export interface Comment {
  id?: string;
  post_id: string;
  user_id: string;
  message: string;
  created_at?: string;
  updated_at?: string;
  edited?: string;
  hidden: boolean;
}

// const resp = await CommentManager.getInstance().AddComment({
//   hidden:false ,
//   message:"asdaskldnas daslndas d",
//   post_id:"ECPFkWgf0aXdKUk9kCrA",
//   user_id:"eEeCcJ3In5bWGHbauY4nRHgxuOn2",
// })

// const resp = await CommentManager.getInstance().UpdateComment("FN36x7rYvQfXJgVVqxXv","ssss s ss ss sasda" , "eEeCcJ3In5bWGHbauY4nRHgxuOn2");

// const resp = await CommentManager.getInstance().GetCommentsByPostId("ECPFkWgf0aXdKUk9kCrA")

export class CommentManager {
  private COMMENT_COLLECTION = "comments";

  static instance: CommentManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new CommentManager();
    }
    return this.instance;
  }

  async AddComment(data: Comment) {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user?.uid) {
        return { success: false };
      }
      const db = getFirestore();
      const commentsRef = collection(db, this.COMMENT_COLLECTION);
      const newCommentRef = doc(commentsRef);
      const newCommentId = newCommentRef.id;

      const resp = await setDoc(doc(commentsRef, newCommentId), {
        id: newCommentId,
        ...data,
        edited: false,
        updated_at: serverTimestamp(),
        created_at: serverTimestamp(),
      });

      // add notification

      const post_details = await GetPostDetailsByPostId({post_id:data.post_id});
      
      if(!post_details?.id || 
        user.uid === post_details?.user_id // no notification for own commment
      ) { 
        return resp;
      }

      NotificationManager.getInstance().CreateNotification({
        payload:{
          src_id:user?.uid , 
          dest_id:post_details?.user_id , 
          event:NotificationEvents.COMMENT, 
          comment:data.message , 
          comment_id:newCommentId,
          post_id:data?.post_id,
          post_url:post_details?.postFile , 
          thumbnail_url:post_details?.thumbnailUrl,
        }
      })


      return resp;
    } catch (error) {
      throw new Error("add_comment_failed");
    }
  }

  async UpdateComment(
    commentId: string,
    updatedMessage: string,
    userId: string
  ) {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user?.uid) {
        return { success: false };
      }
      const { db } = await initFirebase();
      const commentRef = doc(db, "comments", commentId);
      const commentSnap = await getDoc(commentRef);

      if (!commentSnap.exists()) {
        throw new Error("comment_not_found");
      }

      const commentData = commentSnap.data();

      if (commentData.user_id !== user.uid) {
        throw new Error("unauthorized_action");
      }

      await updateDoc(commentRef, {
        message: updatedMessage,
        edited: true,
        updated_at: serverTimestamp(),
      });

      return { success: true, message: "Comment updated successfully" };
    } catch (error) {
      console.error("Error updating comment:", error);
      throw new Error("update_comment_failed");
    }
  }

  async GetCommentsByUserId(userId: string) {
    try {
      const db = getFirestore();
      const commentsRef = collection(db, this.COMMENT_COLLECTION);

      const q = query(
        commentsRef,
        where("user_id", "==", userId),
        where("hidden", "==", false)
      );

      const querySnapshot = await getDocs(q);

      const comments = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return comments;
    } catch (error) {
      console.error("Error fetching comments by user ID:", error);
      throw new Error("get_comment_by_user_id_failed");
    }
  }

  async GetCommentsByPostId(postId: string) {
    try {
      const { db } = await initFirebase();
      const commentsRef = collection(db, this.COMMENT_COLLECTION);

      // Define a type for comment data with the Timestamp
      type CommentData = {
        id: string;
        user_id?: string;
        post_id: string;
        created_at: {
          seconds: number;
          nanoseconds: number;
        };
        profile_name: string | null;
        // Add other comment fields here
        [key: string]: any; // For other fields from commentData
      };

      const q = query(
        commentsRef,
        where("post_id", "==", postId),
        where("hidden", "==", false)
      );
      const querySnapshot = await getDocs(q);

      const comments: CommentData[] = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const commentData = doc.data();
          const userId = commentData.user_id;

          // Fetch the user profile
          if (userId) {
            const usersRef = collection(db, "users");
            const userQuery = query(
              usersRef,
              where(documentId(), "==", userId)
            );
            const userSnapshot = await getDocs(userQuery);

            if (!userSnapshot.empty) {
              const userData = userSnapshot.docs[0].data();

              return {
                id: doc.id,
                ...commentData,
                profile_name: userData.profile_name || null,
              } as CommentData;
            }
          }

          return {
            id: doc.id,
            ...commentData,
            profile_name: null,
          } as CommentData;
        })
      );

      // Sort comments by created_at timestamp (newest first)
      // This puts the most recent comments at the top
      const sortedComments = comments.sort((a, b) => {
        // First compare by seconds (descending order - newest first)
        if (a.created_at.seconds !== b.created_at.seconds) {
          return b.created_at.seconds - a.created_at.seconds;
        }
        // If seconds are equal, compare by nanoseconds (descending order)
        return b.created_at.nanoseconds - a.created_at.nanoseconds;
      });

      return sortedComments;
    } catch (error) {
      console.error("Error fetching comments by post ID:", error);
      throw new Error("get_comments_by_post_id_failed");
    }
  }
}
