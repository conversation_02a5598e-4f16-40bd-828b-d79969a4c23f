import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  setDoc,
  Timestamp,
  increment,
  arrayRemove,
} from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";

import { query, limit, startAfter, orderBy, where } from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { getAuth } from "firebase/auth";
import { User } from "./UserInterface";

// import { postsCollection } from "@/firebase";

// Collection reference

export interface Post {
  id?: string; // Optional since it may not exist before creation
  hashtags: string[]; // Array of hashtags
  postFile: string; // Path or URL to the post's file
  category: string; // Category of the post
  mediaType: "image" | "video"; // Media type with defined possible values
  lastEditDate?: Timestamp; // Optional, Timestamp type for last edit date
  starred: number; // Number of stars the post has received
  added_at?: Timestamp; // Optional, Timestamp type for added date
  thumbnailUrl?: string; // Optional, URL for the thumbnail
  geotags: string[]; // Array of geotags
  about_project: string; // Description of the project
  deleted?: boolean;
  user_id: string;
}



// 📌 Create a new post
export const createPost = async (data: any) => {
  try {
    const { db } = await initFirebase();
    const postsCollection = collection(db, "posts");
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user?.uid) {
      return { success: false, id: null };
    }

    const newPostRef = doc(postsCollection);
    const newPostsId = newPostRef.id;

    await setDoc(doc(postsCollection, newPostsId), {
      id: newPostsId,
      ...data,
      added_at: serverTimestamp(),
      lastEditDate: serverTimestamp(),
    });

    // async createNotification to all the data.user_id followers
    
    const followers = await FollowerManager.getInstance().GetFollowersByUserId(user?.uid);
    NotificationManager.getInstance().BulkCreateNotificationsAndUpdateUnreadCounts({
      event:NotificationEvents.POST_UPLOAD , 
      followers,
      userId:user?.uid , 
      postData:{
        postFile:data?.postFile,
        postId:newPostsId ,
        thumbnailUrl:data?.thumbnailUrl,
      }
    })
    
    return { success: true, id: newPostsId };
  } catch (error) {
    console.error("Error creating post:", error);
    return { success: false, error: "Server error" };
  }
};

export const GetPostDetailsByPostId = async ({ post_id }: { post_id: string }):Promise<Post | null> => {
  try {
    const { db } = await initFirebase();

    const postRef = doc(db, "posts", post_id);
    const postSnap = await getDoc(postRef);

    if (!postSnap.exists()) {
      console.log(`Post with ID ${post_id} does not exist.`);
      return null;
    }

    const data = postSnap.data();
    return data as Post;
    
  } catch (error) {
    console.log({ error });
    throw new Error("GetPostDetailsByPostId_failed");
    
  }
};

// check delete
// 📌 Get all posts
export const getAllPosts = async () => {
  try {
    const { db } = await initFirebase();
    const postsCollection = collection(db, "posts");

    const snapshot = await getDocs(postsCollection);
    const posts: Post[] = snapshot.docs
      .map((doc) => ({ id: doc.id, ...doc.data() }) as Post)
      .filter((post) => post.deleted !== true);

    return { success: true, posts };
  } catch (error) {
    // console.error("Error fetching posts:", error);
    return { success: false, error: "Server error" };
  }
};

// 📌 Get all posts on limited

// export const getAllPostsTest = async () => {
//   try {
//     const { db } = await initFirebase();

//     const postsSnapshot = await getDocs(collection(db, "posts")); // 'posts' is the Firestore collection name
//     const posts = postsSnapshot.docs.map((doc) => ({
//       id: doc.id,
//       ...doc.data(),
//     }));
//     return { posts };
//   } catch (error) {
//     // console.error("Error fetching posts:", error);
//     return { posts: [] };
//   }
// };
export const getAllPostsTest = async () => {
  try {
    const { db } = await initFirebase();

    const postsSnapshot = await getDocs(collection(db, "posts")); // Fetch all posts
    const posts = postsSnapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        timestamp: data.added_at || data.lastEditDate || null,
      };
    });

    const usersSnapshot = await getDocs(collection(db, "users")); // Fetch all users
    const users: User[] = usersSnapshot.docs.map(
      (doc) =>
        ({
          id: doc.id,
          ...doc.data(),
        }) as User
    );

    // Create a Set of valid post IDs (posts linked to non-deleted users)
    const validPostIds = new Set<string>();
    const postToUserMap = new Map<string, string>();

    users.forEach((user: User) => {
      if (!user.isDeleted && Array.isArray(user.posts)) {
        user.posts.forEach((postId: string) => {
          validPostIds.add(postId);
          postToUserMap.set(postId, user.id);
        });
      }
    });

    // Filter posts to only include ones that are valid
    const validPosts = posts
      .filter((post) => validPostIds.has(post.id))
      .map((post) => ({
        ...post,
        user_id: postToUserMap.get(post.id) || null, // Attach user_id
      }));

    // Sort valid posts by timestamp (most recent first)
    const sortedPosts = validPosts.sort((a, b) => {
      return (b.timestamp?.seconds || 0) - (a.timestamp?.seconds || 0);
    });

    return { posts: sortedPosts };
  } catch (error) {
    console.error("Error fetching posts:", error);
    return { posts: [] };
  }
};

// export const getAllPostsTest = async () => {
//   try {
//     const {db} =  await initFirebase();

//     const postsSnapshot = await getDocs(collection(db, "posts")); // Fetch all posts
//     const posts = postsSnapshot.docs.map((doc) => {
//       const data = doc.data();
//       return {
//         id: doc.id,
//         ...data,
//         timestamp: data.added_at || data.lastEditDate || null, // Use added_at, fallback to lastEditDate
//       };
//     });

//     // Sort posts by timestamp (most recent first)
//     const sortedPosts = posts.sort((a, b) => {
//       return (b.timestamp?.seconds || 0) - (a.timestamp?.seconds || 0);
//     });

//     return { posts: sortedPosts };
//   } catch (error) {
//     console.error("Error fetching posts:", error);
//     return { posts: [] };
//   }
// };

// get image on load

export const getAllPostsPaginated = async (category: any, lastVisible: any) => {
  try {
    const { db } = await initFirebase();

    const postsRef = collection(db, "posts");

    let postsQuery = query(
      postsRef,
      where("category", "==", category), // Filter by category
      orderBy("added_at", "desc"), // Order by creation date (ensure this field exists)
      limit(10) // Fetch 10 items at a time
    );

    if (lastVisible) {
      postsQuery = query(postsQuery, startAfter(lastVisible)); // Start after the last fetched document
    }

    const snapshot = await getDocs(postsQuery);
    const posts = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    // console.log(posts);

    return {
      posts,
      lastVisible: snapshot.docs[snapshot.docs.length - 1], // Track the last document for pagination
    };
  } catch (error) {
    // console.error("Error fetching paginated posts:", error);
    return { posts: [], lastVisible: null };
  }
};

// 📌 Get a post by ID
export const getPostById = async (id: string) => {
  try {
    const { db } = await initFirebase();
    const postsCollection = collection(db, "posts");

    const postRef = doc(postsCollection, id);
    const postSnap = await getDoc(postRef);

    if (postSnap.exists()) {
      return {
        success: true,
        post: { id: postSnap.id, ...postSnap.data() } as Post,
      };
    } else {
      return { success: false, error: "Post not found" };
    }
  } catch (error) {
    // console.error("Error fetching post:", error);
    return { success: false, error: "Server error" };
  }
};

export const getPostByIdCat = async (id: string, categoryName?: string) => {
  try {
    const { db } = await initFirebase();
    const postsCollection = collection(db, "posts");

    const postRef = doc(postsCollection, id);
    const postSnap = await getDoc(postRef);

    if (postSnap.exists()) {
      const postData = { id: postSnap.id, ...postSnap.data() } as Post;

      // If categoryName is provided, filter the post
      if (categoryName && postData.category !== categoryName) {
        return { success: false, error: "Post does not match the category" };
      }

      return { success: true, post: postData };
    } else {
      return { success: false, error: "Post not found" };
    }
  } catch (error) {
    return { success: false, error: "Server error" };
  }
};

// get post by any arrya id

// 📌 Update a post by ID
export const updatePost = async (id: string, updatedData: any) => {
  try {
    const { db } = await initFirebase();
    const postsCollection = collection(db, "posts");
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user?.uid) {
      return { success: false };
    }

    //////////////////////////////////////////////////////////// Authorization check
    const usersCollection = collection(db, "users");
    const q = query(usersCollection, where("posts", "array-contains", id));
    const userSnap = await getDocs(q);
    if (!userSnap.empty) {
      const userDoc = userSnap.docs[0]; // assuming post ID is unique to one user
      //📋 check if this user if the current authenticated user then only update else return un-auth
      if (userDoc?.data()?.id !== user?.uid) {
        return { success: false };
      }
    }
    ////////////////////////////////////////////////////////////

    const postRef = doc(postsCollection, id);

    await updateDoc(postRef, {
      ...updatedData,
      lastEditDate: serverTimestamp(),
    });

    return { success: true };
  } catch (error) {
    // console.error("Error updating post:", error);
    return { success: false, error: "Server error" };
  }
};

// 📌 Delete a post by ID
export const deletePost = async (id: string) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user?.uid) {
      return { success: false };
    }
    const postsCollection = collection(db, "posts");
    // 1. Find the user who has this post ID in their posts[]
    const usersCollection = collection(db, "users");
    const q = query(usersCollection, where("posts", "array-contains", id));
    const userSnap = await getDocs(q);

    if (!userSnap.empty) {
      const userDoc = userSnap.docs[0]; // assuming post ID is unique to one user
      // 3. Remove the post ID from the posts[] array field

      //📋 check if this user if the current authenticated user then only delete else return un-auth
      if (userDoc?.data()?.id !== user?.uid) {
        return { success: false };
      }
      await updateDoc(userDoc.ref, {
        posts: arrayRemove(id),
      });
    }

    // 2. Delete the post document
    const postRef = doc(postsCollection, id);
    await deleteDoc(postRef);

    return { success: true };
  } catch (error) {
    // console.error("Error deleting post:", error);
    return { success: false, error: "Server error" };
  }
};

export const updatePostStars = async (postId: string, isStarring: boolean) => {
  try {
    const { db } = await initFirebase();
    const postsCollection = collection(db, "posts");
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user?.uid) {
      return { success: false };
    }
    const postRef = doc(postsCollection, postId);

    await updateDoc(postRef, {
      starred: increment(isStarring ? 1 : -1), // Increase or decrease stars count
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: "Server error" };
  }
};

// get filtered users based on category

export async function getUsersSortedByLastPost(category: string, user_id?: string) {
  const { db } = await initFirebase();
  console.time("getUsersSortedByLastPost");

  let userSnapshots;

  if (category === "my-feed") {
    if (!user_id) throw new Error("user_id is required for my-feed category");

    const currentUserSnap = await getDoc(doc(db, "users", user_id));
    if (!currentUserSnap.exists()) throw new Error("User not found");

    const bookmarkedUserIds: string[] = currentUserSnap.data().bookmarks || [];
    const bookmarkedUserDocs = await Promise.all(
      bookmarkedUserIds.map((uid) => getDoc(doc(db, "users", uid)))
    );
    userSnapshots = bookmarkedUserDocs.filter((docSnap) => docSnap.exists());
  } else {
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("categories", "array-contains", category));
    const snapshot = await getDocs(q);
    userSnapshots = snapshot.docs;
  }

  const userPostMap: Map<string, string[]> = new Map();

  // console.log({ userSnapshots });
  for (const docSnap of userSnapshots) {
    const user = docSnap.data();
    if (user.posts && user.posts.length > 0) {
      userPostMap.set(docSnap.id, user.posts.slice().reverse()); // reversed for latest
    }
  }

  const allPostIds = Array.from(userPostMap.values()).flat();

  // Firestore allows 10 per `in` query, so we chunk
  const chunkArray = <T>(arr: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < arr.length; i += size) {
      chunks.push(arr.slice(i, i + size));
    }
    return chunks;
  };

  const postDocsMap: Map<string, any> = new Map();

  const postChunks = chunkArray(allPostIds, 10);
  for (const chunk of postChunks) {
    const postQuery = query(collection(db, "posts"), where("__name__", "in", chunk));
    const snapshot = await getDocs(postQuery);
    snapshot.forEach((doc) => postDocsMap.set(doc.id, doc.data()));
  }

  const result: {
    userId: string;
    latestPostTime: number;
  }[] = [];

  for (const [userId, postIds] of userPostMap.entries()) {
    for (const postId of postIds) {
      const post = postDocsMap.get(postId);
      if (!post) continue;

      if ((category === "my-feed" || post.category === category) && post.added_at) {
        result.push({
          userId,
          latestPostTime: post.added_at.toMillis(),
        });
        break;
      }
    }
  }

  result.sort((a, b) => b.latestPostTime - a.latestPostTime);
  console.timeEnd("getUsersSortedByLastPost");

  return result.map((r) => r.userId);
}

// export const backfillPostUserIds = async () => {
//   const { db } = await initFirebase();
//   const usersSnapshot = await getDocs(collection(db, "users"));
//   let failed_post_ids: string[] = [];
//   let post_ids: string[] = [];
//   let skipped_ids: string[] = [];
//   console.log("######################");

//   for (const userDoc of usersSnapshot.docs) {
//     const userId = userDoc.id;
//     const userData = userDoc.data();

//     if (!Array.isArray(userData.posts)) continue;

//     for (const postId of userData.posts) {
//       try {
//         const postRef = doc(db, "posts", postId);
//         const postSnap = await getDoc(postRef);

//         if (!postSnap.exists()) {
//           skipped_ids.push(postId)
//           continue;
//         }

//         const resp = await updateDoc(postRef, { user_id: userId });
//         post_ids.push(postId);
//         console.log(`✅ Updated post ${postId} with user_id ${userId} with status : ` , resp);
//       } catch (error) {
//         failed_post_ids.push(postId);
//         console.warn(`⚠️ Failed to update post ${postId}:`, error);
//       }
//     }
//   }

//   console.log("🎉 Backfill complete.");
//   console.log("Updated ", post_ids?.length);
//   console.log("skipped_ids ", skipped_ids?.length);

//   if(failed_post_ids?.length){
//     console.log(JSON.stringify(failed_post_ids , null ,0));
//   }

//   return "ok"

// };

/**
 * Options for fetching posts with pagination
 */
interface GetAllPostsOptions {
  category_name: string; // Category name to filter posts
  userId?: string; // Required for "my-feed" category
  pageSize: number; // Number of posts to fetch per page
  lastVisibleTimestamp?: Timestamp; // Timestamp for pagination
}

/**
 * Optimized function to fetch posts with pagination
 *
 * @param category_name - Category to fetch posts for
 * @param pageSize - Number of posts to fetch per page
 * @param lastVisibleTimestamp - Last visible timestamp for pagination (from previous response)
 * @param userId - User ID (required for "my-feed" category)
 * @returns  Object containing posts array, lastVisible timestamp, and hasMore flag
 * {
 "posts": [
  {
   "id": "d20C1nn2F5IrlnIOgPLz",
   "mediaType": "image",
   "category": "Music",
   "about_project": "test",
   "geotags": [
    "India"
   ],
   "hashtags": [
    "Flag"
   ],
   "added_at": {
    "seconds": 1747465361,
    "nanoseconds": 855000000
   },
   "postFile": "https://firebasestorage.googleapis.com/v0/b/amuzn-webapp.firebasestorage.app/o/images%2F1747465356808_image-removebg-preview.png?alt=media&token=8590a90b-4c30-4637-8b1a-8b05a5a9d033",
   "user_id": "i5TfGRWE46ZslwNOb2ROSqoldTd2",
   "lastEditDate": {
    "seconds": 1747465361,
    "nanoseconds": 855000000
   },
   "starred": 0,
   "timestamp": {
    "seconds": 1747465361,
    "nanoseconds": 855000000
   }
  }
 ],
 "lastVisible": {
  "seconds": 1747465361,
  "nanoseconds": 855000000
 },
 "hasMore": true
}
 *
 *
 */
export const getAllPostsOptimized = async ({
  category_name,
  pageSize,
  lastVisibleTimestamp,
  userId,
}: GetAllPostsOptions) => {
  try {
    const { db } = await initFirebase();

    // 📰 Handle "my-feed" category - requires userId
    if (category_name === "my-feed") {
      if (!userId) {
        throw new Error("userId is required for 'my-feed' category");
      }

      // Get user data to find bookmarked users
      const userSnap = await getDoc(doc(db, "users", userId));
      if (!userSnap.exists()) {
        return { posts: [], lastVisible: null, hasMore: false };
      }

      const userData = userSnap.data();
      const bookmarkedUserIds: string[] = Array.isArray(userData?.bookmarks)
        ? userData.bookmarks
        : [];

      // Return empty result if no bookmarks
      if (bookmarkedUserIds.length === 0) {
        return { posts: [], lastVisible: null, hasMore: false };
      }

      // 🔄 Break into chunks of 10 for "in" queries (Firestore limitation)
      const userChunks: string[][] = [];
      for (let i = 0; i < bookmarkedUserIds.length; i += 10) {
        userChunks.push(bookmarkedUserIds.slice(i, i + 10));
      }

      let allPosts: any[] = [];

      // Fetch posts for each chunk of bookmarked users
      for (const chunk of userChunks) {
        const postQuery = query(
          collection(db, "posts"),
          where("user_id", "in", chunk),
          orderBy("added_at", "desc"),
          ...(lastVisibleTimestamp ? [startAfter(lastVisibleTimestamp)] : []),
          limit(pageSize)
        );

        const postSnap = await getDocs(postQuery);

        postSnap.forEach((doc) => {
          const data = doc.data();
          allPosts.push({
            id: doc.id,
            ...data,
            timestamp: data.added_at || data.timestamp || data.lastEditDate || null,
          });
        });
      }

      // Sort all posts by timestamp (in case they came from different queries)
      allPosts.sort((a, b) => {
        const aTime = a.timestamp?.toMillis ? a.timestamp.toMillis() : 0;
        const bTime = b.timestamp?.toMillis ? b.timestamp.toMillis() : 0;
        return bTime - aTime; // Descending order (newest first)
      });

      // Get paginated posts
      const paginatedPosts = allPosts.slice(0, pageSize);

      // Get the last visible timestamp for pagination
      const lastVisible =
        paginatedPosts.length > 0 ? paginatedPosts[paginatedPosts.length - 1].timestamp : null;

      return {
        posts: paginatedPosts,
        lastVisible,
        hasMore: allPosts.length > pageSize,
      };
    }

    // Handle special case for Literature/Storytelling categories
    let categories = ["Storytelling", "Literature"].includes(category_name)
      ? ["Storytelling", "Literature"]
      : [category_name];

    // 📂 Category-based query
    const postsQuery = query(
      collection(db, "posts"),
      where("category", "in", categories),
      orderBy("added_at", "desc"),
      ...(lastVisibleTimestamp ? [startAfter(lastVisibleTimestamp)] : []),
      limit(pageSize)
    );

    const postSnap = await getDocs(postsQuery);

    const posts = postSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        timestamp: data.added_at || data.timestamp || data.lastEditDate || null,
      };
    });

    // Get the last visible timestamp for pagination
    const lastVisible = posts.length > 0 ? posts[posts.length - 1].timestamp : null;

    return {
      posts,
      lastVisible,
      hasMore: posts.length === pageSize, // If we got a full page, there might be more
    };
  } catch (error) {
    console.error("Error fetching posts:", error);
    return { posts: [], lastVisible: null, hasMore: false };
  }
};

export const _getAllPostsOptimized = async ({
  category_name,
  pageSize,
  lastVisibleTimestamp,
  userId,
}: GetAllPostsOptions) => {
  try {
    const { db } = await initFirebase();

    // 🔁 "my-feed"
    if (category_name === "my-feed") {
      if (!userId) {
        throw new Error("userId is required for 'my-feed'");
      }

      const userSnap = await getDoc(doc(db, "users", userId));
      const userData = userSnap.data();
      const bookmarkedUserIds: string[] = Array.isArray(userData?.bookmarks)
        ? userData.bookmarks
        : [];

      if (bookmarkedUserIds.length === 0) {
        return { posts: [], lastVisible: null, hasMore: false };
      }

      const userChunks: string[][] = [];
      for (let i = 0; i < bookmarkedUserIds.length; i += 10) {
        userChunks.push(bookmarkedUserIds.slice(i, i + 10));
      }

      let allPosts: any[] = [];

      for (const chunk of userChunks) {
        const postQuery = query(
          collection(db, "posts"),
          where("user_id", "in", chunk)
          // limit(1000) // Fetch more to sort manually
        );
        const postSnap = await getDocs(postQuery);

        postSnap.forEach((doc) => {
          const data = doc.data();
          allPosts.push({
            id: doc.id,
            ...data,
            timestamp: data.added_at || data.timestamp || data.lastEditDate || null,
          });
        });
      }

      // 🧠 Manual sorting + pagination
      allPosts = allPosts
        .filter((p) => p.timestamp)
        .sort((a, b) => (b.timestamp?.seconds || 0) - (a.timestamp?.seconds || 0));

      if (lastVisibleTimestamp) {
        allPosts = allPosts.filter(
          (p) => (p.timestamp?.seconds || 0) < lastVisibleTimestamp.seconds
        );
      }

      const paginatedPosts = allPosts.slice(0, pageSize);
      const lastVisible = paginatedPosts.length
        ? paginatedPosts[paginatedPosts.length - 1].timestamp
        : null;

      return {
        posts: paginatedPosts,
        lastVisible,
        hasMore: allPosts.length > pageSize,
      };
    }

    // 📂 Category-based
    const categories = ["Storytelling", "Literature"].includes(category_name)
      ? ["Storytelling", "Literature"]
      : [category_name];

    const postQuery = query(
      collection(db, "posts"),
      where("category", "in", categories)
      // limit(1000) // Fetch more for manual filtering
    );

    const postSnap = await getDocs(postQuery);

    let posts = postSnap.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        timestamp: data.added_at || data.timestamp || data.lastEditDate || null,
      };
    });

    posts = posts
      .filter((p) => p.timestamp)
      .sort((a, b) => (b.timestamp?.seconds || 0) - (a.timestamp?.seconds || 0));

    if (lastVisibleTimestamp) {
      posts = posts.filter((p) => (p.timestamp?.seconds || 0) < lastVisibleTimestamp.seconds);
    }

    const paginated = posts.slice(0, pageSize);
    const lastVisible = paginated.length ? paginated[paginated.length - 1].timestamp : null;

    return {
      posts: paginated,
      lastVisible,
      hasMore: posts.length > pageSize,
    };
  } catch (error) {
    console.error("Error fetching posts:", error);
    return { posts: [], lastVisible: null, hasMore: false };
  }
};

/**
 * Gets all posts optimized and grouped by user ID
 *
 * This function extends getAllPostsOptimized by grouping the returned posts by user_id
 *
 * @param options - Same options as getAllPostsOptimized
 * @returns Object with posts grouped by user_id, lastVisible timestamp, and hasMore flag
 *
 * Example return value:
 * {
 *   "user1": [
 *     {id: "post3", user_id: "user1", date: "2023-01-04", ...},
 *     {id: "post1", user_id: "user1", date: "2023-01-02", ...}
 *   ],
 *   "user2": [
 *     {id: "post2", user_id: "user2", date: "2023-01-03", ...}
 *   ],
 *   "lastVisible": Timestamp,
 *   "hasMore": true
 * }
 */
import { getUsersByIds } from "@/services/usersServices"; // Make sure this import is available
import { FollowerManager } from "./followServices";
import { NotificationEvents, NotificationManager } from "./notificationService";

export const getAllPostsOptimizedByUser = async (options: GetAllPostsOptions) => {
  try {
    // First, get the posts using the existing function
    const { posts, lastVisible, hasMore } = await getAllPostsOptimized(options);

    // Group posts by user_id
    const postsByUser: Record<string, any[]> = {};
    const userIds = new Set<string>();

    // Process each post
    for (const post of posts) {
      // Skip posts without user_id
      if (!post.user_id) continue;

      // Track unique user IDs
      userIds.add(post.user_id);

      // Initialize array for this user if it doesn't exist
      if (!postsByUser[post.user_id]) {
        postsByUser[post.user_id] = [];
      }

      // Add post to the user's array
      postsByUser[post.user_id].push(post);
    }

    // Fetch user data for all the unique user IDs we found
    const userIdsArray = Array.from(userIds);
    const usersResponse = await getUsersByIds(userIdsArray);

    // Create the final result object with user data and their posts
    const result: Record<string, any> = {};

    // Check the response structure and extract users data
    let usersData: any[] = [];
    if (usersResponse && usersResponse.success === true && usersResponse.users) {
      // Standard success response
      usersData = usersResponse.users;
    } else if (usersResponse && usersResponse.users) {
      // Single user response
      usersData = [usersResponse.users];
    } else if (Array.isArray(usersResponse)) {
      // Direct array response
      usersData = usersResponse;
    } else {
      console.warn("Unexpected users response format:", usersResponse);
      // Continue with just the posts without user data
    }

    // For each user ID in our posts
    for (const userId of userIdsArray) {
      // Find the user data from the fetched users, or use a minimal placeholder
      const userData = usersData.find((user: any) => user && user.id === userId) || { id: userId };

      // Sort this user's posts by date (newest first)
      const userPosts = postsByUser[userId].sort((a, b) => {
        const aTime = a.timestamp?.toMillis
          ? a.timestamp.toMillis()
          : a.timestamp?.seconds
            ? a.timestamp.seconds
            : 0;
        const bTime = b.timestamp?.toMillis
          ? b.timestamp.toMillis()
          : b.timestamp?.seconds
            ? b.timestamp.seconds
            : 0;
        return bTime - aTime; // Descending order (newest first)
      });

      // Store in the result object with both user data and posts
      result[userId] = {
        user: userData, // User information
        posts: userPosts, // Sorted posts array
      };
    }

    // Return the grouped posts with user data along with pagination info
    return {
      ...result,
      lastVisible,
      hasMore,
    };
  } catch (error) {
    console.error("Error in getAllPostsOptimizedByUser:", error);
    return { lastVisible: null, hasMore: false };
  }
};

export interface PaginatedUserResults {
  results: {
    user_id: string;
    user_name: string;
    user_location: string;
    user_profile_pic: string;
    is_followed_by_me: boolean;
    posts: Post[];
  }[];
  startAfterUserId?: string;
  more: boolean; // true
}
type GroupedPosts = {
  [user_id: string]: {
    user_id: string;
    posts: any[];
  };
};
// user id
export async function getPaginatedUsersByCategoryPosts({
  category,
  loggedInUserId,
  pageSize,
  startAfterUserId,
  exclude_user_id,
  // postId
}: {
  category: string;
  loggedInUserId?: string | null;
  pageSize: number;
  startAfterUserId?: string; // used for paginating user groups
  exclude_user_id?: string;
}): Promise<PaginatedUserResults | undefined> {
  try {
    const { db } = await initFirebase();
    // console.log({ category });

    console.time("first");

    // Step 1: Get logged-in user's bookmarks
    // put this after for not my-feed to optimise
    console.time("🚀");

    let bookmarks: string[] = [];
    if (loggedInUserId) {
      const userSnap = await getDoc(doc(db, "users", loggedInUserId));
      if (userSnap.exists()) {
        bookmarks = userSnap.data()?.bookmarks || [];
      }
    }
    console.timeEnd("🚀");

    //  Assemble final response
    const results: any = [];

    if (category === "my-feed") {
      const posts = await getAllPosts();

      // Step 1: Filter by bookmarked user IDs
      const filteredPosts = posts.posts?.filter((post) => bookmarks?.includes(post.user_id)) || [];

      // Step 2: Sort by added_at descending
      filteredPosts.sort((a, b) => {
        const aDate = a.added_at?.toDate?.() || new Date(0);
        const bDate = b.added_at?.toDate?.() || new Date(0);
        return bDate.getTime() - aDate.getTime();
      });

      const grouped: GroupedPosts = {};

      for (const post of filteredPosts) {
        const uid = post.user_id;
        if (!grouped[uid]) {
          grouped[uid] = {
            user_id: uid,
            posts: [],
          };
        }
        grouped[uid].posts.push(post);
      }
      const groupedArray = Object.values(grouped);

      // console.log({ grouped, groupedArray });

      let allUserIds = Object.keys(grouped);

      // Pagination: start after a specific user_id
      if (startAfterUserId) {
        const startIndex = allUserIds.indexOf(startAfterUserId);
        if (startIndex !== -1) {
          allUserIds = allUserIds.slice(startIndex + 1);
        }
      }

      // Limit to pageSize
      const paginatedUserIds = allUserIds.slice(0, pageSize);

      const _userQuery = query(
        collection(db, "users"),
        where("__name__", "in", paginatedUserIds)
        // where("user_id", "==", userId),
        // where("deleted", "!=", true),
        // orderBy("added_at", "desc")
      );
      const usersSnap = await getDocs(_userQuery);
      const users = usersSnap.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      let users_mp: any = {};
      users.map((current: any) => {
        users_mp[current.id] = current;
      });

      for (const userId of paginatedUserIds) {
        // const userSnap = await getDoc(doc(db, "users", userId));
        // if (!userSnap.exists()) continue;
        // const userData = userSnap.data();

        results.push({
          user_id: userId,
          user_name: users_mp[userId].profile_name,
          user_location: users_mp[userId].location,
          user_profile_pic: users_mp[userId].avatarSmall,
          is_followed_by_me: !!loggedInUserId && bookmarks.includes(userId),
          posts: grouped[userId]?.posts || [],
        });
      }

      return {
        results,
        startAfterUserId: results?.[results?.length - 1]?.user_id,
        more: results?.length === pageSize,
      };
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // post id fetch
    // user_id

    let categories = ["Storytelling", "Literature"].includes(category)
      ? ["Storytelling", "Literature"]
      : [category];

    // Step 1: Load all posts by category, ordered by added_at
    const postsQuery = query(
      collection(db, "posts"),
      where("category", "in", categories),
      where("user_id", "!=", exclude_user_id ?? ""),
      // where("user_id","!=",//)
      // where("deleted", "!=", true),
      orderBy("added_at", "desc") // or "asc" depending on need
    );

    const postsSnap = await getDocs(postsQuery);
    const allPosts = postsSnap.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Step 2: Group posts by user_id
    const userPostMap = new Map<string, any[]>();

    for (const post of allPosts) {
      //@ts-ignore
      const userId = post.user_id;
      if (!userPostMap.has(userId)) {
        userPostMap.set(userId, []);
      }
      userPostMap.get(userId)!.push(post);
    }

    // Step 3: Get all unique user_ids
    let allUserIds = Array.from(userPostMap.keys());

    // Pagination: start after a specific user_id
    if (startAfterUserId) {
      const startIndex = allUserIds.indexOf(startAfterUserId);
      if (startIndex !== -1) {
        allUserIds = allUserIds.slice(startIndex + 1);
      }
    }

    // Limit to pageSize
    const paginatedUserIds = allUserIds.slice(0, pageSize);
    console.timeEnd("first");

    console.time("second");
    // console.log({paginatedUserIds});

    if (paginatedUserIds?.length === 0) {
      return {
        results: [],
        more: false,
        startAfterUserId: undefined,
      };
    }

    const _userQuery = query(
      collection(db, "users"),
      where("__name__", "in", paginatedUserIds)
      // where("user_id", "==", userId),
      // where("deleted", "!=", true),
      // orderBy("added_at", "desc")
    );
    const usersSnap = await getDocs(_userQuery);
    const users = usersSnap.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
    let users_mp: any = {};
    users.map((current: any) => {
      users_mp[current.id] = current;
    });

    for (const userId of paginatedUserIds) {
      // const userSnap = await getDoc(doc(db, "users", userId));
      // if (!userSnap.exists()) continue;
      // const userData = userSnap.data();

      results.push({
        user_id: userId,
        user_name: users_mp[userId].profile_name,
        user_location: users_mp[userId].location,
        user_profile_pic: users_mp[userId].avatarSmall,
        is_followed_by_me: !!loggedInUserId && bookmarks.includes(userId),
        posts: userPostMap.get(userId) || [],
      });
    }
    console.timeEnd("second");

    return {
      results,
      startAfterUserId: results?.[results?.length - 1]?.user_id,
      more: results?.length === pageSize,
    };
  } catch (error) {
    console.log({ error });
  }
}

// search

// my-feed
export async function getPostsByPostIdUserId({
  postId,
  userId,
  loggedInUserId,
  category,
}: {
  postId: string;
  userId: string;
  loggedInUserId?: string | undefined;
  category: string;
}) {
  try {
    const { db } = await initFirebase();

    let categories = ["Storytelling", "Literature"].includes(category)
      ? ["Storytelling", "Literature"]
      : [category];

    // Query posts
    const postsQuery = query(
      collection(db, "posts"),
      where("user_id", "==", userId),
      where("category", "in", categories),
      orderBy("added_at", "desc")
    );

    // Get posts
    const postsSnap = await getDocs(postsQuery);
    const allPosts = postsSnap.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Fetch only the target user data
    const userDoc = await getDoc(doc(db, "users", userId));
    const userData = userDoc.exists() ? userDoc.data() : null;

    // If no loggedInUserId, return early
    if (!loggedInUserId) {
      return {
        user_id: userId,
        user_name: userData?.profile_name || "",
        user_location: userData?.location || "",
        user_profile_pic: userData?.avatarSmall || "",
        is_followed_by_me: false,
        posts: allPosts || [],
      };
    }

    // Fetch logged in user data
    const loggedInUserDoc = await getDoc(doc(db, "users", loggedInUserId));
    const loggedInUserData = loggedInUserDoc.exists() ? loggedInUserDoc.data() : null;

    return {
      user_id: userId,
      user_name: userData?.profile_name || "",
      user_location: userData?.location || "",
      user_profile_pic: userData?.avatarSmall || "",
      is_followed_by_me: !!loggedInUserData?.bookmarks?.includes?.(userId),
      posts: allPosts || [],
    };
  } catch (error) {
    console.log({ error });
    return null;
  }
}

export async function getPostsByUserSorted({ userId }: { userId: string }): Promise<Post[]> {
  try {
    const { db } = await initFirebase();
    const postsRef = collection(db, "posts");
    const q = query(postsRef, where("user_id", "==", userId), orderBy("added_at", "desc"));

    const snapshot = await getDocs(q);
    const posts: Post[] = [];

    snapshot.forEach((doc) => {
      posts.push({
        id: doc.id,
        ...doc.data(),
      } as Post);
    });

    return posts;
  } catch (error) {
    return [];
  }
}
