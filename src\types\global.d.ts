// Global type declarations for the project

// Declare global variables that might be used across the project
interface Window {
  ethereum?: any;
  web3?: any;
}

// Declare global functions and objects
declare const localStorage: Storage;
declare const sessionStorage: Storage;
declare const document: Document;
declare const window: Window & typeof globalThis;
declare const navigator: Navigator;
declare const process: {
  env: {
    NODE_ENV: 'development' | 'production' | 'test';
    [key: string]: string | undefined;
  };
};

// Declare module types for files that might not have type definitions
declare module '*.svg' {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>;
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

declare module '*.webp' {
  const content: string;
  export default content;
}

// Add any other global declarations as needed
