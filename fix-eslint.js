// Script to fix ESLint issues across the entire project
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const srcDir = path.join(__dirname, 'src');
const extensions = ['.ts', '.tsx', '.js', '.jsx'];
const excludeDirs = ['node_modules', '.next', 'dist', 'build'];

// Helper function to check if a directory should be excluded
const shouldExcludeDir = (dirPath) => {
  return excludeDirs.some(excludeDir => dirPath.includes(excludeDir));
};

// Helper function to check if a file has a valid extension
const hasValidExtension = (filePath) => {
  const ext = path.extname(filePath);
  return extensions.includes(ext);
};

// Function to find all files with valid extensions
const findFiles = (dir, fileList = []) => {
  if (shouldExcludeDir(dir)) return fileList;

  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findFiles(filePath, fileList);
    } else if (hasValidExtension(filePath)) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
};

// Function to fix ESLint issues in a file
const fixEslintIssues = (filePath) => {
  try {
    console.log(`Fixing ESLint issues in: ${filePath}`);
    execSync(`npx eslint "${filePath}" --fix`, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Error fixing ESLint issues in ${filePath}:`, error.message);
    return false;
  }
};

// Main function
const main = () => {
  console.log('Starting ESLint fix process...');
  
  // Find all files
  console.log('Finding files...');
  const files = findFiles(srcDir);
  console.log(`Found ${files.length} files to process.`);
  
  // Fix ESLint issues in each file
  let successCount = 0;
  let failCount = 0;
  
  files.forEach((file, index) => {
    console.log(`Processing file ${index + 1}/${files.length}: ${file}`);
    const success = fixEslintIssues(file);
    
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  });
  
  // Print summary
  console.log('\nESLint fix process completed!');
  console.log(`Successfully fixed: ${successCount} files`);
  console.log(`Failed to fix: ${failCount} files`);
};

// Run the script
main();
