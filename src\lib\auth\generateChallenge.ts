import { fetcher } from "../../graphql/auth-fetcher";
import {
  ChallengeQuery,
  ChallengeQueryVariables,
  ChallengeDocument,
} from "../../graphql/generated";

export default async function generateChallenge(address: string , profileId:string) {

  return await fetcher<ChallengeQuery, ChallengeQueryVariables>(
    ChallengeDocument,
    {
      request: {
        signedBy: address,
        // use store here and ge the current user id
        for:profileId
      },
    }
  )();
}
