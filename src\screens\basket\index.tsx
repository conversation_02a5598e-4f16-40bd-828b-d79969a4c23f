import { useState, useEffect } from "react";
import BasketCard from "./basketCard";
import { SideSheetContainer, SideSheetHeader, SideSheetDescription } from "@/components/ui/sidebarSheet";
import { closeEvent } from "@/lib/eventEmmiter";

export function Basket({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [isSheetOpen, setSheetOpen] = useState(false);
  useEffect(() => {
    const closeChat = () => {
      setSheetOpen(false);
      onOpenChange(false);
    };

    closeEvent.on('close', closeChat);

    return () => {
      closeEvent.off('close', closeChat);
    };
  }, [onOpenChange]);
  return (
    <SideSheetContainer className="left-[22rem] max-lg:left-0" open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}>
      <SideSheetHeader className="px-8">
        {/* Header */}<div
          className="cursor-pointer text-base text-black font-normal row gap-2"
          onClick={() => onOpenChange(false)} // Close only when Back button is clicked
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">Basket</p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>
      {/* Tabs */}
      <SideSheetDescription className="overflow-y-auto chat-scroll-custom">
        <div className="flex flex-col mr-4">
          <div className="row gap-3">
            <img
              src="https://github.com/shadcn.png"
              alt=""
              className="w-12 h-12 rounded-full"
            />
            <p className="text-xl">John Doe</p>
          </div>
          <BasketCard />
        </div>
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
