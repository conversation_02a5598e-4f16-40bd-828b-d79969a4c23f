import React from "react";

interface PostCardSkeletonProps {
  count?: number;
}

const PostCardSkeleton: React.FC<PostCardSkeletonProps> = ({ count = 2 }) => {
  // We'll use fixed widths for consistency

  return (
    <div className="grid grid-cols-1 gap-[2px] max-lg:grid-cols-1">
      {Array.from({ length: count }).map((_, chunkIndex) => (
        <div key={chunkIndex} className="mb-1">
          <div className="w-full mt-0">
            <div className="w-full mb-[2px] gap-[2px]">
              <div className="w-full mt-0 mr-[2px]">
                {/* First row: 4-2 grid */}
                <div className="grid grid-cols-6 gap-[2px] mb-[2px] mr-[2px]">
                  {/* Large image (4 columns) */}
                  <div className="col-span-4 max-h-[116px] min-h-[116px]">
                    <div
                      className="animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 h-full w-full min-w-full border-l-4 border-t border-r border-b max-h-[116px] min-h-[116px] rounded-sm border-gray-300"
                      // Animation is now handled by the global CSS
                    ></div>
                  </div>
                  {/* Small image (2 columns) */}
                  <div className="w-full min-w-full col-span-2">
                    <div
                      className="animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 h-full w-full min-w-full border-l-4 border-t border-r border-b max-h-[116px] min-h-[116px] rounded-sm border-gray-300"
                      // Animation is now handled by the global CSS
                    ></div>
                  </div>
                </div>

                {/* Second row: 2-4 grid */}
                <div className="grid grid-cols-6 gap-[2px] mr-[2px] min-h-[250px]">
                  {/* Two small images stacked (2 columns) */}
                  <div className="col-span-2 h-full">
                    <div className="w-full min-w-full h-1/2 min-h-1/2 mb-[2px] aspect-square">
                      <div
                        className="animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 h-full w-full min-w-full border-l-4 border-t border-r border-b min-h-[124px] aspect-square rounded-sm border-gray-300"
                        // Animation is now handled by the global CSS
                      ></div>
                    </div>
                    <div className="w-full min-w-full h-1/2 min-h-1/2 aspect-square">
                      <div
                        className="animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 h-full w-full min-w-full border-l-4 border-t border-r border-b min-h-[124px] aspect-square rounded-sm border-gray-300"
                        // Animation is now handled by the global CSS
                      ></div>
                    </div>
                  </div>

                  {/* Large image (4 columns) */}
                  <div className="col-span-4 w-full min-w-full h-full min-h-full">
                    <div
                      className="animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 h-full w-full min-w-full border-l-4 border-t border-r border-b min-h-[250px] aspect-square rounded-sm border-gray-300"
                      // Animation is now handled by the global CSS
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}

      {/* Shimmer animation is now defined globally in globals.css */}
    </div>
  );
};

export default PostCardSkeleton;
