import useAuth from "@/hook";
import useEvent from "@/hook/events";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { useState, useEffect } from "react";
import EditEvents from "../editEvents";
import EventCardSkeleton from "@/components/CardSkeleton/EventCardSkeleton";
import EmptyState from "@/components/EmptyState";

const EventsCard = ({
  activeColor,
  otherUserID,
  selectedDate,
  setIstoggleCalenderEvent,
  setParentIsOpen,
}: any) => {
  const auth = useAuth();
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [localLoading, setLocalLoading] = useState(true);
  const { eventData, loading, refetch } = useEvent(
    otherUserID === "my-profile" ? auth.userId : otherUserID,
    refreshTrigger
  );

  const [isOpen, setIsOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState(null);
  const [eventDataProps, setEventDataProps] = useState(null);

  // Force initial loading state and ensure it persists
  useEffect(() => {
    // Always start with loading state true
    setLocalLoading(true);

    let timer: NodeJS.Timeout;

    // Only transition to non-loading state when data is ready and loading is false
    if (!loading && eventData !== null) {
      timer = setTimeout(() => {
        setLocalLoading(false);
      }, 1000); // Longer delay to ensure skeleton is visible
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [loading, eventData]);

  const handleSelectService = (id: any) => {
    // console.log(id);

    setSelectedServiceId(id);
    setIsOpen(true);
    setIstoggleCalenderEvent(false);
  };

  const handleSelectServiceDetail = (refreshFlag: any) => {
    setSelectedServiceId(null);
    setIsOpen(false);
    setIstoggleCalenderEvent(true);

    // If refresh flag is passed, trigger a refresh of the event data
    if (refreshFlag === "refresh") {
      // Increment the refresh trigger to force a re-fetch
      setRefreshTrigger((prev) => prev + 1);

      // Also call refetch directly if available
      if (refetch) {
        refetch();
      }
    }
  };

  return (
    <>
      <div className="overflow-y-scroll hide-scroll-custom bg-white h-[calc(100vh-280px)] max-md:h-[calc(100vh-107px)]">
        {localLoading ? (
          <div className="p-4">
            <EventCardSkeleton count={4} columns={1} showGrid={true} />
          </div>
        ) : (
          <div>
            {isOpen ? (
              <EditEvents
                eventId={selectedServiceId}
                onSelectServiceDetails={handleSelectServiceDetail}
                otherUserID={otherUserID}
                eventDataProps={eventDataProps}
              />
            ) : (
              <div className="grid grid-cols-1 max-md:grid-cols-1 gap-2">
                {eventData && eventData.length > 0 ? (
                  (() => {
                    const filteredEvents = eventData.filter((service: any) => {
                      const serviceDate = new Date(service.date);
                      return (
                        serviceDate.getMonth() === selectedDate.getMonth() &&
                        serviceDate.getFullYear() === selectedDate.getFullYear()
                      );
                    });

                    return filteredEvents.length > 0 ? (
                      filteredEvents.map((service: any, index: number) => (
                        <div
                          className="cursor-pointer w-full"
                          key={index}
                          onClick={() => {
                            handleSelectService(service.id);
                            setEventDataProps(service);
                          }}
                        >
                          <GlobalCardEvents 
                            post={service} 
                            border={activeColor} 
                            isDeleteIcon={otherUserID === "my-profile" ? true : false}
                            onDelete={() => {
                              // Refresh the events list
                              if (handleSelectServiceDetail) {
                                handleSelectServiceDetail("refresh");
                              }
                            }}
                          />
                        </div>
                      ))
                    ) : (
                      <div className="col-span-2 w-full">
                        <EmptyState
                          type="events"
                          title={`No Events in ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`}
                          message={`There are no events scheduled for ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`}
                          isOwnProfile={otherUserID === "my-profile"}
                          actionLabel={otherUserID === "my-profile" ? "Create Event" : undefined}
                          onAction={
                            otherUserID === "my-profile"
                              ? () => {
                                  if (setParentIsOpen) {
                                    setParentIsOpen(true);
                                  } else {
                                    handleSelectService("new");
                                  }
                                }
                              : undefined
                          }
                        />
                      </div>
                    );
                  })()
                ) : (
                  <div className="col-span-2 w-full">
                    <EmptyState
                      type="events"
                      title="No Events Yet"
                      message={
                        otherUserID === "my-profile"
                          ? `You haven't created any events yet. Create your first event to start scheduling in ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`
                          : `This user hasn't created any events yet for ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`
                      }
                      isOwnProfile={otherUserID === "my-profile"}
                      actionLabel={otherUserID === "my-profile" ? "Create Event" : undefined}
                      onAction={
                        otherUserID === "my-profile"
                          ? () => {
                              if (setParentIsOpen) {
                                setParentIsOpen(true);
                              } else {
                                handleSelectService("new");
                              }
                            }
                          : undefined
                      }
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default EventsCard;
