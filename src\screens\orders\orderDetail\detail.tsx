import { Info } from "react-feather";

const Detail = () => {
  return (
    <>
      <div className="overflow-y-scroll hide-scroll h-screen pb-72">
        <div>
          <p className="text-primary font-bold mb-3">Order delivery details</p>
          <p className="text-subtitle">
            Message me in Whats App for delivery address and method.
            +442987187293
          </p>
        </div>
        <div>
          <p className="text-primary font-bold uppercase mb-3">
            LIVE MUSIC PERFORMANCE
          </p>
          <div className="row justify-between mb-2">
            <p>1 day</p>
            <p> $90.00</p>
          </div>
          <p className="text-subtitle">
            Sed posuere consectetur jmest at dfd lobortis. Donec ul mullamcorper
            nulla non posuere consec tetur metus auctor.
          </p>
        </div>
        <div>
          <p className="text-primary font-bold mb-3">Client’s comment</p>
          <p className="text-subtitle">
            I would like this illustration to be special. My wife is celebrating
            her 50th anniversary. Please be respectful in terms of due date.
          </p>
        </div>
        <div>
          <p className="text-primary font-bold mb-3">Customization</p>
          {/* <p className="text-subtitle">
          I would like this illustration to be special. My wife is celebrating
          her 50th anniversary. Please be respectful in terms of due date.
        </p> */}
        </div>

        <div className=" border-b-2 border-black pb-4">
          <p className="my-3 text-primary text-xl font-bold">Customization</p>

          <div className="grid grid-cols-2">
            <div>
              <p className="text-primary font-bold">Option</p>
            </div>
            <div className="justify-end row">
              <div className="row gap-4">
                <p className="text-primary font-bold w-12 text-center">Time</p>
                <p className="text-primary font-bold  w-16 text-center">Cost</p>
                <p className=" opacity-0  w-6 text-center">hi</p>
              </div>
            </div>
          </div>
          {Array.from({ length: 1 }).map((_, indexs) => (
            <div className="grid grid-cols-2 mt-2">
              <div>
                <p className="text-subtitle">Create a track for 5 minutes.</p>
              </div>
              <div className=" justify-end row">
                <div className="row gap-4">
                  <p className="text-subtitle w-12 text-center ">1d</p>
                  <p className="text-subtitle  w-16 text-center">$10.00</p>
                  <Info
                    className="text-borderColor  w-6 text-center"
                    size={21}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-0">
          <div className="row justify-between mt-3">
            <p className="text-subtitle1">Subtotal</p>
            <p className="text-primary font-bold">$100.00</p>
          </div>
          <div className="row justify-between mt-3">
            <p className="text-subtitle1">Transaction fee (4%)</p>
            <p className="text-primary font-bold">$4.00</p>
          </div>
          <div className="row justify-between mt-3">
            <p className="text-subtitle1">Order total</p>
            <p className="text-primary font-bold">$104.00 </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Detail;
