import { GlobalCard } from "@/globalComponents/globalCard";
import { themes } from "../../../../../../../theme";
import useProfile from "@/hook/profileData";
import { useEffect, useState } from "react";
import { getServicesByUserId } from "@/services/serviceService";
import useAuth from "@/hook";
import { initializeCurrency } from "@/services/currencyService";
import CardSkeleton from "@/components/CardSkeleton";
import EmptyState from "@/components/EmptyState";

const OtherServicesCard = ({ onSelectService, otherUserID, isOtherProfile }: any) => {
  // State to store fetched services
  const [services, setServices]: any = useState([]);
  const [loading, setLoading] = useState(true);
  const [currencySymbol, setCurrencySymbol] = useState("£"); // Default to GBP symbol

  useEffect(() => {
    const fetchAllServices = async () => {
      setLoading(true);
      if (otherUserID) {
        const responsedan = await getServicesByUserId(otherUserID);
        setServices(responsedan.services || []);
        // console.log({ responsedan });
      }
      setLoading(false);
    };

    fetchAllServices();
  }, [otherUserID]);

  return (
    <>
      <div className="w-full">
        <div className="w-full bg-white sticky top-[7.2rem] max-md:top-[5.6rem] z-50">
          <div className="row justify-between py-2">
            <p className="text-primary text-xl font-bold max-md:text-base">My Services </p>
            <p className="text-primary text-xl font-bold px-2 max-md:text-base">{currencySymbol}</p>
          </div>
        </div>
        <div className="bg-white pt-2 w-full">
          {loading ? (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              <CardSkeleton count={3} columns={1} showGrid={true} />
            </div>
          ) : (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              {services.length > 0 ? (
                services.map((service: any, index: any) => (
                  <div
                    className="cursor-pointer"
                    key={index}
                    onClick={() => onSelectService(service.id)}
                  >
                    {Object.entries(themes).map(([_, innerThemeProperties]) => (
                      <div key={innerThemeProperties.title}>
                        {(service.category === "Storytelling" ? "Literature" : service.category) ===
                          innerThemeProperties.title && (
                          <GlobalCard
                            border={innerThemeProperties.backgroundColor}
                            title={service.title}
                            description={service.description}
                            price={service.price}
                            duration={service?.duration}
                            currency={service?.currency || initializeCurrency()}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                ))
              ) : (
                <EmptyState
                  type="services"
                  title="No Services Yet"
                  message={
                    isOtherProfile
                      ? "This user hasn't created any services yet"
                      : "Create your first service to start offering your skills"
                  }
                  isOwnProfile={!isOtherProfile}
                  actionLabel={!isOtherProfile ? "Create Service" : undefined}
                  onAction={!isOtherProfile ? () => onSelectService("new") : undefined}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default OtherServicesCard;
