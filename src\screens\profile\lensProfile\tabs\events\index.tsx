"use client";
import { <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Server } from "react-feather";
import CalendarComp from "./calender";
import * as Tabs from "@radix-ui/react-tabs";
import EventsCard from "./eventsCard";
import CalendarCompEvent from "./calenderEvents";
import { useState } from "react";

type ProfileMyProfileProps = {
  activeBorderColor?: string; // New prop for dynamic active border color
  otherUserID: any;
};
export function EventsMyProfile({
  activeBorderColor = "#333333",
  otherUserID,
}: ProfileMyProfileProps) {
  const [selectedDate, setSelectedDate] = useState(new Date());

  const [istoggle, setIstoggle] = useState(false);
  const [istoggleCalenderEvent, setIstoggleCalenderEvent] = useState(true);
  return (
    <div className="mt-3">
      <Tabs.Root
        defaultValue="Calendar"
        value={istoggle ? "Server" : "Calendar"}
        className="w-full"
      >
        <Tabs.List
          className="TabsList pb-0 max-md:pb-1 sticky top-10 bg-white -mt-5 scroll-pt-2 max-md:-mt-7"
          aria-label="Manage your account"
          style={
            {
              "--active-border-color": activeBorderColor,
            } as React.CSSProperties
          } // Set CSS variable
        >
          <Tabs.Trigger
            className="TabsTrigger min-h-[70px] max-md:min-h-[50px]"
            value="Calendar"
            onClick={() => setIstoggle(false)}
          >
            <Calendar />
          </Tabs.Trigger>
          <Tabs.Trigger
            className="TabsTrigger min-h-[70px] max-md:min-h-[50px]"
            value="Server"
            onClick={() => setIstoggle(true)}
          >
            <Server />
          </Tabs.Trigger>
        </Tabs.List>
        <TabsContent value="Calendar">
          <div className="mt-3">
            <CalendarComp
              activeBorderColor={activeBorderColor}
              otherUserID={otherUserID}
              activeDate={selectedDate}
              setActiveDate={setSelectedDate}
              setIstoggle={setIstoggle}
            />
          </div>
        </TabsContent>
        <TabsContent value="Server">
          <div className="w-full overflow-y-auto hide-scroll  bg-white h-full mt-3">
            {istoggleCalenderEvent && (
              <CalendarCompEvent activeDate={selectedDate} setActiveDate={setSelectedDate} />
            )}

            <EventsCard
              activeColor={activeBorderColor}
              otherUserID={otherUserID}
              selectedDate={selectedDate}
              setIstoggleCalenderEvent={setIstoggleCalenderEvent}
            />
          </div>
        </TabsContent>
      </Tabs.Root>
    </div>
  );
}
