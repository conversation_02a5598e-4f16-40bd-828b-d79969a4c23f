import { useAccountQuery } from "@/graphql/test/generated";
import React from "react";

type Props = {
  handle: string;
  profileId: string;
};

const Profile = ({ handle, profileId }: Props) => {
  const { data, isLoading } = useAccountQuery(
    {
      request: {
        address: handle,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  return (
    <>
      <div>Profile</div>
      <div className="flex flex-col">
        <div>{data?.account?.username?.namespace}</div>
        <div>{data?.account?.username?.localName}</div>
        <div>{data?.account?.metadata?.bio}</div>
      </div>
    </>
  );
};

export default Profile;
