"use client";
import { Badge } from "@/components/ui/badge";
import { useEffect, useState } from "react";
import { ChevronLeft, Info, Check, Loader } from "react-feather";
import EditService from "./editService";
import { deleteService, formatDuration, getServiceById } from "@/services/serviceService";
import { getCurrencySymbol, initializeCurrency } from "@/services/currencyService";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import { Button } from "@/components/ui/button";
import RichTextFormatter from "@/components/RichTextFormatter";

const ServiceDetails = ({ id, onSelectServiceDetails, categories }: any) => {
  const [isTrue, setIsTrue] = useState(false);
  const [services, setServices]: any = useState();
  const [currencySymbol, setCurrencySymbol] = useState("£"); // Default to GBP symbol
  const [isConfirm, setIsConfirm] = useState(false);
  const [isToggle, setIsToggle] = useState(false);
  const [isLoadingCustomizations, setIsLoadingCustomizations] = useState(false);
  const [isLoadingService, setIsLoadingService] = useState(true); // Add loading state for service data
  const [loading, setLoading] = useState(false);

  const handleSelectService = () => {
    setIsTrue(false);
    // Reset loading states immediately to avoid showing loaders unnecessarily
    setIsLoadingCustomizations(false);
    setIsLoadingService(false);
    // Refresh service details when returning from edit mode
    setTimeout(() => {
      fetchAllServices();
    }, 100); // Small delay to ensure UI updates first
  };

  const fetchAllServices = async () => {
    // Set loading states
    if (!isTrue) {
      setIsLoadingCustomizations(true);
      setIsLoadingService(true);
    }

    try {
      const response = await getServiceById(id);

      if (response.success) {
        setServices(response.service);

        // Get the currency symbol from the service data or default
        const servicesCurrency = initializeCurrency();
        setCurrencySymbol(getCurrencySymbol(servicesCurrency));
      }
    } catch (error) {
      console.error("Error fetching service:", error);
    } finally {
      // Only update loading states if we're not in edit mode
      if (!isTrue) {
        setIsLoadingCustomizations(false);
        setIsLoadingService(false);
      }
    }
  };

  useEffect(() => {
    fetchAllServices();
  }, [id]);

  const handleDelete = async () => {
    setLoading(true);
    try {
      const response = await deleteService(id);

      if (response.success) {
        // alert("Service deleted successfully!");
        setIsToggle(true);
      } else {
        alert("Failed to delete post: " + response.error);
      }
    } catch (error) {
      alert("Something went wrong.");
      console.error("Delete error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div
        className={
          isTrue
            ? "grid grid-cols-2 max-md:grid-cols-1 bg-white gap-4 w-full"
            : "grid grid-cols-1 bg-white w-full"
        }
      >
        <div className={isTrue ? "max-md:hidden" : ""}>
          {/* header */}
          <div className=" w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div
                onClick={() => !isTrue && onSelectServiceDetails()}
                className=" cursor-pointer row"
                aria-disabled={true}
              >
                <ChevronLeft />
                <p className="pl-[2px]">Back</p>
              </div>
              <p className="text-titleLabel  font-bold">Service Details</p>
              <p className=" opacity-0">save</p>
            </div>
          </div>
          <div
            className={
              isTrue
                ? "flex flex-row w-full overflow-y-scroll gap-3 hide-scroll-custom bg-white h-[calc(100vh-300px)] pt-4 pb-16"
                : "flex flex-row w-full overflow-scroll gap-3 hide-scroll bg-white h-full pt-4"
            }
          >
            <div className="w-full">
              {isLoadingService ? (
                <div className="flex flex-col items-center justify-center h-[60vh]">
                  <div className="bg-blue-50 rounded-full p-4 mb-4">
                    <Loader size={48} className="text-primary animate-spin" />
                  </div>
                  <h2 className="text-xl font-bold mb-2">Loading Service Details...</h2>
                  <p className="text-gray-500 text-center">
                    Please wait while we load your service information.
                  </p>
                </div>
              ) : (
                <>
                  <p className="text-subtitle">Сategory</p>
                  <p className="text-primary font-[600]">{services?.category}</p>
                  <p className="my-3 text-primary text-lg font-bold max-md:text-base">
                    {services?.title}
                  </p>
                  <div className="row justify-between">
                    <p className="text-titleLabel">
                      {formatDuration(services?.duration, {
                        dayLabel: "day",
                        hourLabel: "hour",
                        handlePlural: true,
                      })}
                    </p>
                    <p className="text-titleLabel">
                      {currencySymbol}{" "}
                      {services?.price ? (services?.price / (1 - 0.16)).toFixed(2) : "0.00"}
                    </p>
                  </div>
                  <div className="mt-3">
                    <RichTextFormatter
                      text={services?.description || ""}
                      className="text-subtitle break-words"
                      preserveWhitespace={true}
                      enableMarkdown={true}
                    />
                  </div>
                </>
              )}
              {!isLoadingService && (
                <>
                  {services?.customizations_array.length !== 0 && (
                    <>
                      <p className="my-3 text-primary text-lg font-semibold text-center">
                        Customization
                      </p>
                      <div className="grid grid-cols-2">
                        <div>
                          <p className="text-primary font-semibold">Option</p>
                        </div>
                        <div className="justify-end row">
                          <div className="row gap-4">
                            <p className="text-primary font-semibold w-16 text-center">Time</p>
                            <p className="text-primary font-semibold w-16 text-center">Cost</p>
                            <p className="opacity-0 w-14 text-center">hi</p>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                  {isLoadingCustomizations ? (
                    <div className="flex flex-col justify-center items-center py-8">
                      <div className="bg-blue-50 rounded-full p-3 mb-2">
                        <Loader size={24} className="text-primary animate-spin" />
                      </div>
                      <p className="text-gray-500 text-sm">Loading customizations...</p>
                    </div>
                  ) : services?.customizations_array &&
                    services?.customizations_array.length > 0 ? (
                    <div>
                      {services?.customizations_array.map((item: any, indexs: any) => (
                        <div className="grid grid-cols-2 mt-2" key={indexs}>
                          <div>
                            <p className="text-subtitle text-sm mr-10">{item?.title}</p>
                          </div>
                          <div className="justify-end row">
                            <div className="row gap-4">
                              <p className="text-subtitle w-12 text-left text-sm">
                                +
                                {formatDuration(item?.duration, {
                                  dayLabel: "d ",
                                  hourLabel: "h",
                                })}
                              </p>
                              <p className="text-subtitle w-16 text-right text-sm">
                                +{currencySymbol}
                                {item?.price ? (item?.price / (1 - 0.16)).toFixed(2) : "0.00"}
                              </p>
                              <Info className="text-borderColor w-14 text-center" size={21} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No Customization available.</p>
                  )}
                </>
              )}

              {/* Only show Edit/Delete buttons when not in edit mode and service is loaded */}
              {!isTrue && !isLoadingService && (
                <>
                  <div className="row justify-center mt-5">
                    <Badge
                      className=" btn-xs w-60 py-4 border-primary btn"
                      variant="outline"
                      onClick={() => {
                        // Don't set loading state here, as it affects the customization loader
                        setIsTrue(true);
                      }}
                    >
                      Edit
                    </Badge>
                  </div>
                  <div className="row justify-center">
                    <Badge
                      className=" btn-xs w-60 py-4 border-primary btn mt-3"
                      variant="outline"
                      onClick={() => setIsConfirm(true)}
                    >
                      Delete
                    </Badge>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Edit Service */}
        {isTrue && (
          <EditService
            onSelectService={handleSelectService}
            id={id}
            categories={categories}
            currencySymbol={currencySymbol}
          />
        )}
      </div>

      <div>
        <Modal
          isDismissable={false}
          isOpen={isConfirm}
          placement="auto"
          onOpenChange={setIsConfirm}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {() => (
              <>
                <ModalBody>
                  {isToggle ? (
                    <div className="flex flex-col items-center justify-between min-h-[200px] py-4">
                      <div className="flex flex-col items-center">
                        <div className="bg-green-100 p-3 rounded-full mb-4">
                          <Check size={32} className="text-green-600" />
                        </div>
                        <p className="text-center text-black text-lg font-medium">
                          Service deleted successfully
                        </p>
                      </div>
                      <div className="w-full mt-auto">
                        <Button
                          variant="outline"
                          className="rounded-full w-full border-primary text-primary border-2 py-5 text-base font-medium transition-all hover:bg-primary hover:text-white"
                          onClick={() => {
                            setIsConfirm(false);
                            onSelectServiceDetails();
                          }}
                        >
                          Close
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="text-center text-black text-lg">
                        Are you sure you want to Delete this service?
                      </p>
                      <div>
                        <Button
                          variant="outline"
                          className="rounded-full w-full border-primary text-primary border-2 py-5 text-base font-medium transition-all hover:bg-primary hover:text-white mt-6"
                          onClick={handleDelete}
                          disabled={loading}
                        >
                          {loading ? (
                            <span className="flex items-center justify-center gap-2">
                              <Loader size={30} className="text-primary animate-spin" />
                              deleting...
                            </span>
                          ) : (
                            "Yes, continue"
                          )}
                        </Button>

                        <Button
                          variant="outline"
                          className="rounded-full w-full border-primary text-primary border-2 py-5 text-base font-medium transition-all hover:bg-primary hover:text-white mt-3"
                          onClick={() => setIsConfirm(false)}
                        >
                          No, cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ServiceDetails;
