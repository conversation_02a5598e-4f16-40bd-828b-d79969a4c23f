"use client";
import { useAccount } from "wagmi";
import { useEffect, useState } from "react";
import WebHome from "./WebHome";
import MobileHome from "./MobileHome";

type ProfileMyProfileProps = {
  activeBgColor?: string; // New prop for dynamic active border color
};
export function Home({ activeBgColor = "#454545" }: ProfileMyProfileProps) {
  const { address, isConnected } = useAccount();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if wallet is connected
    if (!address && !isConnected) {
      try {
        // Clear wagmi.store from localStorage if wallet is not connected
        const wagmiStore = localStorage.getItem("lens-user");
        if (wagmiStore) {
          const parsedStore = JSON.parse(wagmiStore);
          // If there's connection data but we're not actually connected, clear it
          if (!parsedStore?.address) {
            localStorage.removeItem("lens-user");
          }
        }
      } catch (error) {
        console.error("Error checking wallet connection:", error);
      }
    }
  }, [address, isConnected]);

  // Detect mobile screen size
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is the medium breakpoint in Tailwind
    };

    // Check initially
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Clean up
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  return (
    <div className="px-2 max-md:px-0">
      {/* Conditionally render either WebHome or MobileHome based on screen size */}
      {isMobile ? <MobileHome /> : <WebHome activeBgColor={activeBgColor} />}
    </div>
  );
}

export default Home;
