import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeAccountRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { accountId, isUS }: StripeAccountRequest = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const loginLink = await stripeService.accounts.createLoginLink(accountId);

    res.status(200).json({
      loginLink,
      success: true,
    });

  } catch (error) {
    console.error('Error creating login link:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
