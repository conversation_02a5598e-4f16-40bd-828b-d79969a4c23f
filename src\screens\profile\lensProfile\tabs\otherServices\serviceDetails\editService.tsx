"use client";
import { X } from "react-feather";

const EditService = ({ onSelectService }: any) => {
  return (
    <>
      <div>
        <div className="bg-green-700">
          {/* header */}
          <div className=" w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div
                onClick={() => onSelectService(1)}
                className=" cursor-pointer"
              >
                <X />
              </div>
              <p className="text-titleLabel text-lg font-bold">
                Service Details
              </p>
              <p className=" opacity-0">Save</p>
            </div>
          </div>
          <div className="flex flex-row w-full overflow-scroll gap-3 hide-scroll-custom  bg-white h-[calc(100vh-300px)] pt-4 pb-16 px-3">
            <div>
              <p className="text-primary font-[600]">
                Choose Create a track for 5 minutes
              </p>
              <p className="mt-2 text-subtitle">
                Add a unique track to your video! I will create a track in the
                deephouse style for you. Length 3 minutes in FL Studio or
                Ableton.
              </p>
              <div className="mt-3">
                <div className="grid grid-cols-2 gap-1">
                  <img
                    src="/assets/img3.svg"
                    alt=""
                    className="h-full w-full object-cover  "
                    style={
                      {
                        // borderColor: themeProperties.backgroundColor,
                      }
                    }
                  />
                  <img
                    src="/assets/img2.svg"
                    alt=""
                    className="h-full w-full object-cover "
                    style={
                      {
                        // borderColor: themeProperties.backgroundColor,
                      }
                    }
                  />
                </div>
                <img
                  src="/assets/img1.svg"
                  alt=""
                  className="h-full w-full object-cover mt-2"
                  style={
                    {
                      // borderColor: themeProperties.backgroundColor,
                    }
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EditService;
