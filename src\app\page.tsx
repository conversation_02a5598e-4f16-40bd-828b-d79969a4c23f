"use client";
import Home from "@/screens/home";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Chain } from "@thirdweb-dev/chains";
import { createConfig, WagmiProvider } from "wagmi";
import { walletConnect, injected } from "wagmi/connectors";
import getRpc, { CHAIN, NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID } from "@/lib/constant";

export default function HomePage() {
  const queryClient = new QueryClient();
  // const desiredChainId = ChainId.Polygon;
  const connectors = [
    injected(),
    walletConnect({ projectId: NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID }),
  ];

  const config = createConfig({
    chains: [CHAIN],
    transports: {
      [CHAIN.id]: getRpc({ mainnet: true }),
    },
    connectors,
  });

  const lensChain: Chain = {
    name: "Lens Chain",
    chain: "Lens",
    rpc: ["https://rpc.lens.xyz"],
    nativeCurrency: {
      name: "<PERSON>H<PERSON>",
      symbol: "GHO",
      decimals: 18,
    },
    shortName: "lens",
    chainId: 232,
    networkId: 232,
    explorers: [
      {
        name: "Lens Explorer",
        url: "https://explorer.lens.xyz",
        standard: "EIP3091",
      },
    ],
    testnet: false,
    slug: "lens",
  };
  const desiredChainId = lensChain;

  return (
    <>
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <div
            className=""
            style={{
              transition: "all 0.3s ease",
            }}
          >
            {/* <ConnectKitProvider> */}
            <Home />
            {/* </ConnectKitProvider> */}
          </div>
        </QueryClientProvider>
      </WagmiProvider>
    </>
  );
}
