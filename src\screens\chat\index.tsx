import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { ChatBox } from "./chatBox";
import {
  SideSheetContainer,
  SideSheetHeader,
  SideSheetDescription,
} from "@/components/ui/sidebarSheet";
import { closeEvent } from "@/lib/eventEmmiter";

export function Chat({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [isSheetOpen, setSheetOpen] = useState(false);
  const [selectedChatId, setSelectedChatId] = useState<number | null>(null); // To store the selected user ID
  const [selectedChatName, setSelectedChatName] = useState<string | null>(null);
  const [selectedChatImg, setSelectedChatImg] = useState<string | null>(null);

  useEffect(() => {
    const closeChat = () => {
      setSheetOpen(false);
      onOpenChange(false);
    };

    closeEvent.on("close", closeChat);

    return () => {
      closeEvent.off("close", closeChat);
    };
  }, [onOpenChange]);

  const handleUserClick = (id: any, userName: string, imgUrl: string) => {
    setSelectedChatId(id);
    setSheetOpen(true); // Open the chat box when an array element is clicked
    setSelectedChatName(userName);
    setSelectedChatImg(imgUrl);
  };

  const messages = [
    {
      imgUrl: "/assets/chat-img/Ellipse 1.png",
      colorVal: "transparent",
      userName: "Balk",
      text: "How are you?",
      time: "16:54",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 2.png",
      colorVal: "transparent",
      userName: "Veeti Seppanen",
      text: "Hey ! I would like to order you filmmaking audio description along with",
      time: "08:17",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 3.png",
      colorVal: "transparent",
      userName: "Michael Feher",
      text: "The customization looks great. Thanks!",
      time: "Aug 17",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 4.jpeg",
      colorVal: "#E5B045",
      userName: "A. H.",
      text: "How are you?",
      time: "Aug 14",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 5.png",
      colorVal: "transparent",
      userName: "Mercy",
      text: "Really nice work. My cousin wants same service with other items as well",
      time: "Aug 05",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 6.png",
      colorVal: "#E5B045",
      userName: "Bateman",
      text: "Hey! Can you recommend me a new program for creating music through",
      time: "Jul 24",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 7.png",
      colorVal: "transparent",
      userName: "Saleh Almatrafi",
      text: "If you wish to have a set of video tutorials to curve the cue balance",
      time: "Jul 17",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 5.png",
      colorVal: "transparent",
      userName: "Mercy",
      text: "Really nice work. My cousin wants same service with other genre",
      time: "Aug 05",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 6.png",
      colorVal: "#E5B045",
      userName: "Bateman",
      text: "Hey! Can you recommend me a new program for creating music through",
      time: "Jul 24",
    },
    {
      imgUrl: "/assets/chat-img/Ellipse 7.png",
      colorVal: "transparent",
      userName: "Saleh Almatrafi",
      text: "If you wish to have a set of video tutorials to curve the cue balance",
      time: "Jul 17",
    },
  ];

  return (
    <SideSheetContainer
      className="left-[22rem] max-lg:left-0"
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <SideSheetHeader className="px-8">
        <div
          className="cursor-pointer text-base font-medium text-black row gap-2"
          onClick={() => onOpenChange(false)}
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">Chats</p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>
      <SideSheetDescription className="overflow-y-auto chat-scroll-custom">
        {" "}
        {isSheetOpen && (
          <ChatBox
            open={isSheetOpen} // Use isSheetOpen to control the ChatBox visibility
            onOpenChange={setSheetOpen}
            chatId={selectedChatId}
            chatName={selectedChatName}
            chatImg={selectedChatImg}
          />
        )}
        <div className="mr-2">
          {messages.map((msg, indexs) => (
            <div key={indexs}>
              <div
                className={
                  selectedChatId === indexs
                    ? "row justify-between px-4 py-3 items-start border-b-[1px] cursor-pointer bg-[#F2F2F2]"
                    : "row justify-between px-4 py-3 items-start border-b-[1px] cursor-pointer"
                }
                onClick={() =>
                  handleUserClick(indexs, msg.userName, msg.imgUrl)
                }
              >
                <div className="flex flex-row gap-3">
                  <div>
                    <img
                      src={msg.imgUrl}
                      alt=""
                      className="w-[60px] h-[60px] min-w-[60px] min-h-[60px] rounded-full object-cover"
                      style={{
                        border: "3px solid " + msg.colorVal,
                      }}
                    />
                  </div>
                  <div className="text-left w-[13.75rem]">
                    <p className="font-bold text-primary text-[16px] leading-5">
                      {msg.userName}
                    </p>
                    <p className="text-primary text-[14px] leading-5 line-clamp-2 break-words">
                      {msg.text}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-y-[14px]">
                  <p className="text-[#B0B0B0] text-right w-[3.1rem] text-[14px] leading-5">
                    {msg.time}
                  </p>
                  <Badge className="rounded-full py-[2px] px-[8px] text-center  text-[14px] font-semibold text-white">
                    2
                  </Badge>
                </div>
              </div>
            </div>
          ))}
        </div>
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
