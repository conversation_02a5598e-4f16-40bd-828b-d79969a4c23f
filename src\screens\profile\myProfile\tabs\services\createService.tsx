"use client";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import useAuth from "@/hook";
import {
  createService,
  formatDuration,
  listPricecal,
  Service,
} from "@/services/serviceService";
import { updateUser } from "@/services/usersServices";
import { FormEvent, useEffect, useState } from "react";
import { arrayUnion, Timestamp } from "firebase/firestore";
import { Check, Info, Loader, Plus, X } from "react-feather";
import { Trash, Upload } from "lucide-react";
import { Button } from "@heroui/react";
import {
  getCurrencySymbol,
  initializeCurrency,
} from "@/services/currencyService";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { Textarea } from "@/components/ui/textarea";

const CreateService = (props: any) => {
  const auth = useAuth();
  const [title, setTitle]: any = useState<string | null>(null);
  const [category, setCategory]: any = useState<string | null>(null);
  const [about, setAbout] = useState("");
  const [serviceCost, setServiceCost]: any = useState<number | null>(null);
  const [listPrice, setListPrice] = useState<number | null>(null);
  const [time, setTime]: any = useState<string | null>(null);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [geoTags, setGeoTags] = useState<string[]>([]);
  const [media, setMedia] = useState<File | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [customizationList, setCustomizationList]: any = useState([]);
  const [currencyIcon, setCurrencyIcon] = useState("");
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "success"
  >("idle");
  const [customizationsFinalized, setCustomizationsFinalized] = useState(false);

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === "success") {
      // Wait 1.5 seconds before closing the modal to show success state
      const timer = setTimeout(() => {
        props.setIsOpen(false);
        props.setToggleMain((e: boolean) => !e);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, props]);

  const handleSubmit = async () => {
    setLoading(true);
    setUploadStatus("uploading");

    const formData: Service = {
      // id: auth?.userData?.uid, //if i pass this is , while population it will create problem
      title,
      category,
      // about,
      price: serviceCost,
      // listPrice,
      duration: time,
      description: about,
      currency: initializeCurrency(),
    };

    try {
      const completedCustomizations = customizationList.filter(isCustomizationComplete);
      const response = await createService(formData, completedCustomizations);

      // Check if the post was created successfully
      if (response.success) {
        const postId = response.id; // Assuming `response.id` contains the created post's ID
        const userId = auth?.userData?.uid; // Assuming `auth.userData.uid` contains the current user's ID

        if (userId && postId) {
          await updateUser(userId, { services: arrayUnion(postId) });
          // Set success state - modal will close after delay via useEffect
          setUploadStatus("success");
        }
      } else {
        console.error("Error creating post:", response);
        setUploadStatus("idle");
        setLoading(false);
      }
    } catch (error) {
      console.error("Error creating post:", error);
      setUploadStatus("idle");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (serviceCost !== null) {
      setListPrice(parseFloat((serviceCost * 1.16).toFixed(2))); // Ensure a valid number
    } else {
      setListPrice(null);
    }
  }, [serviceCost]);

  // Add a new customization object without id
  const handleAddCustomization = () => {
    const newCustomization = {
      title: "",
      description: "", // Start with empty description for each customization
      price: "", // Start with empty price for each customization
      duration: "", // Start with empty duration for each customization
      about: "",
      media: [], // Array to store media URLs
    };

    setCustomizationList([...customizationList, newCustomization]);
  };

  // Delete a customization using the array index
  const handleDeleteCustomization = (index: number) => {
    setCustomizationList(
      customizationList.filter((_: any, i: number) => i !== index)
    );
  };

  // Update a specific customization field using index
  const handleUpdateCustomization = (index: number, field: any, value: any) => {
    setCustomizationList(
      customizationList.map((item: any, i: number) => {
        if (i === index) {
          return { ...item, [field]: value };
        }
        return item;
      })
    );
  };

  // Function to handle media upload (using index)
  const handleMediaUpload = async (index: number, files: any) => {
    if (!files || files.length === 0 || uploading || loading) return;

    setUploading(true);
    setLoading(true);

    const storage = getStorage();
    const mediaUrls: any = [];

    try {
      for (const file of files) {
        const fileType = file.type.startsWith("image/") ? "images" : "videos";
        const filePath = `services/${fileType}/${auth?.userData?.uid}/${index}/${Date.now()}_${file.name}`;
        const storageRef = ref(storage, filePath);
        await uploadBytes(storageRef, file);
        const downloadUrl = await getDownloadURL(storageRef);
        mediaUrls.push(downloadUrl);
      }
      setCustomizationList(
        customizationList.map((item: any, i: number) => {
          if (i === index) {
            return {
              ...item,
              media: [...(item.media || []), ...mediaUrls],
            };
          }
          return item;
        })
      );
    } catch (error) {
      console.error("Error uploading media:", error);
      alert("Failed to upload media. Please try again.");
    } finally {
      setUploading(false);
      setLoading(false);
    }
  };

  // Remove media from customization (using index)
  const handleRemoveMedia = (index: number, mediaUrl: any) => {
    if (uploading || loading) return;
    setCustomizationList(
      customizationList.map((item: any, i: number) => {
        if (i === index) {
          return {
            ...item,
            media: (item.media || []).filter((url: any) => url !== mediaUrl),
          };
        }
        return item;
      })
    );
  };

  // Initialize currency on component mount
  useEffect(() => {
    try {
      // Use the enhanced currency service to get symbol
      const symbol = getCurrencySymbol();
      setCurrencyIcon(symbol);
    } catch (error) {
      console.error("Error getting currency:", error);
      // Set default currency symbol if there's an error
      setCurrencyIcon("£");
    }
  }, []);

  const isCustomizationComplete = (item: any) =>
    item.title && item.price && item.duration && item.about;

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === "success") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">
            Service Created Successfully!
          </h2>
          <p className="text-gray-500">
            Your service has been created and will be visible on your profile.
          </p>
        </div>
      );
    }

    if (uploadStatus === "uploading") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Creating Your Service...</h2>
          <p className="text-gray-500">
            Please wait while we create your service. This may take a moment.
          </p>
        </div>
      );
    }

    return (
      <div className="flex flex-row max-md:flex-col">
        {!isOpen && (
          <div>
            <div className="flex flex-row justify-between items-center mb-4 sticky top-0 bg-white">
              <div
                onClick={() => !loading && props.setIsOpen(false)}
                className={
                  loading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                }
              >
                <X />
              </div>
              <h2 className="text-xl font-bold">Create Service</h2>
              <p
                onClick={() =>
                  !loading && title && category && serviceCost && time && about
                    ? handleSubmit()
                    : ""
                }
                className={
                  !loading && title && category && serviceCost && time && about
                    ? "font-bold text-primary cursor-pointer"
                    : "font-bold text-borderColor cursor-not-allowed"
                }
              >
                Save
              </p>
            </div>
            <div className=" bg-gray-50">
              <div className=" bg-white  rounded-md p-2 py-4">
                <form className="space-y-4">
                  {/* Choose Category */}
                  <div>
                    <p className="text-primary mb-1 font-[600]">
                      Choose category
                    </p>
                    <div className="flex space-x-2">
                      {props.category.map((cat: any, index: any) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => !loading && setCategory(cat)}
                          className={`px-4 py-2 rounded-md ${
                            category === cat
                              ? "bg-primary text-white"
                              : "bg-[#EEEEEE]  text-primary"
                          } ${
                            loading
                              ? "opacity-50 cursor-not-allowed"
                              : "cursor-pointer"
                          }`}
                          disabled={loading}
                        >
                          {cat}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* About Project */}
                  <div className="grid w-full md:max-full items-center gap-1.5 mt-2 max-md:text-start">
                    <Label
                      htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Title
                    </Label>
                    <Input
                      type="text"
                      id="email"
                      placeholder="Service name"
                      className="text-primary h-10"
                      value={title || ""}
                      onChange={(e) => !loading && setTitle(e.target.value)}
                      disabled={loading}
                    />
                  </div>

                  <div className="grid w-full md:max-full items-center gap-1.5 mt-1 max-md:text-start">
                    <Label
                      htmlFor="about"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Descriptions
                    </Label>
                    <Textarea
                      id="about"
                      name="description"
                      value={about}
                      onChange={(e) => !loading && setAbout(e.target.value)}
                      placeholder="Descriptions"
                      className="resize-none h-40 outline-none text-lg text-primary"
                      disabled={loading}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid w-full md:max-full items-center gap-1.5 max-md:text-start">
                      <Label
                        htmlFor="serviceCost"
                        className="text-base font-[600] text-titleLabel"
                      >
                        Service Cost
                      </Label>
                      <p className="text-titleLabel items-center text-xs -mt-3">
                        (you will receive)
                      </p>
                      <div className="row gap-3">
                        <Input
                          type="number"
                          id="serviceCost"
                          className="text-primary h-10 min-w-24"
                          value={serviceCost || ""}
                          onChange={(e) =>
                            !loading && setServiceCost(Number(e.target.value))
                          }
                          disabled={loading}
                        />
                        <p>{currencyIcon || "£"}</p>
                      </div>
                      <p className="text-xs text-gray-500 opacity-0">.</p>
                    </div>{" "}
                    <div className="grid w-full md:max-full items-center gap-1.5  max-md:text-start">
                      <Label
                        htmlFor="listPrice"
                        className="text-base font-[600] text-titleLabel"
                      >
                        List Price
                      </Label>
                      <p className="text-titleLabel text-xs -mt-3">
                        (customer will pay)
                      </p>
                      <div className="row gap-3">
                        <Input
                          type="number"
                          id="listPrice"
                          disabled
                          className="text-primary h-10 min-w-24"
                          value={listPricecal(serviceCost)}
                        />
                        <p>{currencyIcon || "£"}</p>
                      </div>
                      <p className="text-titleLabel text-xs font-light">
                        **Includes AMUZ App Fee (16% of List Price)
                      </p>
                    </div>
                  </div>

                  <div className="text-black text-sm text-start mt-4">
                    Pricing for individual products and services should start at{" "}
                    {currencyIcon || "£"}25 and not exceed {currencyIcon || "£"}
                    2,500
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid w-full md:max-full items-center gap-1.5  max-md:text-start">
                      <Label
                        htmlFor="time"
                        className="text-base font-[600] text-titleLabel"
                      >
                        Time
                      </Label>
                      <div className="row gap-3">
                        <Input
                          type="number"
                          id="time"
                          className="text-primary  h-10 min-w-24"
                          value={time || ""}
                          onChange={(e) => !loading && setTime(e.target.value)}
                          disabled={loading}
                        />
                        <p>hours</p>
                      </div>
                      <p className="text-sm text-gray-500">8 hours = 1 day</p>
                    </div>
                    <div className="grid w-full items-center gap-1.5 ">
                      <Label className="text-base font-[600] text-titleLabel">
                        Time
                      </Label>
                      <div className="row gap-2 items-center ">
                        <Input
                          name="duration"
                          placeholder="days"
                          disabled
                          className="resize-none h-10 outline-none text-lg text-primary"
                          value={formatDuration(time, {
                            dayLabel: "",
                            hourLabel: "",
                          })}
                          // onChange={handleInputChange}
                        />
                        <p>days</p>
                      </div>
                      <p className="text-sm text-gray-500 opacity-0">.</p>
                    </div>
                  </div>

                  <div className="text-black text-sm text-start mt-2">
                    The service needs to be delivered by the date specified, not
                    to exceed 2 weeks from Order placement
                  </div>

                  <div>
                    <p className="my-3 text-primary text-lg font-semibold text-center">
                      Customization
                    </p>

                    {/* Display existing customizations */}
                    {customizationsFinalized && customizationList.filter(isCustomizationComplete).length > 0 ? (
                      <div className="mb-6 border rounded-md p-4">
                        <div className="grid grid-cols-2 mb-2 border-b pb-2">
                          <div>
                            <p className="text-primary font-semibold">Option</p>
                          </div>
                          <div className="flex justify-end">
                            <div className="flex gap-4">
                              <p className="text-primary font-semibold w-12 text-left">
                                Time
                              </p>
                              <p className="text-primary font-semibold w-16 text-right">
                                Cost
                              </p>
                              <p className="opacity-0 w-8 text-center">hi</p>
                            </div>
                          </div>
                        </div>
                        {customizationList
                          .filter(isCustomizationComplete)
                          .map((item: any, index: number) => (
                            <div className="grid grid-cols-2 mt-3" key={index}>
                              <div>
                                <p className="text-subtitle text-sm mr-10 truncate">
                                  {item.title || "Unnamed customization"}
                                </p>
                              </div>
                              <div className="flex justify-end">
                                <div className="flex gap-4">
                                  <p className="text-subtitle w-12 text-left text-sm">
                                    +
                                    {item.duration
                                      ? `${Math.floor(item.duration / 24)}d`
                                      : "0d"}
                                  </p>
                                  <p className="text-subtitle w-16 text-right text-sm">
                                    +{currencyIcon}
                                    {item.price
                                      ? (
                                          parseFloat(item.price) /
                                          (1 - 0.16)
                                        ).toFixed(2)
                                      : "0.00"}
                                  </p>
                                  <span
                                    className="text-borderColor w-8 text-left cursor-pointer"
                                    onClick={() => !loading && setIsOpen(true)}
                                  >
                                    <Info size={16} className="text-gray-400" />
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    ) : null}

                    <div className="row justify-center mt-5">
                      <Badge
                        className={
                          !loading &&
                          title &&
                          category &&
                          serviceCost &&
                          time &&
                          about
                            ? " btn-xs w-full py-4 border-primary btn text-white"
                            : " btn-xs w-full py-4 bg-gray-300 hover:bg-gray-300 border-gray-300 btn text-white cursor-not-allowed"
                        }
                        onClick={() =>
                          !loading &&
                          title &&
                          category &&
                          serviceCost &&
                          time &&
                          about
                            ? setIsOpen(true)
                            : ""
                        }
                      >
                        {customizationList.length > 0
                          ? "Edit Customizations"
                          : "Set Customization"}
                      </Badge>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {isOpen && (
          <div className="w-full">
            <div className="flex flex-row justify-between items-center mb-4 sticky top-0 bg-white z-10">
              <div
                onClick={() => !loading && setIsOpen(false)}
                className={
                  loading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                }
              >
                <X />
              </div>
              <h2 className="text-xl font-bold">Customizations</h2>
              <p
                onClick={() => {
                  if (!loading) {
                    setIsOpen(false);
                    setCustomizationsFinalized(true);
                  }
                }}
                className={
                  !loading && title && category && serviceCost && time && about
                    ? "font-bold text-primary cursor-pointer"
                    : "font-bold text-borderColor cursor-not-allowed"
                }
              >
                Done
              </p>
            </div>

            <div className=" overflow-y-auto hide-scroll-custom">
              <div className="bg-gray-50">
                <div className=" bg-white rounded-md p-2 py-4 overflow-hidden w-full">
                  <form className="space-y-6 ">
                    {customizationList.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
                        <div className="bg-gray-100 rounded-full p-6 mb-4">
                          <Plus size={32} className="text-primary" />
                        </div>
                        <h3 className="text-xl font-semibold mb-2">
                          No Customizations Added Yet
                        </h3>
                        <p className="text-gray-500 max-w-md mb-6">
                          Customizations allow you to offer variations of your
                          service with different prices, durations, and
                          features. Click the plus button below to add your
                          first customization.
                        </p>
                        <button
                          type="button"
                          onClick={handleAddCustomization}
                          className="flex items-center gap-2 bg-primary text-white px-4 py-2 rounded-full btn"
                        >
                          <Plus size={18} />
                          <span>Add Your First Customization</span>
                        </button>
                      </div>
                    ) : (
                      customizationList.map((item: any, index: any) => (
                        <div key={index} className="p-4 rounded-md">
                          <div className="flex justify-between items-center mb-4">
                            <p className="text-lg font-semibold">
                              Customization #{index + 1}
                            </p>
                            <button
                              type="button"
                              onClick={() =>
                                !loading && handleDeleteCustomization(index)
                              }
                              className={`text-red-500 hover:text-red-700 ${
                                loading ? "opacity-50 cursor-not-allowed" : ""
                              }`}
                              disabled={loading}
                            >
                              <Trash size={18} />
                            </button>
                          </div>

                          <div className="grid w-full items-center gap-1.5 mt-6">
                            <Label
                              htmlFor={`title-${index}`}
                              className="text-base font-[600] text-titleLabel"
                            >
                              Title
                            </Label>
                            <Input
                              type="text"
                              id={`title-${index}`}
                              placeholder="Customization title"
                              className="text-primary h-10"
                              value={item.title || ""}
                              onChange={(e) =>
                                !loading &&
                                handleUpdateCustomization(
                                  index,
                                  "title",
                                  e.target.value
                                )
                              }
                              disabled={loading}
                            />
                          </div>

                          <div className="grid w-full items-center gap-1.5 mt-3">
                            <Label
                              htmlFor={`about-${index}`}
                              className="text-base font-[600] text-titleLabel"
                            >
                              Descriptions
                            </Label>
                            <Textarea
                              id={`about-${index}`}
                              value={item.about || ""}
                              onChange={(e) =>
                                !loading &&
                                handleUpdateCustomization(
                                  index,
                                  "about",
                                  e.target.value
                                )
                              }
                              placeholder="Descriptions"
                              className="resize-none h-40  text-lg text-primary w-full"
                              disabled={loading}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid w-full items-center gap-1.5 mt-3">
                              <Label
                                htmlFor={`price-${index}`}
                                className="text-base font-[600] text-titleLabel"
                              >
                                Service Cost
                              </Label>
                              <p className="text-titleLabel items-center text-xs -mt-3">
                                (you will receive)
                              </p>
                              <div className="flex gap-3 items-center">
                                <Input
                                  type="number"
                                  id={`price-${index}`}
                                  className="text-primary h-10 min-w-24"
                                  value={item.price || ""}
                                  onChange={(e) =>
                                    !loading &&
                                    handleUpdateCustomization(
                                      index,
                                      "price",
                                      e.target.value
                                    )
                                  }
                                  disabled={loading}
                                />
                                <p>{currencyIcon || "£"}</p>
                              </div>
                              <p className="text-sm text-gray-500 opacity-0">
                                .
                              </p>
                            </div>

                            <div className="grid w-full items-center gap-1.5 mt-3">
                              <Label
                                htmlFor={`listPrice-${index}`}
                                className="text-base font-[600] text-titleLabel"
                              >
                                List Price
                              </Label>
                              <p className="text-titleLabel text-xs -mt-3">
                                (customer will pay)
                              </p>
                              <div className="flex gap-3 items-center">
                                <Input
                                  type="number"
                                  id={`listPrice-${index}`}
                                  className="text-primary h-10 min-w-24"
                                  value={listPricecal(item.price)}
                                  disabled
                                />
                                <p>{currencyIcon || "£"}</p>
                              </div>
                              <p className="text-titleLabel text-xs font-light">
                                **Includes AMUZ App Fee (16% of List Price)
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid w-full items-center gap-1.5 mt-3">
                              <Label
                                htmlFor={`duration-${index}`}
                                className="text-primary mb-0 font-[600] block text-start"
                              >
                                Time
                              </Label>
                              <div className="flex gap-2 items-center">
                                <Input
                                  type="number"
                                  id={`duration-${index}`}
                                  className="text-primary h-10"
                                  value={item.duration || ""}
                                  onChange={(e) =>
                                    !loading &&
                                    handleUpdateCustomization(
                                      index,
                                      "duration",
                                      e.target.value
                                    )
                                  }
                                  disabled={loading}
                                />
                                <p>hours</p>
                              </div>
                              <p className="text-sm text-gray-500">
                                8 hours = 1 day
                              </p>
                            </div>
                            <div className="grid w-full items-center gap-1.5 mt-3">
                              <Label className="text-primary mb-0 font-[600] block text-start">
                                Time
                              </Label>
                              <div className="row gap-2 items-center ">
                                <Input
                                  name="duration"
                                  placeholder="days"
                                  disabled
                                  className="resize-none h-10 outline-none text-lg text-primary"
                                  value={formatDuration(item.duration || "", {
                                    dayLabel: "",
                                    hourLabel: "",
                                  })}
                                />
                                <p>days</p>
                              </div>
                              <p className="text-sm text-gray-500 opacity-0">
                                .
                              </p>
                            </div>
                          </div>

                          <div className="mt-4">
                            <p className="text-primary font-[600]">
                              Media Files
                            </p>

                            {/* Custom media uploader */}
                            <div className="border-2 z border-dashed border-gray-300 rounded-md p-4 text-center mb-4 relative">
                              <input
                                type="file"
                                id={`media-upload-${index}`}
                                multiple
                                accept="image/*,video/*"
                                className="hidden"
                                disabled={uploading || loading}
                                onChange={(e) => {
                                  if (
                                    e.target.files &&
                                    e.target.files.length > 0 &&
                                    !uploading &&
                                    !loading
                                  ) {
                                    handleMediaUpload(
                                      index,
                                      Array.from(e.target.files)
                                    );
                                  }
                                }}
                              />
                              <label
                                htmlFor={`media-upload-${index}`}
                                className={`block ${
                                  uploading
                                    ? "cursor-not-allowed opacity-50"
                                    : "cursor-pointer"
                                }`}
                              >
                                {uploading ? (
                                  <Loader className="mx-auto h-8 w-8 text-primary animate-spin" />
                                ) : (
                                  <Upload className="mx-auto h-8 w-8 text-gray-400" />
                                )}
                                <p className="mt-2 text-sm text-gray-500">
                                  {uploading
                                    ? "Uploading media..."
                                    : "Click to upload images or videos"}
                                </p>
                                {!uploading && (
                                  <p className="mt-1 text-xs text-gray-400">
                                    Files will be stored in their respective
                                    folders
                                  </p>
                                )}
                              </label>
                              {uploading && (
                                <div className="mt-2 flex items-center justify-center">
                                  <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                                    <div
                                      className="h-full bg-primary rounded-full animate-pulse"
                                      style={{ width: "100%" }}
                                    ></div>
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Display uploaded media */}
                            {item.media && item.media.length > 0 && (
                              <div className="grid grid-cols-3 gap-3 mt-3">
                                {item.media.map(
                                  (mediaUrl: any, mediaIndex: any) => {
                                    const isVideo = mediaUrl.includes("videos");
                                    return (
                                      <div
                                        key={mediaIndex}
                                        className="relative h-24 bg-gray-100 rounded-md overflow-hidden"
                                      >
                                        {isVideo ? (
                                          <div className="w-full h-full flex items-center justify-center bg-black">
                                            <video
                                              src={mediaUrl}
                                              className="max-h-full max-w-full"
                                            />
                                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                                              <span className="text-white text-xs px-2 py-1 bg-black bg-opacity-50 rounded">
                                                Video
                                              </span>
                                            </div>
                                          </div>
                                        ) : (
                                          <img
                                            src={mediaUrl}
                                            alt={`Media ${mediaIndex}`}
                                            className="w-full h-full object-cover"
                                          />
                                        )}
                                        <button
                                          type="button"
                                          onClick={() =>
                                            !uploading &&
                                            !loading &&
                                            handleRemoveMedia(
                                              index,
                                              mediaUrl
                                            )
                                          }
                                          className={`absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 ${
                                            uploading || loading
                                              ? "opacity-50 cursor-not-allowed"
                                              : ""
                                          }`}
                                          disabled={uploading || loading}
                                          aria-label="Remove media"
                                        >
                                          <X size={12} />
                                        </button>
                                      </div>
                                    );
                                  }
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))
                    )}

                    {customizationList.length > 0 && (
                      <div className="absolute bottom-8 right-8 z-10">
                        <button
                          type="button"
                          onClick={handleAddCustomization}
                          className="flex items-center justify-center bg-primary text-white rounded-full h-12 w-12 shadow-lg"
                        >
                          <Plus size={26} />
                        </button>
                      </div>
                    )}
                  </form>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return <>{renderContent()}</>;
};

export default CreateService;
