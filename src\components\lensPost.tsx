"use client";
import React, { useEffect, useState } from "react";
import FeedPost from "@/components/FeedPost";
import { getLensProfilesById } from "@/services/lensService";
import {
  AccountsBulkQuery,
  MainContentFocus,
  PageSize,
  PostsQuery,
  PostType,
  useAccountsBulkQuery,
  usePostsQuery,
} from "@/graphql/test/generated";

export let exportData: any;

export default function LensPost({ userId }: any) {
  const id = "0x08cfd6";

  const [profiles, setProfiles] = useState<
    Array<{
      localName: string;
    }>
  >([
    {
      localName: "chaoticmonk",
    },
  ]); // State for search input
  const [profilesIds, setProfilesIds] = useState<string[]>(["0x89dc", "0x326c"]); // State for search input
  const [currentCursor, setCurrentCursor] = React.useState<string | null>(null);
  const [testData, setTestData] = React.useState<Array<PostsQuery["posts"]["items"][0]>>([]);

  const {
    data: profileData,
    error: profileError,
    isLoading: loadingProfile,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames: profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!profiles,
    }
  );

  const {
    isLoading: isLoadingPublications,
    data: publicationsData,
    error: publicationsError,
  } = usePostsQuery(
    {
      request: {
        filter: {
          authors: profilesIds, // joanakawaharalino
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        cursor: currentCursor,
        pageSize: PageSize.Fifty,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: profilesIds.length > 0, // Only execute query when profilesIds has at least one element
    }
  );

  useEffect(() => {
    const fetchLensProfilesByCategory = async (category: string) => {
      // get lens profiles
      const resp = await getLensProfilesById(category);
      const lens_profiles: Array<{
        localName: string;
      }> = resp?.lens_ids?.map(
        (curr: any) => {
          return {
            localName: curr,
          };
        }
        // `lens/${curr}`
      );

      // console.log({ lens_profiles });
      setProfiles(lens_profiles);
      // console.log({publicationsData});
    };

    fetchLensProfilesByCategory("music");
  }, []);

  useEffect(() => {
    if (profileData) {
      const lens_ids: string[] = profileData?.accountsBulk?.map(
        (curr: AccountsBulkQuery["accountsBulk"][0]) => curr.address
      );
      // console.log({ lens_ids });

      setProfilesIds(lens_ids); // Triggers the publications query
    }
  }, [profileData]);

  useEffect(() => {
    // if (profileData) console.log("Updated profileData:", profileData);
  }, [profileData]);

  useEffect(() => {
    // if (publicationsData)
    // console.log("Updated publicationsData:", publicationsData);

    loadNextPage();
    if (publicationsData?.posts?.items) {
      // setTestData((curr:any)=>[...curr , ...publicationsData?.posts.items]);
      for (let index = 0; index < publicationsData?.posts.items.length; index++) {
        setTestData((prevData) => [...(prevData ?? []), publicationsData?.posts?.items[index]]);
      }
    }
  }, [publicationsData]);

  const [filteredData, setFilteredData] = useState<{ handle: string; data: any[] }[]>([]);

  useEffect(() => {
    if (testData && profiles) {
      // console.log("testData publicationsData:", testData);
      // console.log("lens_profiles:", profiles);

      // Group and filter data based on lens_profiles with a limit of 15 items per handle
      const groupedData = profiles
        .map((handle) => ({
          handle: handle?.localName,
          data: testData
            .filter(
              (item: PostsQuery["posts"]["items"][0]) =>
                item?.author?.username?.localName === handle.localName
            ) // Match handle
            .slice(0, 15), // Take only the first 15 items
        }))
        .filter((group) => group.data.length > 0); // Remove empty groups

      // Store the filtered data in state
      setFilteredData(groupedData);
    }
  }, [testData, profiles]);

  const loadNextPage = () => {
    const nextCursor = publicationsData?.posts.pageInfo.next;
    // console.log({ nextCursor });

    if (nextCursor) {
      setCurrentCursor(nextCursor);
    }
  };

  if (publicationsError || profileError) {
    return <div>Could not find this profile.</div>;
  }

  if (loadingProfile) {
    return <div>Loading profile...</div>;
  }

  exportData = publicationsData?.posts.items;

  // console.log(testData);

  return (
    <div>
      <div>
        <button onClick={loadNextPage}>Next post</button>

        <div>
          {isLoadingPublications ? (
            <div>Loading Publications...</div>
          ) : true ? (
            // Iterate over the items in the publications array
            publicationsData?.posts.items.map((publication: PostsQuery["posts"]["items"][0]) => (
              <>
                <FeedPost
                  publication={publication}
                  key={publication.id}
                  data={publicationsData?.posts}
                />
              </>
            ))
          ) : (
            "nil"
          )}
        </div>
      </div>
    </div>
  );
}
