import {
  addDoc,
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  Timestamp,
  where,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";

const LENS_COLLECTION = "lens";

interface IData {
  category: string;
  lens_profiles: Array<string>;
}

interface PaginatedResult {
  profiles: string[];
  page: number;
  count: number;
  hasMore: boolean;
}

const data: IData[] = [
  {
    category: "art",
    lens_profiles: [
      "aoifeodwyer",
      "alimo",
      "mrcallaby",
      "chocolategirl",
      "fercaggiano",
      "andrealitmus",
      "mistershot",
    ],
  },
  {
    category: "music",
    lens_profiles: [
      "sttsm",
      "benalistair",
      "yunice",
      "fredriko",
      "piano-kamilla",
      "soundoffractures",
    ],
  },
  {
    category: "film_photography",
    lens_profiles: ["chaoticmonk", "tabassom", "icyviker"],
  },
  {
    category: "literature",
    lens_profiles: ["sheeban", "storyprof", "mengyao"],
  },
  {
    category: "multidisciplinary",
    lens_profiles: ["noko_", "eliot89"],
  },
  {
    category: "groups",
    lens_profiles: ["t2world"],
  },
];

const insertLensProfiles = async (data: IData) => {
  try {
    const chatWithTimestamps = {
      ...data,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };
    const { db } = await initFirebase();

    const docRef = await addDoc(collection(db, LENS_COLLECTION), chatWithTimestamps);
    // console.log({id:docRef.id});

    return { success: true, id: docRef.id };
  } catch (error) {
    // console.error("Error creating chat:", error);
    return { success: false, error: "Failed to create chat" };
  }
};

// export const createLensProfiles = async () => {
//     await Promise.all(data.map(async(curr:IData)=>{
//             await insertLensProfiles(curr);
//     }))
//     return { success: true, id:"asdasd" };
//   };

export const getLensProfilesById = async (id: string) => {
  try {
    const { db } = await initFirebase();

    const querySnapshot = await getDocs(collection(db, LENS_COLLECTION));
    const lens_profile: any = querySnapshot.docs
      .map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }))
      .filter((curr: any) => curr.category === id);
    // console.log({ lens_profile });

    return {
      success: true,
      lens_ids: lens_profile?.[0]?.lens_profiles ?? [],
    };
  } catch (error) {
    // console.error("Error getting category by ID:", error);
    return { success: false, error: "Failed to fetch category by ID" };
  }
};

export const getLensProfileDetails = async (profileId: string) => {
  try {
    // Check if profileId is valid
    if (!profileId || typeof profileId !== "string" || profileId.trim() === "") {
      console.error("Invalid profileId provided:", profileId);
      return { success: false, error: "Invalid profile ID provided" };
    }

    const { db } = await initFirebase();

    // Query Firestore to find documents where lens_profiles array contains profileId
    const lensQuery = query(
      collection(db, LENS_COLLECTION),
      where("lens_profiles", "array-contains", profileId) // ✅ Checks if profileId exists in lens_profiles array
    );

    const lensSnapshot = await getDocs(lensQuery);

    if (lensSnapshot.empty) {
      return { success: false, error: "No matching lens profile found." };
    }

    // Extract the document details
    const lensDetails = lensSnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return {
      success: true,
      lens_details: lensDetails,
    };
  } catch (error) {
    console.error("Error fetching lens profile details:", error);
    return { success: false, error: "Failed to fetch lens profile details" };
  }
};

// updated pagintead
export const getLensProfilesById_V2 = async (
  {
    id,
    page,
    count,
  }: {
    id: string;
    page: number;
    count: number;
  }
): Promise<PaginatedResult> => {
  const data = await getLensProfilesById(id);
  const total = data.lens_ids.length;

  const start = (page - 1) * count;
  const end = start + count;
  console.log({data});
  

  const paginatedProfiles = data.lens_ids.slice(start, end);
  const hasMore = end < total;

  return {
    profiles: paginatedProfiles,
    page,
    count,
    hasMore,
  };
};
