// pages/ExamplePage.tsx

import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { themes } from "../../../../theme";
import Link from "next/link";
import ScrollButton from "@/components/bottomArrow";
import { useRef } from "react";
import ScrollButtonRight from "@/components/rightArrow";
import CategoryComp from "@/globalComponents/homeComp/categoryComp";
import useAuth from "@/hook";
import EventsCard from "./eventsCard";
import ScrollButtonLeft from "@/components/leftArrow";

const data = [
  {
    img: "/assets/img.svg",
    title: "My Feed",
    backgroundColor: "#000000",
  },
  {
    img: "/assets/music.svg",
    title: "Music",
    backgroundColor: "#E5B045",
  },
  {
    img: "/assets/litrature.svg",
    title: "Literature",
    backgroundColor: "#CF5943",
  },
  {
    img: "/assets/art.svg",
    backgroundColor: "#3C5F9A",
    title: "Art",
  },
  {
    img: "/assets/film.svg",
    title: "Film & Photography",
    backgroundColor: "#46B933",
  },
  {
    img: "/assets/Theatre.svg",
    title: "Theatre & Performance",
    backgroundColor: "#E073D2",
  },
  {
    img: "/assets/multi.svg",
    title: "Multidisciplinary",
    backgroundColor: "#5331BC",
  },
  {
    img: "/assets/groups.svg",
    title: "Groups",
    backgroundColor: "#616770",
  },
];

const EventsHome = () => {
  const user = useAuth(); // Assuming useAuth returns null or undefined if the user is not logged in

  const scrollRef = useRef<HTMLDivElement>(null);

  function hexToRgba(hex: any, alpha = 1) {
    const [r, g, b] = hex
      .replace(/^#/, "")
      .match(/.{1,2}/g)
      .map((val: any) => parseInt(val, 16));
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  return (
    <div className="relative p-0">
      <ScrollButton scrollRef={scrollRef} />
      <ScrollButtonRight scrollRef={scrollRef} />
      <ScrollButtonLeft scrollRef={scrollRef} />

      <div
        ref={scrollRef}
        className="overflow-y-auto p-0 h-[calc(100vh-205px)] hide-scroll"
        // style={{ maxHeight: "100vh" }}
      >
        <div className="space-y-4">
          <div
            className={`flex flex-row max-md:flex-col w-full gap-3 hide-scroll bg-white max-md:h-full ${
              user.isLogin ? "pl-2" : ""
            }`}
            ref={scrollRef}
          >
            {data.map((item, index) => {
              return (
                <div className="" key={index}>
                  {!user.isLogin && !(item.title == "My Feed") && (
                    <>
                      <CategoryComp item={item} />
                    </>
                  )}

                  {user.isLogin && (
                    <>
                      <CategoryComp item={item} />
                    </>
                  )}

                  <div
                    className="overflow-auto hide-scroll h-full mt-0"
                    // style={{ minHeight: "100%" }}
                    // Attach ref only to the second item
                  >
                    <div className="max-md:hidden">
                      {Array.from({ length: 1 }).map((_, indexs) => (
                        <div className="mb-3" key={indexs}>
                          {Object.entries(themes).map(
                            ([themeName, themeProperties]) =>
                              themeProperties.title === item.title && (
                                <>
                                  {!user.isLogin &&
                                    !(item.title == "My Feed") && (
                                      <>
                                        <EventsCard
                                          border={
                                            themeProperties.backgroundColor
                                          }
                                          themeProperties={themeProperties}
                                          isMyfeed={true}
                                        />
                                      </>
                                    )}

                                  {user.isLogin && (
                                    <>
                                      {/* <GlobalCardEvents
                                        border={themeProperties.backgroundColor}
                                      /> */}
                                      <EventsCard
                                        border={themeProperties.backgroundColor}
                                        themeProperties={themeProperties}
                                        isMyfeed={true}
                                      />
                                    </>
                                  )}
                                </>
                              )
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventsHome;
