// pages/ExamplePage.tsx
import { themes } from "../../../../theme";
import ScrollButton from "@/components/bottomArrow";
import { useRef } from "react";
import ScrollButtonRight from "@/components/rightArrow";
import CategoryComp from "@/globalComponents/homeComp/categoryComp";
import useAuth from "@/hook";
import ProfileCard from "./profileCard";
import ScrollButtonLeft from "@/components/leftArrow";
import { useAccount } from "wagmi";

const data = [
  {
    img: "/assets/img.svg",
    title: "My Feed",
    backgroundColor: "#000000",
  },
  {
    img: "/assets/music.svg",
    title: "Music",
    backgroundColor: "#E5B045",
  },
  {
    img: "/assets/litrature.svg",
    title: "Literature",
    backgroundColor: "#CF5943",
  },
  {
    img: "/assets/art.svg",
    backgroundColor: "#3C5F9A",
    title: "Art",
  },
  {
    img: "/assets/film.svg",
    title: "Film & Photography",
    backgroundColor: "#46B933",
  },
  {
    img: "/assets/Theatre.svg",
    title: "Theatre & Performance",
    backgroundColor: "#E073D2",
  },
  {
    img: "/assets/multi.svg",
    title: "Multidisciplinary",
    backgroundColor: "#5331BC",
  },
  {
    img: "/assets/groups.svg",
    title: "Groups",
    backgroundColor: "#616770",
  },
];

const ProfileHome = () => {
  const user = useAuth(); // Assuming useAuth returns null or undefined if the user is not logged in
  const { address, isConnected } = useAccount();
  console.log("index", { address, isConnected });
  const scrollRef = useRef<HTMLDivElement>(null);

  return (
    <div className="relative p-0">
      <ScrollButton scrollRef={scrollRef} />
      <ScrollButtonRight scrollRef={scrollRef} />
      <ScrollButtonLeft scrollRef={scrollRef} />

      <div
        ref={scrollRef}
        className="overflow-y-auto p-0 h-[calc(100vh-205px)] hide-scroll"
        // style={{ maxHeight: "100vh" }}
      >
        <div className="space-y-4">
          <div
            className={`flex flex-row max-md:flex-col w-full gap-3 hide-scroll bg-white max-md:h-full ${
              user.isLogin ? "pl-2" : ""
            }`}
            ref={scrollRef}
          >
            {data.map((item: any, index: any) => {
              return (
                <div className="" key={index}>
                  {!user.isLogin && !(item.title == "My Feed") && (
                    <>
                      <CategoryComp item={item} />
                    </>
                  )}

                  {user.isLogin && (
                    <>
                      <CategoryComp item={item} />
                    </>
                  )}

                  <div
                    className="overflow-auto hide-scroll h-full mt-0"
                    style={{ minHeight: "100%" }}
                    // Attach ref only to the second item
                  >
                    <div className="">
                      {Array.from({ length: 1 }).map((_, indexs) => (
                        <div key={indexs}>
                          {Object.entries(themes).map(
                            ([themeName, themeProperties]) =>
                              themeProperties.title === item.title && (
                                <div key={themeName}>
                                  {!user.isLogin && !(item.title == "My Feed") && (
                                    <>
                                      <ProfileCard
                                        border={themeProperties.backgroundColor}
                                        themeProperties={themeProperties}
                                      />
                                    </>
                                  )}

                                  {user.isLogin && (
                                    <>
                                      <ProfileCard
                                        border={themeProperties.backgroundColor}
                                        themeProperties={themeProperties}
                                      />
                                    </>
                                  )}
                                </div>
                              )
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHome;
