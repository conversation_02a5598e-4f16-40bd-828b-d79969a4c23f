"use client";
import { useEffect, useState } from "react";
import ProfileInfo from "./profileInfo";
import TabsMyProfile from "./tabs";
import { themes } from "../../../../theme";
import useProfile from "@/hook/profileData";
import { getUserById } from "@/services/usersServices";
import useAuth from "@/hook";
import LoadingOverlay from "@/components/loadingOverlay";
import { Plus, X } from "react-feather";
import CreatePost from "./tabs/posts/createPost";
import CreateService from "./tabs/services/createService";
import CreateEvent from "./tabs/events/createEvent";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import { useSearchParams } from "next/navigation";
import NotFound from "@/app/not-found";

const MyProfile = (props: any) => {
  const searchParams = useSearchParams();
  const view = searchParams?.get("view");
  const [isOtherProfile, setIsOtherProfile] = useState(false);
  const [toggleMain, setToggleMain] = useState(false);
  const [componentKey, setComponentKey] = useState(0);
  const [profileData, setProfileData] = useState<any>(null);
  const [bgColor, setBgColor] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true); // NEW: Loading state
  const [selectedTabs, setSelectedTabs] = useState(view || "Posts"); // NEW: Loading state
  const [selectedTabsStatus, setSelectedTabsStatus] = useState(false); // NEW: Loading state
  const [selectedProfileTabs, setSelectedProfileTabs] = useState("Following"); // NEW: Loading state
  const [isOpen, setIsOpen] = useState(false);
  const [isRefatch, setIsRefatch] = useState(false);
  const [showLoader, setShowLoader] = useState(false);
  const [showErrorMsg, setShowErrorMsg] = useState(false);

  const auth = useAuth();

  const userId = props.userId !== "my-profile" ? props.userId : auth.userId;
  const profile = useProfile(userId);
  // console.log(userId);

  const fetchUser = async () => {
    try {
      const response = await getUserById(userId);

      if (response?.success && response.user) {
        setProfileData(response.user);
      } else {
        console.error("Invalid response structure:", response);
      }
    } catch (err) {
      console.error("Error fetching user data:", err);
    } finally {
      setLoading(false); // Stop loading after fetch
    }
  };

  useEffect(() => {
    setLoading(true); // Start loading before fetching
    fetchUser();
    setIsOtherProfile(props.userId !== auth.userId);
    setComponentKey((prevKey) => prevKey + 1); // Change key to force re-render
    setToggleMain(false);
  }, [userId, toggleMain]);

  useEffect(() => {
    if (profileData?.categories?.length) {
      const selectedCategory =
        profileData.categories[0] === "Storytelling" ? "Literature" : profileData.categories[0];

      const selectedTheme = Object.values(themes).find((theme) => theme.title === selectedCategory);

      setBgColor(selectedTheme?.backgroundColor);
    }
  }, [profileData]);

  useEffect(() => {
    if (!loading && !profileData) {
      setShowLoader(true);
      setShowErrorMsg(false);
      const timer = setTimeout(() => {
        setShowLoader(false);
        setShowErrorMsg(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [loading, profileData]);

  if (loading) {
    return (
      <div className="h-full">
        <LoadingOverlay isLoading={loading} />
      </div>
    );
  }

  if (!profileData) {
    if (showLoader) {
      return (
        <div className="h-full">
          <LoadingOverlay isLoading={true} />
        </div>
      );
    }
    if (showErrorMsg) {
      return <div className=' -mt-12'><NotFound  /></div>;
    }
    // fallback: don't render anything until loader or error message is shown
    return null;
  }

  return (
    <>
      <div className="relative h-full">
        <div className="grid grid-cols-2 md:gap-3 max-md:grid-cols-1 max-md:gap-y-3 mt-1 max-md:bg-white">
          <div className="md:overflow-y-scroll md:overflow-x-hidden hide-scroll-custom md:h-screen pb-48 max-md:pb-0 mb-8 max-md:mb-0 shadow-lg max-md:shadow-none bg-white pt-2">
            <ProfileInfo
              key={componentKey}
              isOtherProfileStatus={props.otherUserStatus}
              isOtherProfile={isOtherProfile}
              Category={
                profileData?.categories?.[0] === "Storytelling"
                  ? "Literature"
                  : profileData?.categories?.[0]
              }
              otherUserID={props.userId}
              setToggleMain={setToggleMain}
              setSelectedTabs={setSelectedTabs}
              setSelectedTabsStatus={setSelectedTabsStatus}
              setSelectedProfileTabs={setSelectedProfileTabs}
            />
          </div>
          <div className="lg:h-screen shadow-lg max-md:shadow-none bg-white pt-2">
            <TabsMyProfile
              key={componentKey}
              activeBgColor={bgColor}
              isOtherProfile={isOtherProfile}
              isOtherProfileStatus={props.otherUserStatus}
              otherUserID={props.userId}
              setToggleMain={setToggleMain}
              selectedTabs={selectedTabs}
              setSelectedTabs={setSelectedTabs}
              selectedProfileTabs={selectedProfileTabs}
              setSelectedProfileTabs={setSelectedProfileTabs}
              selectedTabsStatus={selectedTabsStatus}
              isRefatch={isRefatch}
              setIsOpen={setIsOpen}
            />
          </div>
        </div>
        <div>
          {!(selectedTabs == "Profiles") && !isOtherProfile && (
            <div
              className="fixed bottom-6 right-10 max-md:right-4 max-md:bottom-2 z-50 p-3 rounded-full text-white font-bold cursor-pointer bg-color-indicator-plus shadow-lg"
              onClick={() =>
                profile?.profileData?.categories.length > 0
                  ? setIsOpen(true)
                  : alert("Add Category first to perform this action")
              }
              style={
                {
                  "--active-bg-color-indicator-plus": bgColor,
                } as React.CSSProperties
              } // Set CSS variable
            >
              <Plus className="w-10 h-10" style={{ strokeWidth: "2.5px" }} />
            </div>
          )}
        </div>
        <div>
          <Modal
            isDismissable={false}
            isOpen={isOpen}
            placement="auto"
            onOpenChange={setIsOpen}
            hideCloseButton={true}
            scrollBehavior="inside"
            size="2xl"
          >
            <ModalContent className="modal-content">
              {(onClose) => (
                <>
                  <ModalBody>
                    <div className=" overflow-y-scroll hide-scroll-custom">
                      {selectedTabs == "Posts" && (
                        <CreatePost
                          category={profile?.profileData?.categories}
                          setIsOpen={setIsOpen}
                          setIsRefatch={setIsRefatch}
                          setToggleMain={setToggleMain}
                        />
                      )}
                      {selectedTabs == "Services" && (
                        <CreateService
                          category={profile?.profileData?.categories}
                          setIsOpen={setIsOpen}
                          setToggleMain={setToggleMain}
                        />
                      )}
                      {selectedTabs == "Events" && (
                        <CreateEvent
                          category={profile?.profileData?.categories}
                          setIsOpen={setIsOpen}
                          setToggleMain={setToggleMain}
                        />
                      )}
                    </div>
                  </ModalBody>
                </>
              )}
            </ModalContent>
          </Modal>
        </div>
      </div>
    </>
  );
};

export default MyProfile;
