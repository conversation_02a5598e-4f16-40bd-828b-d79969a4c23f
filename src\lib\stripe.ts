import Stripe from 'stripe';

// Stripe configuration
// const stripeConfig = {
//   apiVersion: '2024-04-10' as const,
// };

// Initialize Stripe instances
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

// Initialize US Stripe instance only if the key is available, otherwise use default
export const stripeUS = process.env.STRIPE_SECRET_KEY_US
  ? new Stripe(process.env.STRIPE_SECRET_KEY_US as string)
  : stripe; // Fallback to default stripe instance

// Helper function to get the appropriate Stripe instance
export function getStripeInstance(isUS: boolean = false): Stripe {
  return isUS ? stripeUS : stripe;
}

// Helper function to automatically determine Stripe instance based on currency
export function getStripeInstanceByCurrency(currency: string): { stripeInstance: Stripe; isUS: boolean } {
  if (!currency) {
    console.warn('No currency provided, using default Stripe instance');
    return { stripeInstance: stripe, isUS: false };
  }

  const normalizedCurrency = currency.toUpperCase();

  // USD typically uses the US Stripe instance (if available)
  const isUS = normalizedCurrency === 'USD';

  // If US key is not configured, always use default instance
  const hasUSKey = process.env.STRIPE_SECRET_KEY_US && process.env.STRIPE_SECRET_KEY_US !== process.env.STRIPE_SECRET_KEY;

  return {
    stripeInstance: (isUS && hasUSKey) ? stripeUS : stripe,
    isUS: isUS && hasUSKey
  };
}

// Helper function to determine if a currency should use US Stripe instance
export function isUSCurrency(currency: string): boolean {
  return currency.toUpperCase() === 'USD';
}

// Type definitions for common request bodies
export interface StripeCustomerRequest {
  email: string;
  isUS?: string;
}

export interface StripeUpdateCustomerRequest {
  customerId: string;
  paymentMethodId: string;
  city: string;
  country: string;
  line1: string;
  line2?: string;
  postalCode: string;
  state: string;
  name: string;
  phone: string;
  isUS?: string;
}

export interface StripeSetupIntentRequest {
  customerId: string;
  paymentMethodTypes: string[];
  isUS?: string;
}

export interface StripePaymentMethodRequest {
  paymentMethodId?: string;
  customerId?: string;
  email?: string;
  name?: string;
  phone?: string;
  city?: string;
  country?: string;
  line1?: string;
  line2?: string;
  postalCode?: string;
  state?: string;
  isUS?: string;
}

export interface StripePaymentIntentRequest {
  customerId: string;
  amount: string;
  currency: string;
  receipt_email: string;
  capture_method: string;
  isUS?: string;
}

export interface StripeUpdatePaymentIntentRequest {
  paymentId: string;
  orderId: string;
  description: string;
  receipt_email: string;
  isUS?: string;
}

export interface StripeCapturePaymentRequest {
  paymentId: string;
  isUS?: string;
}

export interface StripeTransferRequest {
  amount: string;
  currency: string;
  chargeId: string;
  accountId: string;
  isUS?: string;
}

export interface StripeUpdateTransferRequest {
  transferId: string;
  orderId: string;
  description: string;
  isUS?: string;
}

export interface StripeUpdateChargeRequest {
  paymentId: string;
  orderId: string;
  description: string;
  accountId: string;
  isUS?: string;
}

export interface StripeCreateAccountRequest {
  email: string;
  isUS?: string;
}

export interface StripeAccountLinkRequest {
  accountId: string;
  refreshUrl: string;
  returnUrl: string;
  type: string;
  isUS?: string;
}

export interface StripeAccountRequest {
  accountId: string;
  isUS?: string;
}

export interface StripeInvoiceRequest {
  customerId: string;
  isUS?: string;
}
