import {
  getFirestore,
  collection,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  deleteDoc,
  setDoc,
  getDoc,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";

export interface AuthBridge {
  user_id: string;
  email: string | null;
  lens_id: string;
  wallet_id: string;
}

export interface UserProfile {
  about_me?: string;
  app_version?: string;
  basketOrdersCount?: number;
  bookmarks?: string[];
  categories?: string[];
  created_at?: string;
  currency?: string;
  currentStatus?: string;
  date_of_birth?: string;
  device_language?: string;
  email?: string | null;
  events?: string[];
  full_name?: string;
  hashtags?: string[];
  id: string;
  isEightyPercentsFilled?: boolean;
  isMultiCurrenciesInfoShowed?: boolean;
  isUS?: boolean;
  languages?: string[];
  last_seen?: Date;
  location?: string;
  myOpenOrdersCount?: number;
  password?: string | null | undefined; // Consider encrypting this if storing
  payment_method_id?: string | null;
  personal_moto?: string;
  posts?: string[];
  profile_name?: string;
  services?: string[];
  starredPosts?: string[];
  stripe_id?: string;
  type: "creator" | "user" | "admin"; // Adjust if there are more types
  followers: string[];
  following: string[];
}

const AUTH_BRIDGE_COLLECTION = "auth_bridge";
const USERS_COLLECTION = "users";

export const FindByUserId = async ({ user_id }: { user_id: string }) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);
    const q = query(authBridgeRef, where("user_id", "==", user_id));
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }
    return null;
  } catch (error) {
    console.log({ error });
    return { success: false, error: "Failed to find userid in auth bridge" };
  }
};

//
export const FindByWalletId = async ({ wallet_id }: { wallet_id: string }) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);
    const q = query(authBridgeRef, where("wallet_id", "==", wallet_id));
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }
    return null;
  } catch (error) {
    console.log({ error });
    return { success: false, error: "Failed to find userid in auth bridge" };
  }
};

export const FindByLensId = async ({ lens_id }: { lens_id: string }) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);
    const q = query(authBridgeRef, where("lens_id", "==", lens_id));
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }
    return null;
  } catch (error) {
    console.log({ error });
    return { success: false, error: "Failed to find userid in auth bridge" };
  }
};

//
export const UpdateAuthBridgeByUserId = async ({
  user_id,
  updateData,
}: {
  user_id: string;
  updateData: Partial<AuthBridge>;
}) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, "auth_bridge");
    const q = query(authBridgeRef, where("user_id", "==", user_id));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const docRef = doc(db, "auth_bridge", querySnapshot.docs[0].id);
      await updateDoc(docRef, updateData);
      console.log("Document updated successfully");
      return {
        success: true,
        message: "success",
      };
    } else {
      return {
        success: false,
        message: "user_id_not_found",
      };
      console.log("No document found with the specified user_id");
    }
  } catch (error) {
    console.log({ error });
    return { success: false, error: "Failed to update auth_bridge" };
  }
};

export const UpdateAuthBridgeByWalletId = async ({
  wallet_id,
  updateData,
}: {
  wallet_id: string;
  updateData: Partial<AuthBridge>;
}) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, "auth_bridge");
    const q = query(authBridgeRef, where("wallet_id", "==", wallet_id));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const docRef = doc(db, "auth_bridge", querySnapshot.docs[0].id);
      await updateDoc(docRef, updateData);
      console.log("Document updated successfully");
      return {
        success: true,
        message: "success",
      };
    } else {
      return {
        success: false,
        message: "user_id_not_found",
      };
    }
  } catch (error) {
    console.log({ error });
    return { success: false, error: "Failed to update auth_bridge" };
  }
};

export const AddAuthBridge = async ({
  data,
}: {
  data: Partial<AuthBridge>;
}) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);

    const docRef = await addDoc(authBridgeRef, data);
    return { success: true, error: docRef.id };
  } catch (error) {
    console.log({ error });
    return { success: false, error: "Failed to add auth_bridge" };
  }
};

export const _UpdateAuthBridge_V2 = async ({
  email,
  user_id,
  wallet_id,
  lens_id,
  lens_code,
}: {
  email?: string | null;
  user_id?: string | null;
  wallet_id?: string | null;
  lens_id?: string | null;
  lens_code?: string | null;
}) => {
  try {
    if (!user_id || !wallet_id) {
      return "failed";
    }
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);

    // check if wallet_id is already linked to another user
    const walletQuery = query(
      authBridgeRef,
      where("wallet_id", "==", wallet_id)
    );
    const walletSnapshot = await getDocs(walletQuery);
    console.log({ walletSnapshot });

    for (const doc of walletSnapshot.docs) {
      if (doc.data().user_id !== user_id) {
        return "already bind"; // wallet is linked to another user
      }
    }

    // check if user_id is already linked to another wallet
    const userQuery = query(authBridgeRef, where("user_id", "==", user_id));
    const userSnapshot = await getDocs(userQuery);

    for (const doc of userSnapshot.docs) {
      if (doc.data().wallet_id !== wallet_id) {
        return "already bind"; // user is linked to another wallet
      }
    }

    const updateData: Record<string, string | null> = {};
    if (email) updateData.email = email;
    if (user_id) updateData.user_id = user_id;
    if (wallet_id) updateData.wallet_id = wallet_id;
    if (lens_id) updateData.lens_id = lens_id;
    if (lens_code) updateData.lens_code = lens_code;

    if (walletSnapshot.empty && userSnapshot.empty) {
      // no existing record → create a new one
      await addDoc(authBridgeRef, {
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        ...updateData,
      });
      return "created";
    }

    for (const doc of walletSnapshot.docs.concat(userSnapshot.docs)) {
      const docData = doc.data();
      const shouldUpdate = Object.entries(updateData).some(
        ([key, value]) => docData[key] !== value
      );

      if (shouldUpdate) {
        await updateDoc(doc.ref, {
          updatedAt: Timestamp.now(),
          ...updateData,
        });
        console.log("Document updated successfully.");
        return "updated";
      }
    }

    console.log("No changes needed, skipping update.");
    return "no change";
  } catch (error) {
    console.error("Error updating/inserting document: ", error);
  }
};

export const UpdateAuthBridge_V2 = async ({
  email,
  user_id,
  wallet_id,
  lens_id,
  lens_code,
}: {
  email?: string | null;
  user_id?: string | null;
  wallet_id?: string | null;
  lens_id?: string | null;
  lens_code?: string | null;
}) => {
  try {
    console.log({ email, user_id, wallet_id, lens_id, lens_code });

    // Ensure all fields are present
    if (!email || !user_id || !wallet_id || !lens_id || !lens_code) {
      console.log("Missing required fields");
      return { success: false, message: "Missing required fields" };
    }
    const { db } = await initFirebase();

    const authBridgeCollection = collection(db, AUTH_BRIDGE_COLLECTION);

    // Run both queries in parallel
    const [walletSnap, userIdSnap] = await Promise.all([
      getDocs(query(authBridgeCollection, 
        where("wallet_id", "==", wallet_id),
        where("lens_code", "==", lens_code)
      )),
      getDocs(query(authBridgeCollection, where("user_id", "==", user_id))),
    ]);

    if (!walletSnap.empty) {
      console.log("Wallet + lens_account already exists in auth-bridge");
      return {
        success: false,
        message: "Wallet + lens_account already exists in auth-bridge",
      };
    }

    if (!userIdSnap.empty) {
      console.log("User id already exists in auth-bridge");
      return {
        success: false,
        message: "User id already exists in auth-bridge",
      };
    }

    // Create a new document in auth-bridge
    const newAuthRef = doc(authBridgeCollection, (wallet_id + lens_code)); // Use wallet_id + lens_code as document ID
    const resp = await setDoc(newAuthRef, {
      email,
      user_id,
      wallet_id,
      lens_id,
      lens_code,
      created_at: new Date().toISOString(), // Optional: Add timestamp
    });


    return { success: true, message: "Auth-Bridge updated successfully" };
  } catch (error) {
    console.error("Error updating auth-bridge:", error);
    return { success: false, message: "Server error" };
  }
};

//
// const resp = await getId({id:""});
// if (!resp?.lens_code) // for getting web3 lens_id
// if (!resp?.user_id) // for getting web2 id
//

export const getId = async ({
  id, // user_id or lens_code
}: {
  id: string;
}) => {
  try {
    const { db } = await initFirebase();

    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);

    //   for either `user_id` or `lens_code`
    const q = query(authBridgeRef, where("user_id", "==", id));
    const userSnapshot = await getDocs(q);

    if (!userSnapshot.empty) {
      const docData = userSnapshot.docs[0].data();
      return {
        user_id: docData.user_id || null,
        lens_code: docData.lens_code || null,
        wallet_id: docData.wallet_id || null,
        lens_id: docData.lens_id || null,
      };
    }

    // if no match found for `user_id`, check `lens_code`
    const lensQuery = query(authBridgeRef, where("lens_code", "==", id));
    const lensSnapshot = await getDocs(lensQuery);

    if (!lensSnapshot.empty) {
      const docData = lensSnapshot.docs[0].data();
      return {
        user_id: docData.user_id || null,
        lens_code: docData.lens_code || null,
        wallet_id: docData.wallet_id || null,
        lens_id: docData.lens_id || null,
      };
    }

    return null;
  } catch (error) {
    throw new Error("get_id_failed");
  }
};

// get web2 user details by lens id
export const GetUsersDetailsByLensId = async ({
  lens_code,
}: {
  lens_code: string; // 0x
}) => {
  try {
    const { db } = await initFirebase();
    const authBridgeRef = collection(db, AUTH_BRIDGE_COLLECTION);
    const q = query(authBridgeRef, where("lens_code", "==", lens_code));

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) {
      console.log("No matching document found in auth_bridge.");
      return null;
    }

    const authData = querySnapshot.docs[0].data();
    const user_id = authData.user_id;
    if (!user_id) {
      console.log("No user_id found for the given lens_id.");
      return null;
    }

    const usersRef = collection(db, USERS_COLLECTION);
    const userQuery = query(usersRef, where("id", "==", user_id));
    const userSnapshot = await getDocs(userQuery);

    if (userSnapshot.empty) {
      console.log("No matching user found in users collection.");
      return null;
    }

    return userSnapshot.docs[0].data();
  } catch (error) {
    console.log({ error });
    throw new Error("unable to fetch user deatils by lens id");
  }
};

export const GetWalletId = () => {
  try {
    if (typeof window === "undefined") return null;

    const ls = localStorage || window.localStorage;
    const store = ls.getItem("wagmi.store");
    // console.log({ store });

    const findWalletId = JSON.parse(store ?? "")?.state?.connections
      ?.value?.[0]?.[1]?.accounts?.[0];
    // console.log({ findWalletId });
    const token = ls.getItem("LH_STORAGE_KEY");

    if(!token) { 
      return null;
    }

    return findWalletId;
  } catch (error) {
    console.log({ error });
  }
};



export const AddUserApi = async () => {
  try {
    const db = getFirestore();
    // const usersRef = collection(db,USERS_COLLECTION);

    const querySnapshot = await getDocs(collection(db, USERS_COLLECTION));
    const docs = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
    // console.log({docs});

    return docs;
  } catch (error) {
    console.log({ error });
  }
};

export const GetWeb2user = async () => {
  try {
    if (typeof window === "undefined") return null;

    const ss = localStorage || window.localStorage;
    const store = ss.getItem("user");
    console.log({ store });

    const findUser = JSON.parse(store ?? "")?.state?.connections?.value[0][1]
      ?.accounts?.[0];
    console.log({ findUser });

    if (!findUser) return null;

    return {
      uid: findUser.uid,
      email: findUser.email,
    };
  } catch (error) {
    console.log({ error });
  }
};

export const UpdateOrCreateUser = async (
  user_id: string,
  data: Partial<UserProfile>
) => {
  try {
    if (!user_id) throw new Error("user_id is required");

    const { db } = await initFirebase();

    const userRef = doc(db, USERS_COLLECTION, user_id);

    const docSnap = await getDoc(userRef);

    if (docSnap.exists()) {
      await updateDoc(userRef, {
        ...data,
        updated_at: serverTimestamp(),
      });
    } else {
      await setDoc(userRef, {
        id: user_id,
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
      });
    }

    return "success";
  } catch (error) {
    console.error("Error updating/creating user:", error);
  }
};
