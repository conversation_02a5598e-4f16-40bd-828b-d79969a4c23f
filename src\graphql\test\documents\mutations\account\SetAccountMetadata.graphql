mutation SetAccountMetadata($request: SetAccountMetadataRequest!) {
  setAccountMetadata(request: $request) {
    ... on SetAccountMetadataResponse {
      hash
      __typename
    }
    ... on SelfFundedTransactionRequest {
      ...SelfFundedTransactionRequest
    }
    ... on SponsoredTransactionRequest {
      ...SponsoredTransactionRequest
    }
    ... on TransactionWillFail {
      ...TransactionWillFail
    }
  }
}
