import { NextRequest, NextResponse } from "next/server";
import { getPageBlocks } from "@/lib/notion";

export async function GET(request: NextRequest) {
  try {
    const blocks = await getPageBlocks(process.env.NEXT_PUBLIC_PAYMENT_TERMS_PAGE_ID!);

    return NextResponse.json({
      success: true,
      blocks,
    });
  } catch (error) {
    console.error("Error fetching Notion blocks:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch blocks",
      },
      { status: 500 }
    );
  }
}
