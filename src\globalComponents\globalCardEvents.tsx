import * as React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Clock, Trash2 } from "react-feather";
import { deleteEvent } from "@/services/eventsServices";
import { useState } from "react";

const formatDateWithAMPM = (dateInput: any) => {
  if (!dateInput) return "";

  let date: Date;

  // Handle Firestore Timestamp object
  if (typeof dateInput === "object" && dateInput.seconds) {
    date = new Date(dateInput.seconds * 1000 + (dateInput.nanoseconds || 0) / 1e6);
  } else if (typeof dateInput === "string") {
    date = new Date(dateInput);
  } else if (dateInput instanceof Date) {
    date = dateInput;
  } else {
    return "";
  }

  if (isNaN(date.getTime())) {
    return "";
  }

  // Get day, month, year
  const day = date.getDate();
  const month = date.toLocaleString("default", { month: "long" });
  const year = date.getFullYear();

  // Get hours for AM/PM format
  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";

  // Convert hours to 12-hour format
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'

  // Format the date with AM/PM
  return `${day} ${month} ${year} ${hours}:${minutes} ${ampm}`;
};

export function GlobalCardEvents(props: any) {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const response = await deleteEvent(props?.post?.id);
      if (response.success) {
        // Close modal and refresh the events list
        setShowDeleteModal(false);
        // You might want to add a callback to refresh the parent component
        if (props?.onDelete) props?.onDelete();
      } else {
        alert("Failed to delete event. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      alert("An error occurred while deleting the event.");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <Card className="shadow-none border-0 p-0 w-full min-h-[140px] max-md:w-full relative">
        <CardHeader className="px-0 py-1">
          <CardTitle>
            <div className="row justify-between ">
              <div className="flex flex-row gap-2 items-center">
                <span className="w-4 h-4 rounded-[2px]" style={{ backgroundColor: props?.border }}>
                  <p className=" opacity-0">h</p>
                </span>
                <p className="font-bold text-primary">{props?.post?.name ? props?.post?.name : ""}</p>
              </div>
              {props.isDeleteIcon && 
                <p 
                  className="cursor-pointer text-red-600 z-10 absolute top-0 right-0" 
                  onClick={(e) => {
                    e.stopPropagation(); // Stop event from bubbling up to parent
                    setShowDeleteModal(true);
                  }}
                >
                  <Trash2 />
                </p>
              }
            </div>
            <div className="row justify-between mt-2">
              <div className="row gap-2">
                <Clock size={16} />

                <p className="font-[500]">
                  {props?.post?.date ? formatDateWithAMPM(props?.post?.date) : ""}
                </p>
              </div>
            
            </div>
          </CardTitle>
          <CardDescription className="text-subtitle text-base pl-6  pr-3 line-clamp-3">
            {props?.post?.description ? props?.post?.description : " "}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Delete Event</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this event? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
