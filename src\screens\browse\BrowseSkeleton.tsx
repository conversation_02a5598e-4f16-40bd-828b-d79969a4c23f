"use client";
import React from "react";

interface BrowseSkeletonProps {
  count?: number;
}

const BrowseSkeleton: React.FC<BrowseSkeletonProps> = ({ count = 3 }) => {
  return (
    <div className="w-full space-y-6">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="animate-pulse">
          {/* User Profile Section */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex gap-3">
              {/* Avatar */}
              <div className="w-[40px] h-[40px] rounded-full bg-gray-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
              </div>

              {/* User Info */}
              <div className="flex flex-col gap-1">
                <div className="h-5 bg-gray-200 rounded-md w-32 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded-md w-24 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
                </div>
              </div>
            </div>

            {/* Follow Button */}
            <div className="h-8 bg-gray-200 rounded-md w-20 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
            </div>
          </div>

          {/* Posts Horizontal Scroll Section */}
          <div className="flex gap-2 overflow-hidden">
            {Array.from({ length: 4 }).map((_, postIndex) => (
              <div key={postIndex} className="flex-shrink-0">
                {/* Post Card */}
                <div className="w-[350px] ">
                  {/* Post Image/Video */}
                  <div className="w-full h-full aspect-square  bg-gray-200 rounded-sm border-2 border-gray-300 mb-3 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
                  </div>

                  {/* Post Actions */}
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex gap-4">
                      {/* Like button */}
                      <div className="w-6 h-6 bg-gray-200 rounded"></div>
                      {/* Comment button */}
                      <div className="w-6 h-6 bg-gray-200 rounded"></div>
                      {/* Share button */}
                      <div className="w-6 h-6 bg-gray-200 rounded"></div>
                    </div>
                    {/* More options */}
                    <div className="w-6 h-6 bg-gray-200 rounded"></div>
                  </div>

                  {/* Post Metadata */}
                  <div className="space-y-2">
                    {/* Timestamp */}
                    <div className="h-3 bg-gray-200 rounded-md w-16"></div>

                    {/* Post Content */}
                    <div className="space-y-1">
                      <div className="h-4 bg-gray-200 rounded-md w-full"></div>
                      <div className="h-4 bg-gray-200 rounded-md w-3/4"></div>
                    </div>

                    {/* Comments Preview */}
                    <div className="space-y-1">
                      <div className="flex gap-2">
                        <div className="h-3 bg-gray-200 rounded-md w-16"></div>
                        <div className="h-3 bg-gray-200 rounded-md w-32"></div>
                      </div>
                      <div className="flex gap-2">
                        <div className="h-3 bg-gray-200 rounded-md w-20"></div>
                        <div className="h-3 bg-gray-200 rounded-md w-28"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default BrowseSkeleton;
