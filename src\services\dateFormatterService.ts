// dateFormatterService.ts

/**
 * A service for formatting Firestore timestamp objects into human-readable date strings
 */
export class DateFormatterService {
  /**
   * Formats a Firestore timestamp into "DD Month" format (e.g., "28 March")
   *
   * @param timestamp - Firestore timestamp object with seconds and nanoseconds
   * @returns A formatted date string
   */
  static formatToDateMonth(timestamp: { seconds: number; nanoseconds: number }): string {
    if (!timestamp || !timestamp.seconds) {
      return "";
    }

    const date = new Date(timestamp.seconds * 1000);
    return `${date.getDate()} ${date.toLocaleString("en-US", {
      month: "short",
      year: "numeric",
    })}`;
  }

  /**
   * Formats a Firestore timestamp into "DD Mon" format with shortened month (e.g., "28 Mar")
   *
   * @param timestamp - Firestore timestamp object with seconds and nanoseconds
   * @returns A formatted date string
   */
  static formatToDateShortMonth(timestamp: { seconds: number; nanoseconds: number }): string {
    if (!timestamp || !timestamp.seconds) {
      return "";
    }

    const date = new Date(timestamp.seconds * 1000);
    return `${date.getDate()} ${date.toLocaleString("en-US", {
      month: "short",
    })}`;
  }

  /**
   * Formats a Firestore timestamp into a relative time string (e.g., "2 days ago", "Just now")
   *
   * @param timestamp - Firestore timestamp object with seconds and nanoseconds
   * @returns A formatted relative time string
   */
  static formatToRelativeTime(timestamp: { seconds: number; nanoseconds: number }): string {
    if (!timestamp || !timestamp.seconds) {
      return "";
    }

    const now = new Date();
    const date = new Date(timestamp.seconds * 1000);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return "Just now";
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? "minute" : "minutes"} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? "hour" : "hours"} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} ${diffInDays === 1 ? "day" : "days"} ago`;
    }

    // Fall back to DD Month format for older dates
    return DateFormatterService.formatToDateMonth(timestamp);
  }

  /**
   * Formats a Firestore timestamp to a full date and time (e.g., "28 March 2023, 14:30")
   *
   * @param timestamp - Firestore timestamp object with seconds and nanoseconds
   * @returns A formatted date and time string
   */
  static formatToFullDateTime(timestamp: { seconds: number; nanoseconds: number }): string {
    if (!timestamp || !timestamp.seconds) {
      return "";
    }

    const date = new Date(timestamp.seconds * 1000);
    const day = date.getDate();
    const month = date.toLocaleString("en-US", { month: "long" });
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");

    return `${day} ${month} ${year}, ${hours}:${minutes}`;
  }
}
