"use client";
import { Edit2, X } from "react-feather";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import useAuth from "@/hook";
import { updateUser } from "@/services/usersServices";
const data = [
  "Deephouse style",
  "Musician",
  "Music",
  "Creator",
  "Deephouse",
  "New songs",
  "Music track",
];
const ProfileInfoHashtags = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [profileName, setProfileName]: any = useState("");
  const [location, setLocation]: any = useState("");
  const addHashtag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && e.currentTarget.value.trim()) {
      e.preventDefault();
      setHashtags([...hashtags, e.currentTarget.value.trim()]);
      e.currentTarget.value = ""; // Clear input
    }
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter((hashtag) => hashtag !== tag));
  };
  const auth = useAuth();
  const handleSubmit = async () => {
    if (hashtags) {
      const updatedData = {
        hashtags: hashtags,
      };
      const response = await updateUser(auth?.userData?.uid, updatedData);
      if (response.success) {
        // console.log(response);
        setHashtags([]);
        setIsOpen(false);
      } else {
        // console.error(response.error);
      }
    }
  };
  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">Hashtags</span>
            </div>

            {/* {props.isOtherProfile && (
              <div onClick={() => setIsOpen(true)} className=" cursor-pointer">
                <Edit2 color={props.bgColor} />
              </div>
            )} */}
          </div>
          <div className="mt-5">
            {props.hashtags &&
              props.hashtags.map((item: string, index: number) => {
                return (
                  <Badge
                    variant="secondary"
                    className="mr-2 mb-2 px-[4.9rem] max-md:px-[1rem] py-[4px] text-[#404040] bg-[#EEEEEE] text-[14px] font-normal"
                  >
                    {item}
                  </Badge>
                );
              })}
          </div>
        </div>
      </div>

      {/* PersonalMotto Modal */}
      <div>
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogTrigger asChild>
            {/* Empty or hidden trigger since we're controlling externally */}
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent
            className="min-w-96 max-w-96 min-h-60 p-6 rounded-[30px]"
            style={{ borderRadius: "20px" }}
          >
            <AlertDialogHeader>
              <AlertDialogDescription>
                <div className="row justify-between">
                  <div onClick={() => setIsOpen(false)} className=" cursor-pointer">
                    <X />
                  </div>
                  <p className="font-bold text-primary">Edit Hashtags</p>
                  <p
                    className={
                      hashtags.length > 0
                        ? "font-bold text-primary cursor-pointer"
                        : "font-bold text-borderColor cursor-not-allowed"
                    }
                    onClick={hashtags && handleSubmit}
                  >
                    Save
                  </p>
                </div>

                <div>
                  {/* <div className="grid w-full items-center gap-1.5 mt-6">
                    <Label
                      htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Enter hashtags*
                    </Label>
                    <Textarea
                      placeholder="Enter hashtags here "
                      className="resize-none h-40 outline-none text-lg text-primary"
                      //   {...field}
                    />
                  </div> */}
                  {/* Languages */}
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Enter hashtags*
                    </Label>
                    <div className="border-2 rounded-lg p-3 ">
                      <div className="flex flex-wrap gap-2 mb-12">
                        {hashtags.map((tag, index) => (
                          <span
                            key={index}
                            className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeHashtag(tag)}
                              className="text-primary pt-[2px] pl-2"
                            >
                              <X size={15} strokeWidth="3px" />
                            </button>
                          </span>
                        ))}
                      </div>
                      <input
                        type="text"
                        placeholder="Enter hashtags here "
                        className="w-full pt-2 border-none border-gray-300 rounded-md  outline-none"
                        onKeyDown={addHashtag}
                      />
                    </div>
                  </div>
                  <p className="text-sm text-subtitle mt-2">
                    Hashtag is word, which allows users to discover your posts and services.
                  </p>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};

export default ProfileInfoHashtags;
