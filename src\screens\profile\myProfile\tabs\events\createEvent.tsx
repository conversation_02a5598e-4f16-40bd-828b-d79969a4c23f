"use client";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRef, useState, useEffect } from "react";
import { Check, FilePlus, Loader, Plus, Trash, X, Upload } from "react-feather";
import FileUploader from "../services/serviceDetails/fileUploader";
import { updateUser } from "@/services/usersServices";
import { arrayUnion, Timestamp } from "firebase/firestore";
import { createEvent } from "@/services/eventsServices";
import useAuth from "@/hook";
import { Textarea } from "@/components/ui/textarea";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";

const CreateEvent = (props: any) => {
  const auth = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const postsRef = useRef<HTMLDivElement>(null);
  const Category = "Music";

  const [title, setTitle]: any = useState<string | null>(null);
  const [category, setCategory] = useState<string | null>(null);
  const [about, setAbout] = useState("");
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");
  const [mediaFiles, setMediaFiles] = useState<string[]>([]);

  // Validation states
  const [errors, setErrors] = useState<{
    title: string;
    about: string;
    date: string;
    time: string;
  }>({
    title: "",
    about: "",
    date: "",
    time: "",
  });
  const [touched, setTouched] = useState<{
    title: boolean;
    about: boolean;
    date: boolean;
    time: boolean;
  }>({
    title: false,
    about: false,
    date: false,
    time: false,
  });

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === "success") {
      // Wait 1.5 seconds before closing the modal to show success state
      const timer = setTimeout(() => {
        props.setIsOpen(false);
        props.setToggleMain((e: boolean) => !e);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, props]);

  // Validation function
  const validateField = (name: string, value: string) => {
    let errorMessage = "";

    switch (name) {
      case "title":
        if (!value || !value.trim()) {
          errorMessage = "Event name is required";
        }
        // else if (value.trim().length < 3) {
        //   errorMessage = "Event name must be at least 3 characters";
        // }

        // else if (value.trim().length > 50) {
        //   errorMessage = "Event name must be less than 50 characters";
        // }
        break;
      case "about":
        if (!value || !value.trim()) {
          errorMessage = "Description is required";
        }
        // else if (value.trim().length < 10) {
        //   errorMessage = "Description must be at least 10 characters";
        // }
        break;
      case "date":
        if (!value) {
          errorMessage = "Date is required";
        } else {
          const selectedDate = new Date(value);
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          if (selectedDate < today) {
            errorMessage = "Date cannot be in the past";
          }
        }
        break;
      case "time":
        if (!value) {
          errorMessage = "Time is required";
        }
        break;
      default:
        break;
    }

    return errorMessage;
  };

  // Validate all fields
  const validateForm = () => {
    const newErrors = {
      title: validateField("title", title || ""),
      about: validateField("about", about),
      date: validateField("date", date),
      time: validateField("time", time),
    };

    setErrors(newErrors);

    // Form is valid if all error messages are empty
    return Object.values(newErrors).every((error) => error === "");
  };

  const handleSubmit = async () => {
    // Mark all fields as touched to show validation errors
    setTouched({
      title: true,
      about: true,
      date: true,
      time: true,
    });

    // Validate all fields
    const isValid = validateForm();

    if (!isValid) {
      // If form is not valid, don't submit
      return;
    }

    setLoading(true);
    setUploadStatus("uploading");

    const formData = {
      id: auth?.userData?.uid,
      name: title,
      description: about,
      date: dateTime,
      media: mediaFiles,
    };

    try {
      // Call createPost for the single post
      const response = await createEvent(formData);

      // Check if the post was created successfully
      if (response.success) {
        const postId = response.id; // Assuming `response.id` contains the created post's ID
        const userId = auth?.userData?.uid; // Assuming `auth.userData.uid` contains the current user's ID

        if (userId && postId) {
          await updateUser(userId, { events: arrayUnion(postId) });
          // Set success state - modal will close after delay via useEffect
          setUploadStatus("success");
        }
      } else {
        console.error("Error creating post:", response);
        setUploadStatus("idle");
        setLoading(false);
      }
    } catch (error) {
      console.error("Error creating event:", error);
      setUploadStatus("idle");
      setLoading(false);
    }
  };

  // for date and time picker

  const [date, setDate] = useState<string>("");
  const [time, setTime] = useState<string>("");
  const [dateTime, setDateTime] = useState<string>("");

  // Function to update date and time in the required format
  const updateDateTime = (selectedDate: string, selectedTime: string) => {
    if (selectedDate && selectedTime) {
      const [year, month, day] = selectedDate.split("-");
      const [hours, minutes] = selectedTime.split(":");

      // Convert month number to full month name
      const monthName = new Date(parseInt(year), parseInt(month) - 1).toLocaleString("default", {
        month: "long",
      });

      // Format date as "11 June 2023 10:00"
      const formattedDateTime = `${parseInt(day)} ${monthName} ${year} ${hours}:${minutes}`;

      setDateTime(formattedDateTime);
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setDate(value);
    updateDateTime(value, time);

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      date: true,
    }));

    // Validate field
    setErrors((prev) => ({
      ...prev,
      date: validateField("date", value),
    }));
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setTime(value);
    updateDateTime(date, value);

    // Mark field as touched
    setTouched((prev) => ({
      ...prev,
      time: true,
    }));

    // Validate field
    setErrors((prev) => ({
      ...prev,
      time: validateField("time", value),
    }));
  };

  // Function to handle media upload
  const handleMediaUpload = async (files: File[]) => {
    if (!files || files.length === 0 || uploading || loading) return;

    // Disable all actions during upload
    setUploading(true);
    setLoading(true); // Also set loading to true to disable other form elements

    const storage = getStorage();
    const mediaUrls: string[] = [];

    try {
      for (const file of files) {
        // Determine if file is image or video
        const fileType = file.type.startsWith("image/") ? "images" : "videos";

        // Create reference path with proper folder structure
        const filePath = `events/${fileType}/${auth?.userData?.uid}/${Date.now()}_${file.name}`;
        const storageRef = ref(storage, filePath);

        // Upload file to Firebase Storage
        await uploadBytes(storageRef, file);

        // Get download URL
        const downloadUrl = await getDownloadURL(storageRef);
        mediaUrls.push(downloadUrl);
      }

      // Update media files state
      setMediaFiles(prev => [...prev, ...mediaUrls]);
    } catch (error) {
      console.error("Error uploading media:", error);
      // Show error message to user
      alert("Failed to upload media. Please try again.");
    } finally {
      setUploading(false);
      setLoading(false); // Re-enable form elements
    }
  };

  // Remove media from list
  const handleRemoveMedia = (mediaUrl: string) => {
    // Don't allow removing media during upload or when form is loading
    if (uploading || loading) return;

    setMediaFiles(prev => prev.filter(url => url !== mediaUrl));
  };

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === "success") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">Event Created Successfully!</h2>
          <p className="text-gray-500">
            Your event has been created and will be visible on your profile.
          </p>
        </div>
      );
    }

    if (uploadStatus === "uploading") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Creating Your Event...</h2>
          <p className="text-gray-500">
            Please wait while we create your event. This may take a moment.
          </p>
        </div>
      );
    }

    return (
      <>
        <div className="flex flex-row justify-between items-center mb-4">
          <div
            onClick={() => !loading && props.setIsOpen(false)}
            className={loading ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
          >
            <X />
          </div>
          <h2 className="text-xl font-bold">Create Events</h2>
          <p
            onClick={() => {
              if (loading) return;

              // Mark all fields as touched to show validation errors
              setTouched({
                title: true,
                about: true,
                date: true,
                time: true,
              });

              // Only submit if all fields are valid
              if (
                title &&
                about &&
                date &&
                time &&
                !errors.title &&
                !errors.about &&
                !errors.date &&
                !errors.time
              ) {
                handleSubmit();
              }
            }}
            className={
              !loading &&
                title &&
                about &&
                date &&
                time &&
                !errors.title &&
                !errors.about &&
                !errors.date &&
                !errors.time
                ? "font-bold text-primary cursor-pointer"
                : "font-bold text-borderColor cursor-not-allowed"
            }
          >
            Save
          </p>
        </div>
        <div className=" bg-gray-50">
          <div className=" bg-white  rounded-md p-3 py-4">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (!loading) handleSubmit();
              }}
              className="space-y-6"
            >
              {/* Choose Category */}
              <div>
                <p className="text-primary mb-2 font-[600] text-start">Choose category</p>
                <div className="flex space-x-2">
                  {props.category.map((cat: any, index: any) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => !loading && setCategory(cat)}
                      className={`px-4 py-2 rounded-md ${category === cat ? "bg-primary text-white" : "bg-[#EEEEEE]  text-primary"
                        } ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
                      disabled={loading}
                    >
                      {cat}
                    </button>
                  ))}
                </div>
              </div>

              {/* About Project */}

              <div className="grid w-full md:max-full items-center gap-1.5 mt-6 max-md:text-start">
                <Label htmlFor="email" className="text-primary mb-2 font-[600] block text-start">
                  Event name
                </Label>
                <Input
                  type="text"
                  id="email"
                  name="title"
                  placeholder="Event name"
                  className={`text-primary h-10 ${touched.title && errors.title ? "border-red-500" : ""
                    }`}
                  value={title}
                  onChange={(e) => {
                    if (!loading) {
                      setTitle(e.target.value);
                      setTouched((prev) => ({ ...prev, title: true }));
                      setErrors((prev) => ({
                        ...prev,
                        title: validateField("title", e.target.value),
                      }));
                    }
                  }}
                  disabled={loading}
                />
                {touched.title && errors.title && (
                  <p className="text-red-500 text-xs mt-1">{errors.title}</p>
                )}
              </div>

              <div>
                <Label htmlFor="about" className="text-primary mb-2 font-[600] block text-start">
                  Description
                </Label>
                <Textarea
                  id="about"
                  name="about"
                  value={about}
                  onChange={(e) => {
                    if (!loading) {
                      setAbout(e.target.value);
                      setTouched((prev) => ({ ...prev, about: true }));
                      setErrors((prev) => ({
                        ...prev,
                        about: validateField("about", e.target.value),
                      }));
                    }
                  }}
                  placeholder="Tell the world about your event"
                  className={`w-full p-2 border rounded-md ${touched.about && errors.about ? "border-red-500" : "border-gray-300"
                    }`}
                  disabled={loading}
                />
                {touched.about && errors.about && (
                  <p className="text-red-500 text-xs mt-1">{errors.about}</p>
                )}
                {/* <p className="text-sm text-gray-500"></p> */}
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="event-datetime"
                  className="text-primary mb-2 font-[600] block text-start"
                >
                  Event Date & Time
                </Label>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Date Selection */}
                  <div className="space-y-2 w-full">
                    <label htmlFor="event-date" className="text-sm text-gray-600 font-medium">
                      Date
                    </label>
                    <div className="relative w-full">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="date"
                        id="event-date"
                        value={date}
                        onChange={handleDateChange}
                        className={`pl-10 h-[47px] w-full border p-2.5 rounded-md shadow-sm focus:ring-primary focus:border-primary appearance-none ${loading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                          } ${touched.date && errors.date ? "border-red-500" : "border-gray-300"}`}
                        disabled={loading}
                        min={new Date().toISOString().split("T")[0]} // Prevent selecting past dates
                        style={{ WebkitAppearance: 'none', MozAppearance: 'none' }}
                      />
                    </div>
                    {touched.date && errors.date ? (
                      <p className="text-red-500 text-xs mt-1">{errors.date}</p>
                    ) : !date ? (
                      <p className="text-xs text-gray-500">Select the event date</p>
                    ) : null}
                  </div>

                  {/* Time Selection */}
                  <div className="space-y-2 w-full">
                    <label htmlFor="event-time" className="text-sm text-gray-600 font-medium">
                      Time
                    </label>
                    <div className="relative w-full">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none min-h-[50px]">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <input
                        type="time"
                        id="event-time"
                        value={time}
                        onChange={handleTimeChange}
                        className={`pl-10 h-[47px] w-full border p-2.5 rounded-md shadow-sm focus:ring-primary focus:border-primary appearance-none ${loading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                          } ${touched.time && errors.time ? "border-red-500" : "border-gray-300"}`}
                        disabled={loading}
                        style={{ WebkitAppearance: 'none', MozAppearance: 'none' }}
                      />
                    </div>
                    {touched.time && errors.time ? (
                      <p className="text-red-500 text-xs mt-1">{errors.time}</p>
                    ) : !time ? (
                      <p className="text-xs text-gray-500">Select the event time</p>
                    ) : null}
                  </div>
                </div>

                {/* Preview of formatted date and time */}
                {date && time && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200">
                    <p className="text-sm font-medium text-gray-700">Event scheduled for:</p>
                    <p className="text-primary font-semibold">{dateTime}</p>
                  </div>
                )}
              </div>

              <div>
                <div className="mt-4">
                  <p className="text-primary font-[600]">
                    Media Files
                  </p>

                  {/* Custom media uploader */}
                  <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center mb-4 relative">
                    <input
                      type="file"
                      id="media-upload"
                      multiple
                      accept="image/*,video/*"
                      className="hidden"
                      disabled={uploading || loading}
                      onChange={(e) => {
                        if (
                          e.target.files &&
                          e.target.files.length > 0 &&
                          !uploading &&
                          !loading
                        ) {
                          handleMediaUpload(Array.from(e.target.files));
                        }
                      }}
                    />
                    <label
                      htmlFor="media-upload"
                      className={`block ${uploading
                          ? "cursor-not-allowed opacity-50"
                          : "cursor-pointer"
                        }`}
                    >
                      {uploading ? (
                        <Loader className="mx-auto h-8 w-8 text-primary animate-spin" />
                      ) : (
                        <Upload className="mx-auto h-8 w-8 text-gray-400" />
                      )}
                      <p className="mt-2 text-sm text-gray-500">
                        {uploading
                          ? "Uploading media..."
                          : "Click to upload images or videos"}
                      </p>
                      {!uploading && (
                        <p className="mt-1 text-xs text-gray-400">
                          Files will be stored in their respective folders
                        </p>
                      )}
                    </label>
                    {uploading && (
                      <div className="mt-2 flex items-center justify-center">
                        <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-primary rounded-full animate-pulse"
                            style={{ width: "100%" }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Display uploaded media */}
                  {mediaFiles.length > 0 && (
                    <div className="grid grid-cols-3 gap-3 mt-3">
                      {mediaFiles.map((mediaUrl, mediaIndex) => {
                        const isVideo = mediaUrl.includes("videos");
                        return (
                          <div
                            key={mediaIndex}
                            className="relative h-24 bg-gray-100 rounded-md overflow-hidden"
                          >
                            {isVideo ? (
                              <div className="w-full h-full flex items-center justify-center bg-black">
                                <video
                                  src={mediaUrl}
                                  className="max-h-full max-w-full"
                                />
                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                                  <span className="text-white text-xs px-2 py-1 bg-black bg-opacity-50 rounded">
                                    Video
                                  </span>
                                </div>
                              </div>
                            ) : (
                              <img
                                src={mediaUrl}
                                alt={`Media ${mediaIndex}`}
                                className="w-full h-full object-cover"
                              />
                            )}
                            <button
                              type="button"
                              onClick={() =>
                                !uploading &&
                                !loading &&
                                handleRemoveMedia(mediaUrl)
                              }
                              className={`absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 ${uploading || loading
                                  ? "opacity-50 cursor-not-allowed"
                                  : ""
                                }`}
                              disabled={uploading || loading}
                              aria-label="Remove media"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      </>
    );
  };

  return <>{renderContent()}</>;
};

export default CreateEvent;
