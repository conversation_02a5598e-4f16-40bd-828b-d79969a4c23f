import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getEscrowTransactionByOrderId, updateTransaction, releaseEscrowStage } from '@/services/transactionService';
import { getStripeInstanceByCurrency } from '@/lib/stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

export async function POST(request: NextRequest) {
  try {
    const { orderId, stage = 'accept', paymentIntentId } = await request.json();

    if (!orderId) {
      return NextResponse.json({
        success: false,
        error: 'Order ID is required'
      }, { status: 400 });
    }

    console.log('🔄 ===== ESCROW CAPTURE AND RELEASE =====');
    console.log(`📋 Order ID: ${orderId}`);
    console.log(`🎯 Stage: ${stage}`);
    console.log(`💳 Payment Intent ID: ${paymentIntentId}`);
    console.log('🕐 Timestamp:', new Date().toISOString());

    // Step 1: Get escrow transaction
    console.log('🔄 Step 1: Getting escrow transaction...');
    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        success: false,
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;
    console.log('✅ Found escrow transaction:', {
      transactionId: transaction.id,
      amount: transaction.amount,
      currentStage: transaction.currentStage,
      sellerId: transaction.sellerId,
      sellerStripeAccountId: transaction.sellerStripeAccountId
    });

    // Validate seller account
    if (!transaction.sellerStripeAccountId) {
      return NextResponse.json({
        success: false,
        error: 'Seller Stripe account ID not found in transaction'
      }, { status: 400 });
    }

    // Step 2: Get payment intent (use from transaction if not provided)
    const targetPaymentIntentId = paymentIntentId || transaction.stripePaymentIntentId;
    
    if (!targetPaymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID not found'
      }, { status: 400 });
    }

    console.log('🔄 Step 2: Retrieving payment intent...');

    // Use the correct Stripe instance based on transaction currency
    const { stripeInstance, isUS } = getStripeInstanceByCurrency(transaction.currency);
    console.log(`🌍 Using ${isUS ? 'US' : 'International'} Stripe instance for ${transaction.currency.toUpperCase()} currency`);

    const paymentIntent = await stripeInstance.paymentIntents.retrieve(targetPaymentIntentId);
    
    console.log('📊 Payment Intent Status:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      amount_capturable: paymentIntent.amount_capturable,
      capture_method: paymentIntent.capture_method
    });

    // Step 3: Capture payment (user should have already confirmed via checkout)
    let capturedPaymentIntent = paymentIntent;

    if (paymentIntent.status === 'requires_payment_method' || paymentIntent.status === 'requires_confirmation') {
      return NextResponse.json({
        success: false,
        error: 'Payment has not been confirmed by user yet. User must complete payment via checkout URL first.',
        paymentIntentStatus: paymentIntent.status,
        nextStep: 'Send user to checkout URL to complete payment authorization'
      }, { status: 400 });
    }

    if (paymentIntent.status === 'requires_capture') {
      console.log('🔄 Step 3: Capturing authorized payment...');

      capturedPaymentIntent = await stripeInstance.paymentIntents.capture(paymentIntent.id, {
        metadata: {
          orderId,
          escrowStage: stage,
          capturedAt: new Date().toISOString()
        },
        expand: ['charges.data']
      });

      console.log('✅ Payment captured successfully:', {
        id: capturedPaymentIntent.id,
        status: capturedPaymentIntent.status,
        amount_received: capturedPaymentIntent.amount_received
      });
    } else if (paymentIntent.status === 'succeeded') {
      console.log('✅ Payment already captured');
    } else {
      return NextResponse.json({
        success: false,
        error: `Cannot capture payment. Current status: ${paymentIntent.status}`,
        paymentIntentStatus: paymentIntent.status
      }, { status: 400 });
    }

    // Step 4: Get the charge ID for transfer
    console.log('🔄 Step 4: Getting charge ID for transfer...');

    // If no charges in the payment intent, retrieve it again to get updated data
    const expandedPaymentIntent = capturedPaymentIntent as unknown as Stripe.PaymentIntent & { charges: { data: Stripe.Charge[] } };

    if (!expandedPaymentIntent.charges || expandedPaymentIntent.charges.data.length === 0) {
      console.log('No charges found, retrieving payment intent again...');
      const retrievedPaymentIntent = await stripeInstance.paymentIntents.retrieve(capturedPaymentIntent.id, {
        expand: ['charges.data']
      });
      // Update the reference with the newly retrieved payment intent
      Object.assign(capturedPaymentIntent, retrievedPaymentIntent);
    }

    // Re-check after potential retrieval
    const finalExpandedPaymentIntent = capturedPaymentIntent as unknown as Stripe.PaymentIntent & { charges: { data: Stripe.Charge[] } };

    if (!finalExpandedPaymentIntent.charges || finalExpandedPaymentIntent.charges.data.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No charges found for captured payment intent',
        paymentIntentId: capturedPaymentIntent.id,
        paymentIntentStatus: capturedPaymentIntent.status,
        debug: {
          hasCharges: !!finalExpandedPaymentIntent.charges,
          chargesCount: finalExpandedPaymentIntent.charges?.data?.length || 0
        }
      }, { status: 400 });
    }

    const latestCharge = finalExpandedPaymentIntent.charges.data[0];
    const chargeId = latestCharge.id;
    
    console.log('✅ Found charge for transfer:', {
      chargeId,
      amount: latestCharge.amount,
      currency: latestCharge.currency,
      captured: latestCharge.captured
    });

    // Step 5: Calculate stage amount and transfer to seller
    console.log('🔄 Step 5: Calculating stage amount and creating transfer...');
    
    // Find the escrow stage
    const escrowStage = transaction.escrowStages?.find(s => s.stage === stage);
    if (!escrowStage) {
      return NextResponse.json({
        success: false,
        error: `Escrow stage '${stage}' not found`
      }, { status: 400 });
    }

    // For 'accept' stage, transfer 10% to seller
    const stageAmount = escrowStage.amount; // This is already in cents
    
    console.log('💰 Stage calculation:', {
      stage,
      stagePercentage: escrowStage.percentage,
      stageAmount,
      stageAmountInDollars: (stageAmount / 100).toFixed(2)
    });

    // Validate seller account before transfer
    if (!transaction.sellerStripeAccountId) {
      return NextResponse.json({
        success: false,
        error: 'Seller Stripe account ID not found in transaction',
        debug: {
          transactionId: transaction.id,
          sellerId: transaction.sellerId,
          sellerEmail: transaction.sellerEmail
        }
      }, { status: 400 });
    }

    console.log('💳 Creating transfer:', {
      amount: stageAmount,
      currency: latestCharge.currency,
      chargeId,
      destination: transaction.sellerStripeAccountId,
      stage
    });

    // Create transfer to seller
    let transfer;
    try {
      transfer = await stripeInstance.transfers.create({
        amount: stageAmount,
        currency: latestCharge.currency,
        source_transaction: chargeId,
        destination: transaction.sellerStripeAccountId,
        metadata: {
          orderId,
          stage,
          transactionId: transaction.id,
          sellerId: transaction.sellerId!,
          escrowStage: stage,
          transferredAt: new Date().toISOString(),
          captureAndRelease: 'true'
        }
      });
    } catch (transferError: any) {
      console.error('❌ Transfer creation failed:', {
        error: transferError.message,
        code: transferError.code,
        type: transferError.type,
        destination: transaction.sellerStripeAccountId,
        amount: stageAmount,
        chargeId
      });

      return NextResponse.json({
        success: false,
        error: 'Failed to create transfer to seller',
        details: transferError.message,
        stripeError: {
          code: transferError.code,
          type: transferError.type
        },
        debug: {
          destination: transaction.sellerStripeAccountId,
          amount: stageAmount,
          chargeId,
          stage
        }
      }, { status: 400 });
    }

    console.log('✅ Transfer created successfully:', {
      transferId: transfer.id,
      amount: transfer.amount,
      amountInDollars: (transfer.amount / 100).toFixed(2),
      destination: transfer.destination,
      currency: transfer.currency,
      status: (transfer as any).status
    });

    // Step 6: Update escrow stage status
    console.log('🔄 Step 6: Updating escrow stage status...');
    
    const releaseResult = await releaseEscrowStage(
      transaction.id,
      stage,
      transfer.id
    );

    if (!releaseResult.success) {
      console.error('Failed to update escrow stage status:', releaseResult.error);
      // Don't fail the entire operation, just log the error
    }

    // Step 7: Update transaction status
    await updateTransaction(transaction.id, {
      currentStage: stage === 'accept' ? 'accepted' : stage,
      stripePaymentIntentId: capturedPaymentIntent.id,
      metadata: {
        ...transaction.metadata,
        chargeId,
        lastCaptureAt: new Date().toISOString(),
        lastTransferAt: new Date().toISOString(),
        [`${stage}TransferId`]: transfer.id
      }
    });

    return NextResponse.json({
      success: true,
      message: `Successfully captured payment and released ${stage} stage`,
      orderId,
      stage,
      paymentIntentId: capturedPaymentIntent.id,
      chargeId,
      transferId: transfer.id,
      stageAmount,
      stageAmountInDollars: (stageAmount / 100).toFixed(2),
      currency: latestCharge.currency,
      paymentIntentStatus: capturedPaymentIntent.status,
      transferAmount: transfer.amount,
      sellerAccountId: transaction.sellerStripeAccountId,
      captureAndReleaseComplete: true
    });

  } catch (error) {
    console.error('Escrow capture and release error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to capture payment and release escrow stage',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
