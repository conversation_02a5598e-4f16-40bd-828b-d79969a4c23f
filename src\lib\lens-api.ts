import { LENS_API_URL } from "./constant";

const LENS_API = LENS_API_URL;

export async function lensRequest(query: string, variables: any = {}) {
  const token = localStorage.getItem("lens-auth-token");

  const response = await fetch(LENS_API, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
      "x-access-token": `Bearer ${token}`,
    },
    body: JSON.stringify({
      query,
      variables,
    }),
  });

  const json = await response.json();

  if (json.errors) {
    // console.error('Lens API Error:', json.errors);
    throw new Error(json.errors[0].message);
  }

  return json.data;
}
