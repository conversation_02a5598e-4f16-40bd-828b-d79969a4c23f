"use client";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@radix-ui/react-dropdown-menu";
import { useState } from "react";
import { DollarSign, Info, Trash } from "react-feather";
import FileUploader from "./fileUploader";

const EditCustomizationService = ({ onSelectService }: any) => {
  const [isTrue, setIsTrue] = useState(false);
  return (
    <>
      <div>
        <div className="bg-green-700">
          {/* header */}
          <div className=" w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div
                onClick={() => onSelectService(1)}
                className=" cursor-pointer"
              >
                <p>Back</p>
              </div>
              <p className="text-titleLabel text-lg font-bold">
                Edit Customizations
              </p>
              <p>Save</p>
            </div>
          </div>
          <div className="w-full overflow-y-scroll overflow-x-hidden gap-3 hide-scroll-custom  bg-white h-[calc(100vh-300px)] pt-4 pb-16 px-3">
            {Array.from({ length: 2 }).map((_, indexs) => (
              <div className="mt-4">
                <div className="row justify-between">
                  <p className="text-primary font-[600] text-xl">
                    Customization # {indexs + 1}
                  </p>
                  <Trash />
                </div>

                <div className="mt-3">
                  <p className="text-primary font-[600]">Choose category</p>
                  <div className="row gap-3">
                    <Button className="text-white">Music</Button>
                    <Button className=" bg-[#F2F2F2] text-primary shadow-none hover:text-white">
                      Storytelling
                    </Button>
                  </div>
                </div>

                <div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Customization title
                    </Label>
                    <Input
                      placeholder="Customization title"
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                    />
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Description of the customization
                    </Label>
                    <Textarea
                      placeholder="Description of the customization"
                      className="resize-none h-40 outline-none text-lg text-primary"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-x-4 ">
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel1"
                    >
                      Service Cost
                    </Label>
                    <p className="text-titleLabel1">(you will receive)</p>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>$</p>
                    </div>
                    <p>
                      By Seller. VAT inclusive.
                      {"                                   "}
                    </p>
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel1"
                    >
                      List Price
                    </Label>
                    <p className="text-titleLabel1">(you will receive)</p>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>$</p>
                    </div>
                    <p className="line-clamp-2">
                      **Includes AMUZ App Fee (16% of List Price)
                    </p>
                  </div>

                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel1"
                    >
                      Time
                    </Label>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>hours</p>
                    </div>
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel1"
                    >
                      Time
                    </Label>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>days</p>
                    </div>
                  </div>
                </div>

                <p className="mt-3">8 hours = 1 day</p>
                <p className="mt-3">
                  The service proposed deivery date shall not exceed 2 weeks
                  (112hours).
                </p>
                <p className="mt-3">
                  The service delivery due date, based on above hour, will
                  appear in ‘My Order’ page detailsafter order obtaining
                  ACCEPTED status.
                </p>

                <div>
                  <p className=" text-primary font-[600] mt-2">Add media</p>

                  {/* <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="picture">Picture</Label>
                    <Input id="picture" type="file" />
                  </div> */}
                  <FileUploader />
                </div>

                <div className="row justify-center mt-5">
                  <Badge className=" btn-xs w-full py-4 border-primary btn text-white">
                    Edit Customizations (3)
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default EditCustomizationService;
