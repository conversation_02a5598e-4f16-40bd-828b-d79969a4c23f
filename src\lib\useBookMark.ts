import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import {
  useBookmarkPostMutation,
  useUndoBookmarkPostMutation,
} from "@/graphql/test/generated";
import { useAccount } from "wagmi";

export function useBookMark() {
  const { mutateAsync: addbookmark } = useBookmarkPostMutation();
  const { mutateAsync: undobookmark } = useUndoBookmarkPostMutation();

  const { address ,isConnected } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();

  async function reaction(on: string, bookmark: boolean) {
    try {
      if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      if (bookmark) {
        const typedData = await addbookmark({
          request: {
            post: on,
          },
        });
        return typedData;
      } else {
        const typedData = await undobookmark({
          request: {
            post: on,
          },
        });
        return typedData.undoBookmarkPost;
      }
    } catch (error) {
      // console.error("Follow error:", error);
      throw error;
    }
  }

  return reaction;
}
