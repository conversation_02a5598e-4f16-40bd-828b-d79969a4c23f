import {
  SideSheetContainer,
  SideSheetHeader,
  SideSheetDescription,
} from "@/components/ui/sidebarSheet";
import ActiveChat from "./activeChat";

export function ChatBox({
  open,
  onOpenChange,
  chatId,
  chatName,
  chatImg,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  chatId: any;
  chatName: any;
  chatImg: any;
}) {
  return (
    <SideSheetContainer
      className="max-md:left-0 left-[48.25rem] max-lg:left-[26.25rem] max-w-[26.25rem]"
      open={open}
      onOpenChange={(isOpen) => {
        // Prevent closing the sheet unless explicitly triggered
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Header */}
      <SideSheetHeader className="px-8 xs-px-2">
        <div
          className="cursor-pointer text-base font-medium text-black row gap-2"
          // Close only the ChatBox when Back button is clicked
          onClick={() => onOpenChange(false)}
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>

        <p className="text-base font-bold text-titleLabel">{chatName}</p>
        <div>
          <img
            src={chatImg}
            alt=""
            className="w-[30px] h-[30px] min-w-[30px] min-h-[30px] rounded-full object-cover"
            style={{
              border: "2px solid",
            }}
          />
        </div>
      </SideSheetHeader>

      {/* Tabs */}

      <SideSheetDescription>
        {/* <p>Chat Box</p> */}
        <ActiveChat />
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
