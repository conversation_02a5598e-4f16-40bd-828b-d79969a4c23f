'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import CustomCheckout from '../../components/CustomCheckout'

export default function CheckoutPage() {
  const searchParams = useSearchParams()
  const [checkoutData, setCheckoutData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    // Get checkout parameters from URL or localStorage
    const urlParams = {
      userId: searchParams.get('userId'),
      userEmail: searchParams.get('userEmail'),
      amount: parseInt(searchParams.get('amount')) || 2000,
      currency: searchParams.get('currency') || 'usd',
      productName: searchParams.get('productName') || 'Demo Product',
      productDescription: searchParams.get('productDescription') || '',
      isEscrow: searchParams.get('isEscrow') === 'true',
      sellerId: searchParams.get('sellerId'),
      sellerEmail: searchParams.get('sellerEmail'),
      sellerStripeAccountId: searchParams.get('sellerStripeAccountId'),
      orderId: searchParams.get('orderId'),
    }

    // Try to get data from localStorage if not in URL
    const storedCheckoutData = localStorage.getItem('checkoutData')
    let checkoutParams = urlParams

    if (storedCheckoutData && !urlParams.userId) {
      try {
        const parsed = JSON.parse(storedCheckoutData)
        checkoutParams = { ...parsed, ...urlParams }
        // Clear stored data after use
        localStorage.removeItem('checkoutData')
      } catch (err) {
        console.warn('Failed to parse stored checkout data:', err)
      }
    }

    // Validate required fields
    if (!checkoutParams.userId || !checkoutParams.userEmail) {
      setError('Missing required user information. Please try again.')
      setLoading(false)
      return
    }

    setCheckoutData(checkoutParams)
    setLoading(false)
  }, [searchParams])

  const handleSuccess = (result) => {
    console.log('Payment successful:', result)
    // The EmbeddedCheckout will automatically redirect to the return_url
  }

  const handleError = (error) => {
    console.error('Payment error:', error)
    setError(error.message)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50 p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="mb-4">
            <svg
              className="mx-auto h-16 w-16 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-red-600 mb-2">Checkout Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  if (!checkoutData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No checkout data available</p>
        </div>
      </div>
    )
  }

  return (
    <CustomCheckout
      userId={checkoutData.userId}
      userEmail={checkoutData.userEmail}
      amount={checkoutData.amount}
      currency={checkoutData.currency}
      productName={checkoutData.productName}
      productDescription={checkoutData.productDescription}
      isEscrow={checkoutData.isEscrow}
      sellerId={checkoutData.sellerId}
      sellerEmail={checkoutData.sellerEmail}
      sellerStripeAccountId={checkoutData.sellerStripeAccountId}
      orderId={checkoutData.orderId}
      onSuccess={handleSuccess}
      onError={handleError}
    />
  )
}
