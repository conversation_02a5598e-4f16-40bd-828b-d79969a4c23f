"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@radix-ui/react-dropdown-menu";
import { useState, useEffect } from "react";
import { ChevronLeft, Trash, Plus, X, Check } from "react-feather";
import { Image, Video, Loader } from "lucide-react";
import {
  CustomizationInput,
  deleteCustomization,
  formatDuration,
  getServiceById,
  updateCustomizations,
} from "@/services/serviceService";
import EmptyState from "@/components/EmptyState";
import { getCurrencySymbol, initializeCurrency } from "@/services/currencyService";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";

interface EditCustomizationServiceProps {
  onSelectService: any;
  serviceId: string;
  customizations: any[];
  currencySymbol?: string;
  serviceCurrency?: string;
  categories?: string[];
}

const EditCustomizationService = ({
  onSelectService,
  serviceId,
  customizations,
  currencySymbol: propCurrencySymbol,
  serviceCurrency: propServiceCurrency,
  categories = ["Music", "Storytelling"],
}: EditCustomizationServiceProps) => {
  const [services, setServices] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");
  const [deleteStatus, setDeleteStatus] = useState<"idle" | "deleting" | "success">("idle");
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [customizationToDelete, setCustomizationToDelete] = useState<string | null>(null);
  const [currencySymbol, setCurrencySymbol] = useState(propCurrencySymbol || "£");
  // Keep track of the parent service's currency for consistency
  const [serviceCurrency, setServiceCurrency] = useState(
    propServiceCurrency || initializeCurrency()
  );

  // Array to hold all customizations
  const [customizationList, setCustomizationList] = useState<any[]>([]);

  // Media upload states
  const [uploading, setUploading] = useState(false);
  const [currentUploadingItem, setCurrentUploadingItem] = useState<string | null>(null);

  // console.log("Initial customizations:", customizations);

  // Set initial loading state
  useEffect(() => {
    // Start with loading state
    setIsLoadingData(true);
  }, []);

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === "success") {
      // Wait 1.5 seconds before navigating back
      const timer = setTimeout(() => {
        onSelectService();
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, onSelectService]);

  // Fetch service details and set up state
  useEffect(() => {
    const fetchAllServices = async (id: string) => {
      try {
        const response = await getServiceById(id);
        // console.log("Service response:", response);

        if (response?.success && response?.service) {
          // Type casting to make TypeScript happy
          const serviceData = response.service as unknown as any;
          setServices(serviceData);

          // Get currency symbol
          const servicesCurrency =
            serviceData?.currency || propServiceCurrency || initializeCurrency();
          setServiceCurrency(servicesCurrency);

          // Only update the currency symbol if propCurrencySymbol wasn't provided
          if (!propCurrencySymbol) {
            const symbol = getCurrencySymbol(servicesCurrency);
            setCurrencySymbol(symbol);
          }

          // If there are existing customizations, populate them
          if (serviceData?.customizations_array && serviceData.customizations_array.length > 0) {
            // Ensure each customization has a unique ID
            const populatedCustomizations = serviceData.customizations_array.map(
              (customization: any, index: number) => ({
                id: customization.id || `custom-${Date.now()}-${index}`,
                title: customization.title || "",
                description: customization.description || "",
                serviceCost: customization.price || "0",
                listPrice: customization.price
                  ? (Number(customization.price) / (1 - 0.16)).toFixed(2)
                  : "0",
                timeHours: customization.duration
                  ? (Number(customization.duration) % 24).toString()
                  : "0",
                timeDays: customization.duration
                  ? Math.floor(Number(customization.duration) / 24).toString()
                  : "0",
                category: customization.category || serviceData?.category || "Music",
                currency: customization.currency || servicesCurrency,
                media: customization.media || [],
              })
            );

            // console.log("Populated customizations:", populatedCustomizations);
            setCustomizationList(populatedCustomizations);
          } else if (customizations && customizations.length > 0) {
            // Use passed customizations if available
            const formattedCustomizations = customizations.map((customization, index) => ({
              id: customization.id || `custom-${Date.now()}-${index}`,
              title: customization.title || "",
              description: customization.description || "",
              serviceCost: customization.price || "0",
              listPrice: customization.price
                ? (Number(customization.price) / (1 - 0.16)).toFixed(2)
                : "0",
              timeHours: customization.duration || "0",
              timeDays: "",
              category: customization.category || serviceData?.category || "Music",
              currency: customization.currency || servicesCurrency,
              media: customization.media || [],
            }));

            // console.log("Using passed customizations:", formattedCustomizations);
            setCustomizationList(formattedCustomizations);
          } else {
            // Don't create a default customization, leave the list empty
            setCustomizationList([]);
          }
        }
      } catch (error) {
        console.error("Error fetching service:", error);
        // Don't create a default customization, leave the list empty
        setCustomizationList([]);
      } finally {
        setIsLoadingData(false);
      }
    };

    if (serviceId) {
      fetchAllServices(serviceId);
    } else {
      // Don't create a default customization, leave the list empty
      setCustomizationList([]);
      setIsLoadingData(false);
    }
  }, [serviceId, propCurrencySymbol, propServiceCurrency]);

  // Function to add a new customization
  const handleAddCustomization = () => {
    // Get the category from the service or from the first existing customization
    const serviceCategory = services?.category;
    const firstCustomizationCategory =
      customizationList.length > 0 ? customizationList[0].category : null;

    const newCustomization = {
      id: `custom-${Date.now()}`,
      title: "New Customization",
      description: "",
      serviceCost: "0",
      listPrice: "0",
      timeHours: "0",
      timeDays: "0",
      // Use the service's category or the first customization's category to ensure consistency
      category:
        serviceCategory ||
        firstCustomizationCategory ||
        (categories.length > 0 ? categories[0] : "Music"),
      currency: serviceCurrency,
      media: [],
    };

    setCustomizationList((prevList) => [...prevList, newCustomization]);
  };

  // Function to show delete confirmation modal
  const showDeleteConfirmation = (customId: string) => {
    setCustomizationToDelete(customId);
    setDeleteModalOpen(true);
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setCustomizationToDelete(null);
    setDeleteModalOpen(false);
  };

  // Function to remove a customization
  const handleDeleteCustomization = async () => {
    if (!customizationToDelete) return;

    if (customizationList.length <= 1) {
      alert("You must have at least one customization option");
      cancelDelete();
      return;
    }

    setDeleteStatus("deleting");

    try {
      const response = await deleteCustomization(serviceId, customizationToDelete);

      if (response.success) {
        setDeleteStatus("success");
        // Success state will be handled by the Close button
      } else {
        setDeleteStatus("idle");
        alert("Failed to delete customization. Please try again.");
        cancelDelete();
      }
    } catch (error) {
      console.error("Error deleting customization:", error);
      setDeleteStatus("idle");
      alert("Something went wrong while deleting. Please try again.");
      cancelDelete();
    }
  };

  // Function to update a single customization when any field changes
  const handleUpdateCustomization = (customId: string, field: string, value: string) => {
    setCustomizationList((prevList) =>
      prevList.map((item) => {
        if (item.id === customId) {
          // Special handling for price fields to auto-calculate
          if (field === "serviceCost") {
            const serviceCost = value || "0";
            const listPrice = parseFloat((Number(serviceCost) / (1 - 0.16)).toFixed(2)).toString();
            return { ...item, serviceCost, listPrice };
          } else if (field === "listPrice") {
            const listPrice = value || "0";
            const serviceCost = parseFloat((Number(listPrice) * (1 - 0.16)).toFixed(2)).toString();
            return { ...item, listPrice, serviceCost };
          }
          return { ...item, [field]: value };
        }
        return item;
      })
    );
  };

  // Categories cannot be changed in customizations

  // Function to save all customizations
  const handleSaveCustomizations = async () => {
    // Format the customizations for the API
    const formattedCustomizations: CustomizationInput[] = customizationList.map((item) => ({
      id: item.id,
      title: item.title,
      description: item.description,
      price: item.serviceCost,
      duration: String((Number(item.timeHours) || 0) + (Number(item.timeDays) || 0) * 24),
      category: item.category,
      media: item.media,
      // currency: serviceCurrency, // Use the consistent service currency
    }));

    // console.log("Saving customizations:", formattedCustomizations);
    setLoading(true);
    setUploadStatus("uploading");

    try {
      // const response = await updateService(serviceId, serviceToUpdate);
      const resp = await updateCustomizations(serviceId, formattedCustomizations);

      if (resp.success) {
        setUploadStatus("success");
        // Success state will handle navigation after delay
      } else {
        setUploadStatus("idle");
        alert("Failed to save customizations. Please try again.");
      }
    } catch (error) {
      console.error("Error saving customizations:", error);
      setUploadStatus("idle");
      alert("Failed to save customizations. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Function to handle media upload
  const handleMediaUpload = async (customizationId: string, files: any) => {
    if (!files || files.length === 0) return;

    setUploading(true);
    setCurrentUploadingItem(customizationId);
    const storage = getStorage();
    const mediaUrls: any = [];

    try {
      for (const file of files) {
        // Determine if file is image or video
        const fileType = file.type.startsWith("image/") ? "images" : "videos";

        // Create reference path with proper folder structure
        const filePath = `services/${fileType}/${serviceId}/${customizationId}/${Date.now()}_${
          file.name
        }`;
        const storageRef = ref(storage, filePath);

        // Upload file to Firebase Storage
        await uploadBytes(storageRef, file);

        // Get download URL
        const downloadUrl = await getDownloadURL(storageRef);
        mediaUrls.push(downloadUrl);
      }

      // Update customization with new media URLs
      setCustomizationList(
        customizationList.map((item) => {
          if (item.id === customizationId) {
            return {
              ...item,
              media: [...(item.media || []), ...mediaUrls],
            };
          }
          return item;
        })
      );
    } catch (error) {
      console.error("Error uploading media:", error);
    } finally {
      setUploading(false);
      setCurrentUploadingItem(null);
    }
  };

  // Remove media from customization
  const handleRemoveMedia = (customizationId: string, mediaUrl: any) => {
    setCustomizationList(
      customizationList.map((item) => {
        if (item.id === customizationId) {
          return {
            ...item,
            media: (item.media || []).filter((media: any) =>
              typeof media === "string" ? media !== mediaUrl : media.url !== mediaUrl.url
            ),
          };
        }
        return item;
      })
    );
  };

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === "success") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2 text-center">
            Customizations Updated Successfully!
          </h2>
          <p className="text-gray-500 text-center">
            Your customizations have been updated and the changes will be visible on your service
            details.
          </p>
        </div>
      );
    }

    if (uploadStatus === "uploading") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2 text-center">Updating Customizations...</h2>
          <p className="text-gray-500 text-center">
            Please wait while we update your customizations. This may take a moment.
          </p>
        </div>
      );
    }

    if (isLoadingData) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2 text-center">Loading Customizations...</h2>
          <p className="text-gray-500 text-center">
            Please wait while we load your customization information.
          </p>
        </div>
      );
    }

    if (customizationList.length === 0) {
      return (
        <>
          <EmptyState
            type="customizations"
            title="No Customizations Yet"
            message="Add customizations to give your customers options to enhance your service."
            isOwnProfile={true}
            actionLabel="Add Customization"
            onAction={handleAddCustomization}
          />

          <div
            className="fixed bottom-6 right-10  z-[999999] p-3 rounded-full bg-primary text-white font-bold cursor-pointer bg-color-indicator-plus shadow-lg"
            onClick={handleAddCustomization}
          >
            <Plus className="w-10 h-10" style={{ strokeWidth: "2.5px" }} />
          </div>
        </>
      );
    }

    return (
      <>
        {customizationList.map((item: any, index: number) => (
          <div className="mt-4 border-b pb-6" key={item.id}>
            <div className="row justify-between">
              <p className="text-primary font-[600]">Customization #{index + 1}</p>
              <Trash
                className="cursor-pointer text-red-500"
                onClick={() => showDeleteConfirmation(item.id)}
              />
            </div>

            <div className="mt-3">
              <p className="text-primary font-[600]">Category</p>
              <div className="row gap-3 items-center">
                <Button key={item.category} className="text-white cursor-default" disabled>
                  {item.category}
                </Button>
                <p className="text-xs text-gray-500 italic ml-2">(Category cannot be changed)</p>
              </div>
            </div>

            <div>
              <div className="grid w-full items-center gap-1.5 mt-3">
                <Label className="text-base font-[600] text-titleLabel">Customization title</Label>
                <Input
                  placeholder="Customization title"
                  className="resize-none h-[40px] outline-none text-lg text-primary"
                  value={item.title}
                  onChange={(e) => handleUpdateCustomization(item.id, "title", e.target.value)}
                />
              </div>
              <div className="grid w-full items-center gap-1.5 mt-3">
                <Label className="text-base font-[600] text-titleLabel">
                  Description of the customization
                </Label>
                <Textarea
                  placeholder="Description of the customization"
                  className="resize-none h-40 outline-none text-lg text-primary"
                  value={item.description}
                  onChange={(e) =>
                    handleUpdateCustomization(item.id, "description", e.target.value)
                  }
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-x-4">
              <div className="grid w-full items-center gap-1.5 mt-3">
                <Label className="text-base font-[600] text-titleLabel1">Service Cost</Label>
                <p className="text-titleLabel items-center text-xs -mt-3">(you will receive)</p>
                <div className="row gap-2">
                  <Input
                    type="number"
                    placeholder="Service Cost"
                    className="resize-none h-[40px] outline-none text-lg text-primary"
                    value={item.serviceCost}
                    onChange={(e) =>
                      handleUpdateCustomization(item.id, "serviceCost", e.target.value)
                    }
                  />
                  <p>{currencySymbol}</p>
                </div>
                <p className="text-titleLabel text-xs font-light min-h-[32px]">
                  By Seller. VAT inclusive.
                </p>
              </div>
              <div className="grid w-full items-center gap-1.5 mt-3">
                <Label className="text-base font-[600] text-titleLabel1">List Price</Label>
                <p className="text-titleLabel text-xs -mt-3">(user will pay)</p>
                <div className="row gap-2">
                  <Input
                    type="number"
                    placeholder="List Price"
                    className="resize-none h-[40px] outline-none text-lg text-primary"
                    value={item.listPrice}
                    disabled
                    onChange={(e) =>
                      handleUpdateCustomization(item.id, "listPrice", e.target.value)
                    }
                  />
                  <p>{currencySymbol}</p>
                </div>
                <p className="text-titleLabel text-xs font-light">
                  **Includes AMUZ App Fee (16% of List Price)
                </p>
              </div>

              <div className="grid w-full items-center gap-1.5 mt-3">
                <Label className="text-base font-[600] text-titleLabel1">Time</Label>
                <div className="row gap-2">
                  <Input
                    type="number"
                    placeholder="Hours"
                    className="resize-none h-[40px] outline-none text-lg text-primary"
                    value={item.timeHours}
                    onChange={(e) =>
                      handleUpdateCustomization(item.id, "timeHours", e.target.value)
                    }
                  />
                  <p>hours</p>
                </div>
              </div>
              <div className="grid w-full items-center gap-1.5 mt-3">
                <Label className="text-base font-[600] text-titleLabel1">Time</Label>
                <div className="row gap-2">
                  <Input
                    type="number"
                    placeholder="Days"
                    className="resize-none h-[40px] outline-none text-lg text-primary"
                    value={formatDuration(item.timeHours, {
                      dayLabel: "",
                      hourLabel: "",
                    })}
                    disabled
                  />
                  <p>days</p>
                </div>
              </div>
            </div>

            <p className="mt-3">8 hours = 1 day</p>
            <p className="mt-3 text-xs">
              The service proposed delivery date shall not exceed 2 weeks (112 hours).
            </p>
            <p className="mt-1 text-xs">
              The service delivery due date, based on above hour, will appear in 'My Order' page
              details after order obtaining ACCEPTED status.
            </p>

            <div className="mt-4">
              <p className="text-primary font-[600] mb-3">Media Files</p>

              {/* Custom media uploader */}
              <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center mb-4 hover:border-primary transition-colors">
                <input
                  type="file"
                  id={`media-upload-${item.id}`}
                  multiple
                  accept="image/*,video/*"
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      handleMediaUpload(item.id, Array.from(e.target.files));
                    }
                  }}
                />
                <label htmlFor={`media-upload-${item.id}`} className="cursor-pointer block">
                  <div className="flex items-center justify-center">
                    <Image className="h-6 w-6 text-gray-400 mr-2" />
                    <Video className="h-6 w-6 text-gray-400" />
                  </div>
                  <p className="mt-2 text-sm text-gray-600 font-medium">
                    Click to upload images or videos
                  </p>
                  <p className="mt-1 text-xs text-gray-400">
                    Files will be stored in images/videos folders
                  </p>
                </label>
                {uploading && currentUploadingItem === item.id && (
                  <div className="mt-2 flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2"></div>
                    <p className="text-xs text-primary">Uploading...</p>
                  </div>
                )}
              </div>

              {/* Display uploaded media */}
              {item.media && item.media.length > 0 && (
                <div>
                  <p className="text-sm font-medium mb-2 text-gray-700">
                    Uploaded files ({item.media.length})
                  </p>
                  <div className="grid grid-cols-3 gap-3 mt-2">
                    {item.media.map((media: any, mediaIndex: number) => {
                      // Handle both string URLs and object format
                      const mediaUrl = typeof media === "string" ? media : media.url;
                      const mediaType =
                        typeof media === "string"
                          ? mediaUrl.includes("videos")
                            ? "videos"
                            : "images"
                          : media.type;

                      const isVideo = mediaType === "videos";

                      return (
                        <div
                          key={mediaIndex}
                          className="relative h-24 bg-gray-100 rounded-lg overflow-hidden border border-gray-200 group"
                        >
                          {isVideo ? (
                            <div className="w-full h-full flex items-center justify-center bg-black">
                              <video src={mediaUrl} className="max-h-full max-w-full" />
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                                <span className="text-white text-xs px-2 py-1 bg-black bg-opacity-50 rounded">
                                  Video
                                </span>
                              </div>
                            </div>
                          ) : (
                            <img
                              src={mediaUrl}
                              alt={`Media ${mediaIndex}`}
                              className="w-full h-full object-cover"
                            />
                          )}
                          <button
                            type="button"
                            onClick={() => handleRemoveMedia(item.id, media)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            aria-label="Remove media"
                          >
                            <X size={12} />
                          </button>
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">
                            {typeof media === "string"
                              ? mediaUrl.split("/").pop()?.split("_").pop()
                              : media.name || `File ${mediaIndex + 1}`}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Add customization button */}

        <div
          className="fixed bottom-6 right-10  z-[999999] p-3 rounded-full bg-primary text-white font-bold cursor-pointer bg-color-indicator-plus shadow-lg"
          onClick={handleAddCustomization}
        >
          <Plus className="w-10 h-10" style={{ strokeWidth: "2.5px" }} />
        </div>
      </>
    );
  };

  return (
    <>
      <div>
        <div className="">
          {/* header */}
          <div className="w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div onClick={() => onSelectService(1)} className="cursor-pointer row">
                <ChevronLeft />
                <p className="pl-[2px]">Back</p>
              </div>
              <p className="text-titleLabel font-bold">Edit Customizations</p>
              <p
                onClick={loading || uploadStatus !== "idle" ? undefined : handleSaveCustomizations}
                className={`cursor-pointer ${
                  loading || uploadStatus !== "idle" ? "opacity-50 flex items-center" : ""
                }`}
              >
                {loading ? (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary animate-spin mr-2"
                    >
                      <line x1="12" y1="2" x2="12" y2="6"></line>
                      <line x1="12" y1="18" x2="12" y2="22"></line>
                      <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
                      <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
                      <line x1="2" y1="12" x2="6" y2="12"></line>
                      <line x1="18" y1="12" x2="22" y2="12"></line>
                      <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
                      <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
                    </svg>
                    Saving...
                  </>
                ) : (
                  "Save"
                )}
              </p>
            </div>
          </div>
          <div className="w-full overflow-y-scroll overflow-x-hidden gap-3 hide-scroll-custom bg-white h-[calc(100vh-300px)] max-md:h-[calc(100vh-150px)] pt-4 pb-16 px-3">
            {renderContent()}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            {deleteStatus === "deleting" ? (
              <div className="flex flex-col items-center justify-center min-h-[200px] py-6">
                <div className="bg-blue-50 rounded-full p-4 mb-4">
                  <Loader size={36} className="text-primary animate-spin" />
                </div>
                <p className="text-center text-black text-lg font-medium mb-2">
                  Deleting Customization...
                </p>
                <p className="text-gray-500 text-center">
                  Please wait while we process your request.
                </p>
              </div>
            ) : deleteStatus === "success" ? (
              <div className="flex flex-col items-center justify-between min-h-[200px] py-4">
                <div className="flex flex-col items-center">
                  <div className="bg-green-100 p-3 rounded-full mb-4">
                    <Check size={32} className="text-green-600" />
                  </div>
                  <p className="text-center text-black text-lg font-medium">
                    Customization deleted successfully
                  </p>
                </div>
                <div className="w-full mt-auto">
                  <Button
                    variant="outline"
                    className="rounded-full w-full border-primary text-primary border-2 py-5 text-base font-medium transition-all hover:bg-primary hover:text-white"
                    onClick={() => {
                      setDeleteStatus("idle");
                      setDeleteModalOpen(false);
                      setCustomizationList((prevList) =>
                        prevList.filter((item) => item.id !== customizationToDelete)
                      );
                      setCustomizationToDelete(null);
                    }}
                  >
                    Close
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <p className="text-center text-black text-lg mb-6">
                  Are you sure you want to delete this customization?
                </p>
                <div>
                  <Button
                    variant="outline"
                    className="rounded-full w-full border-primary text-primary border-2 py-5 text-base font-medium transition-all hover:bg-primary hover:text-white mt-6"
                    onClick={handleDeleteCustomization}
                  >
                    Yes, delete
                  </Button>

                  <Button
                    variant="outline"
                    className="rounded-full w-full border-primary text-primary border-2 py-5 text-base font-medium transition-all hover:bg-primary hover:text-white mt-3"
                    onClick={cancelDelete}
                  >
                    No, cancel
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default EditCustomizationService;
