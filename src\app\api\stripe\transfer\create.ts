import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeTransferRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      amount,
      currency,
      chargeId,
      accountId,
      isUS
    }: StripeTransferRequest = req.body;

    if (!amount || !currency || !chargeId || !accountId) {
      return res.status(400).json({ 
        error: 'Amount, currency, charge ID, and account ID are required' 
      });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const transfer = await stripeService.transfers.create({
      amount: parseInt(amount),
      currency,
      source_transaction: chargeId,
      destination: accountId,
    });

    res.status(200).json({
      transfer,
      success: true,
    });

  } catch (error) {
    console.error('Error creating transfer:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
