"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation"; // Import the useRouter hook for navigation
import Profile from "@/screens/profile";
import useAuth from "@/hook";
import LoadingOverlay from "@/components/loadingOverlay";

const ProfilePage = () => {
  const [isChecking, setIsChecking] = useState(true); // State to handle the checking/loading phase
  const user = useAuth(); // Assuming useAuth returns null or undefined if the user is not logged in
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!user.isLogin) {
        router.push("/"); // Redirect to the home page if the user is not logged in
      }
      setIsChecking(false); // Stop the checking/loading phase
    }, 2000); // 2-second delay

    return () => clearTimeout(timer); // Cleanup the timer when the component unmounts
  }, [user, router]);

  // Show a loading indicator during the 2-second timer
  if (isChecking) {
    return (
      <div>
        <LoadingOverlay isLoading={isChecking} />
      </div>
    ); // Replace with your loading component or animation
  }

  return (
    <div>
      <Profile 
        userId="my-profile" 
        profileType="amuzn"
        profile_name={user.users?.profile_name || user.userId}
      />
    </div>
  );
};

export default ProfilePage;
