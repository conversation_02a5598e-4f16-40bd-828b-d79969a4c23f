/**
 * Utility functions for handling checkout redirects and data
 */

/**
 * Redirect to custom embedded checkout instead of Stripe hosted checkout
 * @param {Object} checkoutData - Checkout parameters
 * @param {string} checkoutData.userId - User ID
 * @param {string} checkoutData.userEmail - User email
 * @param {number} checkoutData.amount - Amount in cents
 * @param {string} checkoutData.currency - Currency code
 * @param {string} checkoutData.productName - Product name
 * @param {string} checkoutData.productDescription - Product description
 * @param {boolean} checkoutData.isEscrow - Whether this is an escrow payment
 * @param {string} checkoutData.sellerId - Seller ID (for escrow)
 * @param {string} checkoutData.sellerEmail - Seller email (for escrow)
 * @param {string} checkoutData.sellerStripeAccountId - Seller Stripe account (for escrow)
 * @param {string} checkoutData.orderId - Order ID (for escrow)
 */
export function redirectToCustomCheckout(checkoutData) {
  // Store checkout data in localStorage as backup
  localStorage.setItem('checkoutData', JSON.stringify(checkoutData))
  
  // Create URL with parameters
  const params = new URLSearchParams()
  
  Object.entries(checkoutData).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      params.append(key, value.toString())
    }
  })
  
  // Redirect to custom checkout page
  window.location.href = `/checkout?${params.toString()}`
}

/**
 * Redirect to the new custom payment form (like the image you showed)
 * @param {Object} paymentData - Payment data for the custom form
 * @param {number} paymentData.amount - Amount in cents
 * @param {string} paymentData.currency - Currency code
 * @param {string} paymentData.productName - Product name
 * @param {string} paymentData.productImage - Product image URL
 */
export function redirectToCustomPaymentForm(paymentData) {
  // Store data in localStorage as backup
  if (typeof window !== 'undefined') {
    localStorage.setItem('checkoutData', JSON.stringify(paymentData))
  }

  // Create URL with parameters
  const params = new URLSearchParams({
    amount: paymentData.amount || 6500,
    currency: paymentData.currency || 'usd',
    productName: paymentData.productName || 'Pure set',
    productImage: paymentData.productImage || '/api/placeholder/300/300',
  })

  // Redirect to custom checkout page
  window.location.href = `/checkout-custom?${params.toString()}`
}

/**
 * Replace existing Stripe hosted checkout calls with custom checkout
 * @param {Object} originalCheckoutData - Data that would be sent to hosted checkout
 * @returns {Promise} - Promise that resolves when redirect happens
 */
export async function replaceHostedCheckout(originalCheckoutData) {
  // Transform the data format if needed to match your custom checkout
  const customCheckoutData = {
    userId: originalCheckoutData.userId,
    userEmail: originalCheckoutData.userEmail,
    amount: originalCheckoutData.amount || 1000,
    currency: originalCheckoutData.currency || 'usd',
    productName: originalCheckoutData.productName || 'Product',
    productDescription: originalCheckoutData.productDescription || '',
    isEscrow: originalCheckoutData.isEscrow || false,
    sellerId: originalCheckoutData.sellerId,
    sellerEmail: originalCheckoutData.sellerEmail,
    sellerStripeAccountId: originalCheckoutData.sellerStripeAccountId,
    orderId: originalCheckoutData.orderId,
  }
  
  redirectToCustomCheckout(customCheckoutData)
  
  return Promise.resolve()
}

/**
 * Helper function to extract checkout data from basket items
 * @param {Array} basketItems - Array of basket items
 * @param {Object} userInfo - User information
 * @returns {Object} - Formatted checkout data
 */
export function formatBasketForCheckout(basketItems, userInfo) {
  const totalAmount = basketItems.reduce((sum, item) => sum + (item.subtotal * 100), 0) // Convert to cents
  
  const productNames = basketItems.map(item => item.title).join(', ')
  const productDescription = basketItems.length > 1 
    ? `${basketItems.length} items` 
    : basketItems[0]?.description || ''
  
  return {
    userId: userInfo.userId,
    userEmail: userInfo.userEmail,
    amount: totalAmount,
    currency: 'usd',
    productName: productNames,
    productDescription: productDescription,
    isEscrow: basketItems.some(item => item.orderDetails?.switchEnabled), // Check if any item uses escrow
  }
}

/**
 * Helper function for seller-to-seller payments
 * @param {string} sellerId - Seller ID
 * @param {Object} userInfo - Buyer information
 * @param {Object} orderInfo - Order details
 * @returns {Object} - Formatted checkout data for seller payment
 */
export function formatSellerPayment(sellerId, userInfo, orderInfo) {
  return {
    userId: userInfo.userId,
    userEmail: userInfo.userEmail,
    amount: orderInfo.amount,
    currency: orderInfo.currency || 'usd',
    productName: orderInfo.productName,
    productDescription: orderInfo.productDescription,
    isEscrow: false, // Direct seller payments typically don't use escrow
    sellerId: sellerId,
  }
}

/**
 * Helper function for escrow payments
 * @param {Object} escrowData - Escrow payment data
 * @returns {Object} - Formatted checkout data for escrow payment
 */
export function formatEscrowPayment(escrowData) {
  return {
    userId: escrowData.userId,
    userEmail: escrowData.userEmail,
    amount: escrowData.amount,
    currency: escrowData.currency || 'usd',
    productName: escrowData.productName,
    productDescription: escrowData.productDescription,
    isEscrow: true,
    sellerId: escrowData.sellerId,
    sellerEmail: escrowData.sellerEmail,
    sellerStripeAccountId: escrowData.sellerStripeAccountId,
    orderId: escrowData.orderId,
  }
}
