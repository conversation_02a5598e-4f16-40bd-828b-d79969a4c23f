import { useState } from 'react';
import { redirectToCustomCheckout } from '../../utils/checkout';

export default function BuyFromSeller() {
  const [loading, setLoading] = useState(false);

  const handlePaySeller = async () => {
    setLoading(true);

    try {
      // Hardcoded seller for demo — in real app, this is dynamic!
      const sellerId = "seller_123";

      // Instead of creating a hosted checkout session, redirect to custom checkout
      const checkoutData = {
        userId: "user_123", // Get from your auth system
        userEmail: "<EMAIL>", // Get from your auth system
        amount: 1000, // $10.00 in cents
        currency: 'usd',
        productName: 'Test Product',
        productDescription: 'Purchase from seller',
        isEscrow: false, // Direct seller payment
        sellerId: sellerId,
      };

      redirectToCustomCheckout(checkoutData);
    } catch (error) {
      console.error('Error initiating payment:', error);
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Buy from Seller</h1>
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-4">
          <h3 className="font-semibold">Test Product</h3>
          <p className="text-gray-600">Direct payment to seller</p>
          <p className="text-xl font-bold text-green-600">$10.00</p>
        </div>
        <button
          onClick={handlePaySeller}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-md transition-colors"
        >
          {loading ? "Setting up payment..." : "Pay Seller $10"}
        </button>
      </div>
    </div>
  );
}
