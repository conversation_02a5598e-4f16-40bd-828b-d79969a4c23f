import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>dalBody, ModalContent } from "@heroui/react";
import { Badge } from "@/components/ui/badge";
import { Check, Loader, X, Mail } from "lucide-react";

interface SendVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSend: () => Promise<boolean>;
  email?: string;
}

const SendVerificationModal: React.FC<SendVerificationModalProps> = ({
  isOpen,
  onClose,
  onSend,
  email,
}) => {
  const [status, setStatus] = useState<"idle" | "sending" | "sent" | "error">(
    "idle"
  );
  const [countdown, setCountdown] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");

  // Handle countdown for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Reset states when modal opens
  useEffect(() => {
    if (isOpen) {
      setStatus("idle");
      setErrorMessage("");
    }
  }, [isOpen]);

  const handleSendVerification = async () => {
    setStatus("sending");
    setErrorMessage("");

    try {
      const success = await onSend();

      if (success) {
        setStatus("sent");
        setCountdown(30); // 30 seconds cooldown for resend
      } else {
        setStatus("error");
        setErrorMessage("Failed to send verification code. Please try again.");
      }
    } catch (error) {
      setStatus("error");
      setErrorMessage("An error occurred. Please try again.");
      console.error("Verification error:", error);
    }
  };

  const formatEmail = (email: string): string => {
    if (!email || !email.includes("@")) return email;

    const [name, domain] = email.split("@");

    // Keep first 3 characters and last character of name
    const visibleNameStart = name.substring(0, 3);
    const visibleNameEnd = name.charAt(name.length - 1);

    // Format as "tes****<EMAIL>"
    return `${visibleNameStart}****${visibleNameEnd}@${domain}`;
  };

  return (
    <Modal
      isOpen={isOpen}
      size="md"
      hideCloseButton={true}
      isDismissable={false}
    >
      <ModalContent>
        {status === "sent" ? (
          <>
            <ModalBody className="flex flex-col items-center justify-center py-5">
              <div className="bg-green-100 rounded-full p-4 mb-4">
                <Check size={48} className="text-green-500" />
              </div>
              <h2 className="text-xl font-bold mb-2 text-center">
                Verification Email Sent!
              </h2>
              <p className="text-gray-500 text-center mb-4">
                We've sent a verification code to {formatEmail(email || "")}.
                <br></br>
                Please check your inbox and follow the instructions to verify
                your email.
              </p>

              <div className="px-12">
                {countdown > 0 ? (
                  <Badge
                    className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-gray-300 text-gray-400 btn  mt-5 cursor-not-allowed"
                    variant="outline"
                    onClick={() => {}}
                  >
                    Resend in {countdown}s
                  </Badge>
                ) : (
                  <Badge
                    className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-5"
                    variant="outline"
                    onClick={handleSendVerification}
                  >
                    Resend Code
                  </Badge>
                )}

                <Badge
                  className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-3"
                  variant="outline"
                  onClick={onClose}
                >
                  Close
                </Badge>
              </div>
            </ModalBody>
          </>
        ) : status === "sending" ? (
          <ModalBody className="flex flex-col items-center justify-center py-5">
            <div className="bg-blue-50 rounded-full p-4 mb-4">
              <Loader size={48} className="text-blue-500 animate-spin" />
            </div>
            <h2 className="text-xl font-bold mb-2 text-center">
              Sending Verification Email...
            </h2>
            <p className="text-gray-500 text-center">
              Please wait while we send the verification email.
            </p>
          </ModalBody>
        ) : (
          <>
            <ModalBody>
              <div className="row justify-between mb-4">
                <div onClick={onClose} className="cursor-pointer">
                  {/* <X /> */}
                </div>
                <p className="font-bold text-primary">Verify Your Email</p>
                <div className="w-6"></div> {/* Empty div for alignment */}
              </div>

              <div className="flex flex-col items-center justify-center py-3">
                <div className="bg-blue-50 rounded-full p-4 mb-4">
                  <Mail size={40} className="text-blue-500" />
                </div>

                <p className="text-center mb-6">
                  We'll send a verification code to{" "}
                  <span className="font-semibold">
                    {formatEmail(email || "")}
                  </span>{" "}
                  <br></br>. Check your inbox and follow the instructions to
                  verify your email.
                </p>

                {errorMessage && (
                  <p className="text-red-500 text-sm mb-4">{errorMessage}</p>
                )}

                <div className="px-12 w-full">
                  <Badge
                    className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-5"
                    variant="outline"
                    onClick={handleSendVerification}
                  >
                    Send Verification Code
                  </Badge>

                  <Badge
                    className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-3"
                    variant="outline"
                    onClick={onClose}
                  >
                    Cancel
                  </Badge>
                </div>
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default SendVerificationModal;
