import * as Tabs from "@radix-ui/react-tabs";
import { TabsContent, TabsTrigger } from "@/components/ui/tabs";

import {
  SideSheetContainer,
  SideSheetHeader,
  SideSheetDescription,
} from "@/components/ui/sidebarSheet";
import { closeEvent } from "@/lib/eventEmmiter";

export function OrderCardDetail({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <SideSheetContainer
      className="max-md:left-0 left-[48.25rem] max-lg:left-[26.25rem] max-w-[26.25rem]"
      open={open}
      onOpenChange={(isOpen) => {
        // Prevent closing the sheet unless explicitly triggered
        if (!isOpen) return;
        onOpenChange(isOpen);
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Header */}
      <SideSheetHeader className="px-8 xs-px-2">
        {/* Header */}{" "}
        <div
          className="cursor-pointer text-base text-black font-normal row gap-2"
          // Close only when Back button is clicked
        >
          <img src="/assets/left-arrow.svg" alt="" />
          Back
        </div>
        <p className="text-base font-bold text-titleLabel">My Orders</p>
        <p className="text-base text-primary font-bold opacity-0">Done</p>
      </SideSheetHeader>

      {/* Tabs */}
      <SideSheetDescription>
        <Tabs.Root
          defaultValue="All"
          className=" overflow-scroll h-full hide-scroll max-md:overflow-x-hidden mt-4"
        >
          <div className="sticky top-0 bg-white z-50 pb-2 ">
            <Tabs.List
              className="TabsListBg w-full"
              aria-label="Manage your account"
              style={
                {
                  "--active-bg-color": "#BDBDBD",
                } as React.CSSProperties
              }
            >
              <Tabs.Trigger className="TabsTriggerBg" value="All">
                All
              </Tabs.Trigger>
              <Tabs.Trigger className="TabsTriggerBg" value="Placed">
                Placed
              </Tabs.Trigger>
              <Tabs.Trigger className="TabsTriggerBg" value="Received">
                Received
              </Tabs.Trigger>
            </Tabs.List>
          </div>

          <div className="pb-12">
            <TabsContent value="All">{/* <OrderCard /> */}</TabsContent>
            <TabsContent value="Placed">
              <p>Placed</p>
            </TabsContent>
            <TabsContent value="Received">
              <p>Received</p>
            </TabsContent>
          </div>
        </Tabs.Root>
      </SideSheetDescription>
    </SideSheetContainer>
  );
}
