export interface User {
  id: string;
  uid?: string; // Firebase Auth UID
  about_me: string;
  app_version: string;
  basketOrdersCount: number;
  avatar: string;
  avatarSmall: string;
  facebookLink: string;
  instagramLink: string;
  bookmarks: string[]; // Array of followings IDs
  post_bookmarked: string[]; // all the posts which are bookmarked
  categories: string[]; // Array of category names
  created_at: Date; // Timestamp as a string (you can also use Firebase's Timestamp type)
  currency: string; // 'usd' or other supported currencies
  currentStatus: string; // e.g. 'Available', 'Unavailable'
  date_of_birth: string; // Date in string format (e.g. "1.9.2003")
  device_language: string; // e.g. 'en', 'fr', etc.
  email?: string | null;
  events: string[]; // Array of event IDs
  full_name: string;
  hashtags: string[]; // Array of hashtags
  isEightyPercentsFilled: boolean;
  isMultiCurrenciesInfoShowed: boolean;
  isUS: boolean; // true or false
  languages: string[]; // Array of language names
  last_seen: Date; // Timestamp as a string
  location: string; // e.g. "Accra, Ghana"
  myOpenOrdersCount: number;
  password: string; // Encrypted password
  payment_method_id?: string | null; // Stripe or other payment method IDs
  personal_moto: string;
  posts: string[]; // Array of post IDs
  profile_name: string; // User's profile name
  services: string[]; // Array of service IDs
  starredPosts: string[]; // Array of starred post IDs
  stripe_id?: string | undefined; // Stripe account ID
  type: "creator" | "user"; // User type
  followers: string[]; // not used
  following: string[]; // not used
  isDeleted?: boolean;
}
