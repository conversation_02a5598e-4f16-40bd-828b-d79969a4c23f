import BrowseLensUser from ".";
import { useEffect, useState, useCallback, useMemo } from "react";
import { AccountsBulkQuery, MainContentFocus, PageSize, PostsQuery, PostType, usePostsQuery } from "@/graphql/test/generated";

const BrowseLensData = ({ userId, categoryName, postId, userData, refetch }: {
  userId: string,
  postId: string,
  categoryName: string,
  userData: AccountsBulkQuery["accountsBulk"][0],
  refetch: any
}) => {
  const [userPosts, setUserPosts] = useState<PostsQuery["posts"]["items"]>([]);
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);

  // Query configuration for fetching posts
  const queryRequest = useMemo(() => ({
    pageSize: PageSize.Fifty,
    cursor: currentCursor,
    filter: {
      authors: [userId],
      postTypes: [PostType.Root],
      metadata: {
        mainContentFocus: [
          MainContentFocus.Image, MainContentFocus.Video
        ],
      },
    }
  }), [userId, currentCursor]);

  // Fetch posts data
  const { data: publicationsData } = usePostsQuery({ 
    request: queryRequest
  });

  // Update posts state when new data is fetched
  useEffect(() => {
    if (publicationsData?.posts?.items) {
      setUserPosts((prevData) => {
        const newItems = publicationsData.posts.items;
        
        // Create a new array to avoid reference issues
        const updatedPosts = [...prevData];
        
        // Add only new posts that aren't already in the array
        newItems.forEach(newItem => {
          if (!updatedPosts.some(existingItem => existingItem.id === newItem.id)) {
            updatedPosts.push(newItem);
          }
        });
        
        return updatedPosts;
      });
    }
  }, [publicationsData]);

  // Load next page of posts
  const loadNextPage = useCallback(() => {
    const nextCursor = publicationsData?.posts?.pageInfo?.next;
    if (nextCursor) setCurrentCursor(nextCursor);
  }, [publicationsData]);

  // Convert any timestamp format to a comparable number
  const getTimestampValue = useCallback((timestamp: any): number => {
    if (!timestamp) return 0;
    
    // Handle number timestamps (unix timestamps)
    if (typeof timestamp === 'number') {
      return timestamp;
    }
    
    // Handle ISO string or other string date formats
    try {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return date.getTime();
      }
    } catch (e) {
      console.error("Error parsing date:", e);
    }
    
    return 0;
  }, []);

  // Sort posts by timestamp (newest first)
  const sortedPosts = useMemo(() => {
    if (!userPosts || userPosts.length === 0) return [];
    
    // Create a new array to avoid mutating the original
    return [...userPosts].sort((a, b) => {
      const timeA = getTimestampValue(a.timestamp);
      const timeB = getTimestampValue(b.timestamp);
      
      // Sort in descending order (newest first)
      return timeB - timeA;
    });
  }, [userPosts, getTimestampValue]);

  const decodedCategory = useMemo(() => decodeURIComponent(categoryName), [categoryName]);
  
  return (
    <div>
      <BrowseLensUser
        isLens={true}
        postId={postId}
        userData={userData}
        postData={sortedPosts}
        userPost={publicationsData}
        loadNextPage={loadNextPage}
        categoryName={decodedCategory}
        refetch={refetch}
      />
    </div>
  );
};

export default BrowseLensData;