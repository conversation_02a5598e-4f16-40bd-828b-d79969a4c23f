// types/basket.ts
export interface CustomizationOption {
  id: string;
  title: string;
  description: string;
  price: string; // Your data has price as string
  duration: string; // Your data has duration as string
  media: string[]; // Array of image paths
  updated_at?: any; // Timestamp
}

export interface BasketItem {
  id: number;
  orderId?: string; // Original Firebase document ID for updates
  title: string;
  description: string;
  time: string;
  subtotal: number;
  image: string;
  userName: string;
  selectedCustomizations?: string[];
  // Updated to match your actual Firebase serviceDetails structure
  serviceDetails?: {
    customizations: CustomizationOption[]; // Now using full customization objects
    id: string;
    title?: string;
    duration?: string;
    price?: string;
    description?: string;
  } | null;
  orderDetails: {
    deliveryDate: string;
    switchEnabled: boolean;
    transactionFee: number;
    selectedDate: Date;
    comment?: string;
    selectedOptions?: string[];
  };
}

export interface EditBasketItemProps {
  selectedItem: BasketItem | null;
  onSave: (item: BasketItem) => void;
  onClose: () => void;
}
