"use client";
import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../../../../theme";
import useAuth from "@/hook";
import { useEffect, useRef, useState } from "react";

import React from "react";
import Link from "next/link";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import {
  FollowersQuery,
  FollowingOrderBy,
  FollowingQuery,
  PageSize,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";
import EmptyState from "@/components/EmptyState";

const Following = (otherUserID: any) => {
  // Web 3 Following list
  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [allFollowing, setAllFollowing] = useState<FollowingQuery["following"]["items"]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true); // Track if this is the first load
  const [isPaginating, setIsPaginating] = useState(false); // Track pagination loading
  const [localLoading, setLocalLoading] = useState(true); // Local loading state for skeleton
  const [scrollPosition, setScrollPosition] = useState(0); // Store scroll position

  // Add a function to handle follow/unfollow state changes
  const handleFollowChange = (profileId: string, isFollowed: boolean) => {
    // Update the UI immediately for better user experience
    const updatedFollowing = [...allFollowing];
    for (let i = 0; i < updatedFollowing.length; i++) {
      if (updatedFollowing[i].following.address === profileId) {
        try {
          // Try to update the follow status directly
          // This might not work due to TypeScript issues, but it's worth trying
          // @ts-ignore - Ignore TypeScript errors for this operation
          updatedFollowing[i].following.operations.isFollowedByMe = isFollowed;
        } catch (error) {
          console.error("Error updating follow status:", error);
        }
      }
    }

    try {
      // Try to update the following list
      // @ts-ignore - Ignore TypeScript errors for this operation
      setAllFollowing(updatedFollowing);
    } catch (error) {
      console.error("Error updating following list:", error);
    }
  };
  const containerRef = useRef<HTMLDivElement>(null); // ✅ Reference to scroll container

  const { data: following, isLoading: following_loading } = useFollowingQuery(
    {
      request: {
        account: otherUserID.otherUserID, // address
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // Reset data when otherUserID changes
  useEffect(() => {
    setAllFollowing([]);
    setCurrentFollowingCursor(null);
    setIsInitialLoad(true);
    setIsPaginating(false);
    setLocalLoading(true);
  }, [otherUserID.otherUserID]);

  // Handle loading states
  useEffect(() => {
    // Set pagination loading state when loading starts
    if (following_loading) {
      // If we have existing following, this is pagination
      if (allFollowing.length > 0) {
        setIsPaginating(true);
        // Store current scroll position before pagination
        if (containerRef.current) {
          setScrollPosition(containerRef.current.scrollTop);
        }
      } else {
        // This is initial load
        setIsInitialLoad(true);
      }
    } else {
      // Loading finished
      setIsPaginating(false);
      setIsInitialLoad(false);

      // Restore scroll position after pagination (with a small delay)
      if (containerRef.current && scrollPosition > 0) {
        setTimeout(() => {
          if (containerRef.current) {
            containerRef.current.scrollTop = scrollPosition;
          }
        }, 100);
      }
    }
  }, [following_loading, allFollowing.length, scrollPosition]);

  // Effect to manage local loading state - only show skeleton on initial load
  useEffect(() => {
    // Only show skeleton loader on initial load, not during pagination
    if (isInitialLoad && following_loading) {
      setLocalLoading(true);
    }

    // Only transition to non-loading state when loading is complete
    if (!following_loading) {
      // Use a single timeout to prevent state flickering
      const timer = setTimeout(() => {
        setLocalLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [following_loading, isInitialLoad]);

  // Preserve scroll position while appending new data
  useEffect(() => {
    if (following?.following?.items) {
      const container = containerRef.current;
      if (!container) return;

      const previousScrollTop = container.scrollTop; // ✅ Save scroll position before update

      // Only append new data if it's not already in the list
      const newItems = following.following.items.filter(
        (newItem) =>
          !allFollowing.some(
            (existingItem) => existingItem.following.address === newItem.following.address
          )
      );

      if (newItems.length > 0) {
        setAllFollowing((prev) => [...prev, ...newItems]);
      }

      setTimeout(() => {
        container.scrollTop = previousScrollTop; // ✅ Restore previous scroll position
      }, 0);
    }
  }, [following, allFollowing]);

  const loadNextFollowing = () => {
    const nextCursor = following?.following?.pageInfo?.next;
    if (nextCursor) {
      setCurrentFollowingCursor(nextCursor);
    }
  };

  // Function to detect scroll bottom
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight + 20) {
      loadNextFollowing();
    }
  };

  return (
    <div
      className="overflow-y-scroll hide-scroll-custom bg-white h-[calc(100vh-280px)] max-md:h-[calc(100vh-107px)]"
      onScroll={handleScroll}
      ref={containerRef} // ✅ Attach scroll container reference
    >
      {localLoading ? (
        <div className="p-4">
          <ProfileCardSkeleton count={6} columns={2} showGrid={true} />
        </div>
      ) : allFollowing.length === 0 ? (
        <EmptyState
          type="following"
          title={
            otherUserID.otherUserID && otherUserID.otherUserID !== "my-profile"
              ? "Not following anyone"
              : "Not following anyone yet"
          }
          message={
            otherUserID.otherUserID && otherUserID.otherUserID !== "my-profile"
              ? "This user isn't following anyone yet."
              : "When you follow people, they'll appear here. Explore profiles to find people to follow!"
          }
          isOwnProfile={!(otherUserID.otherUserID && otherUserID.otherUserID !== "my-profile")}
          customIcon="/assets/empty-following.svg"
        />
      ) : (
        <div>
          <div className="grid grid-cols-2 max-md:grid-cols-1 gap-3 p-4">
            {allFollowing.map((curr: FollowingQuery["following"]["items"][0], index) => {
              return (
                <div key={index} className="w-full">
                  <div className="row justify-between mt-0">
                    <GlobalProfileCardLens
                      themeProperties="#00"
                      isFollow={curr?.following?.operations?.isFollowedByMe}
                      location={
                        getLocation(curr?.following?.metadata?.attributes) ||
                        curr?.following?.username?.localName ||
                        "location*"
                      }
                      profile_name={curr.following?.metadata?.name || "profile name"}
                      avatar={
                        curr?.following?.metadata?.picture ??
                        "https://static.hey.xyz/images/default.png"
                      }
                      id={curr?.following?.username?.localName}
                      onFollowChange={handleFollowChange}
                    />
                  </div>
                </div>
              );
            })}
          </div>

          {/* Pagination loader - show at bottom when paginating */}
          {isPaginating && (
            <div className="p-4">
              <ProfileCardSkeleton count={2} columns={2} showGrid={true} />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Following;
