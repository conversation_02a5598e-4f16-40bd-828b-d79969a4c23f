"use client";
import React, { useEffect, useState, useRef } from "react";
import { create } from "zustand";
import { Connector, useAccount, useConnect, useDisconnect, useConnectorClient } from "wagmi";
import { <PERSON>dal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { Check, Loader2, AlertCircle, X } from "lucide-react";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import useLensUser from "../lib/auth/useLensUser";
import useLogin from "../lib/auth/useLogin";
import { PageSize, useAccountsAvailableQuery } from "@/graphql/test/generated";
import { GetWalletId, UpdateAuthBridge_V2 } from "@/services/authBridgeService";
import { initFirebase } from "../../firebaseConfig";
import { useLoading } from "@/context/LoadingContext";
import useHandleWrongNetwork from "@/lib/useHandleWrongNetwork";

// Create a store to manage the global state of the sign-in modal
interface SignInStore {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  isWalletConnecting: boolean;
  setIsWalletConnecting: (connecting: boolean) => void;
}

export const useSignInStore = create<SignInStore>((set) => ({
  isOpen: false,
  setIsOpen: (isOpen) => set({ isOpen }),
  isWalletConnecting: false,
  setIsWalletConnecting: (connecting) => set({ isWalletConnecting: connecting }),
}));

// Helper function to detect mobile devices
function isMobileDevice() {
  if (typeof window !== "undefined") {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }
  return false;
}

// This component will be mounted at the root level
const GlobalSignInButton = () => {
  const { isOpen, setIsOpen } = useSignInStore();
  const [mounted, setMounted] = useState(false);
  const { isConnected } = useAccount();

  // Only render on client side
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close the modal when wallet is connected
  useEffect(() => {
    if (isConnected && isOpen) {
      // Give a small delay to allow the wallet connection to complete
      const timer = setTimeout(() => {
        setIsOpen(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isConnected, setIsOpen, isOpen]);

  if (!mounted) return null;

  return (
    <>
      <WalletConnectionModal isOpen={isOpen} setIsOpen={setIsOpen} />
    </>
  );
};

// Main wallet connection modal component
interface WalletConnectionModalProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const WalletConnectionModal: React.FC<WalletConnectionModalProps> = ({ isOpen, setIsOpen }) => {
  const [isMobile, setIsMobile] = useState(false);
  const lensLoginStatusRef = useRef<"idle" | "loading" | "success" | "error">("idle");
  const { address, isConnected, connector } = useAccount();
  const { data: connectorClient } = useConnectorClient();
  const [isPostConnectModalOpen, setIsPostConnectModalOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<any>(undefined);
  const { isSignedInQuery, profileQuery } = useLensUser();
  const { mutate: requestLogin } = useLogin();
  const { disconnect } = useDisconnect();
  const { connectors, connect, error } = useConnect();
  const [showWalletReminder, setShowWalletReminder] = useState(false);
  const [isWalletConnecting, setIsWalletConnecting] = useState(false);
  const { hideLoader } = useLoading();
  const handleWrongNetwork = useHandleWrongNetwork();
  // Store the connected wallet info for proper deep linking
  const [connectedWalletInfo, setConnectedWalletInfo] = useState<{
    connectorId: string;
    walletName?: string;
  } | null>(null);

  // Connection states
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const availableConnectors: any = ["injected", "walletConnect"];

  // Function to detect the actual wallet being used
  const detectActualWallet = () => {
    // Try multiple methods to detect the wallet

    // Method 1: Check localStorage for stored wallet name
    const storedWallet = localStorage.getItem("walletconnect-wallet-name");
    if (storedWallet && storedWallet !== "WalletConnect") {
      console.log(`Detected wallet from localStorage: ${storedWallet}`);
      return storedWallet;
    }

    // Method 2: Check WalletConnect session data
    try {
      // Check various WalletConnect localStorage keys
      const wcKeys = Object.keys(localStorage).filter(
        (key) =>
          key.includes("wc@2") || key.includes("walletconnect") || key.includes("WALLETCONNECT")
      );

      console.log(`Checking ${wcKeys.length} WalletConnect keys:`, wcKeys);

      for (const key of wcKeys) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const parsed = JSON.parse(data);
            console.log(`Checking key ${key}:`, parsed);

            // Look for wallet metadata in various possible locations
            if (parsed.peer && parsed.peer.metadata && parsed.peer.metadata.name) {
              const walletName = parsed.peer.metadata.name;
              console.log(`Found wallet from WC session peer: ${walletName}`);
              return walletName;
            }

            // Check for session data with different structure
            if (parsed.sessions) {
              for (const sessionKey in parsed.sessions) {
                const session = parsed.sessions[sessionKey];
                if (session.peer && session.peer.metadata && session.peer.metadata.name) {
                  const walletName = session.peer.metadata.name;
                  console.log(`Found wallet from WC session: ${walletName}`);
                  return walletName;
                }
              }
            }

            // Check for metadata directly
            if (parsed.metadata && parsed.metadata.name) {
              const walletName = parsed.metadata.name;
              console.log(`Found wallet from WC metadata: ${walletName}`);
              return walletName;
            }

            // Check for name field directly
            if (parsed.name && typeof parsed.name === "string") {
              console.log(`Found wallet name directly: ${parsed.name}`);
              return parsed.name;
            }
          }
        } catch (e) {
          console.log(`Error parsing key ${key}:`, e);
          // Continue to next key
        }
      }
    } catch (error) {
      console.log("Could not parse WalletConnect session data:", error);
    }

    // Method 3: Check for specific wallet indicators in user agent or other sources
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes("rainbow")) return "Rainbow";
    if (userAgent.includes("metamask")) return "MetaMask";
    if (userAgent.includes("trust")) return "Trust Wallet";
    if (userAgent.includes("coinbase")) return "Coinbase Wallet";

    // Method 4: Check for injected wallet providers
    if (typeof window !== "undefined" && window.ethereum) {
      if ((window.ethereum as any).isMetaMask) return "MetaMask";
      if ((window.ethereum as any).isTrust) return "Trust Wallet";
      if ((window.ethereum as any).isCoinbaseWallet) return "Coinbase Wallet";
    }

    console.log("Could not detect specific wallet, using generic WalletConnect");
    return "WalletConnect";
  };

  // Function to get wallet-specific deep link URLs
  const getWalletDeepLink = (walletName?: string) => {
    // If no wallet name provided, try to detect it
    if (!walletName || walletName === "WalletConnect") {
      walletName = detectActualWallet();
    }

    // Ensure we have a valid wallet name
    const finalWalletName = walletName || "WalletConnect";

    // Log the wallet name for debugging
    console.log(`Looking up deep link for wallet: "${finalWalletName}"`);

    const walletDeepLinks: { [key: string]: { ios: string; android: string } } = {
      MetaMask: {
        ios: "metamask://",
        android: "metamask://",
      },
      Rainbow: {
        ios: "rainbow://",
        android: "rainbow://",
      },
      "Trust Wallet": {
        ios: "trust://",
        android: "trust://",
      },
      "Coinbase Wallet": {
        ios: "cbwallet://",
        android: "cbwallet://",
      },
      "1inch Wallet": {
        ios: "oneinch://",
        android: "oneinch://",
      },
      Argent: {
        ios: "argent://",
        android: "argent://",
      },
      Binance: {
        ios: "bnc://",
        android: "bnc://",
      },
      "Crypto.com": {
        ios: "cryptowallet://",
        android: "cryptowallet://",
      },
      Exodus: {
        ios: "exodus://",
        android: "exodus://",
      },
      imToken: {
        ios: "imtokenv2://",
        android: "imtokenv2://",
      },
      "Ledger Live": {
        ios: "ledgerlive://",
        android: "ledgerlive://",
      },
      "Math Wallet": {
        ios: "mathwallet://",
        android: "mathwallet://",
      },
      "OKX Wallet": {
        ios: "okx://",
        android: "okx://",
      },
      Phantom: {
        ios: "phantom://",
        android: "phantom://",
      },
      SafePal: {
        ios: "safepal://",
        android: "safepal://",
      },
      TokenPocket: {
        ios: "tpoutside://",
        android: "tpoutside://",
      },
      "Uniswap Wallet": {
        ios: "uniswap://",
        android: "uniswap://",
      },
      "Wallet 3": {
        ios: "wallet3://",
        android: "wallet3://",
      },
      Zerion: {
        ios: "zerion://",
        android: "zerion://",
      },
      Zapper: {
        ios: "zapper://",
        android: "zapper://",
      },
      Rabby: {
        ios: "rabby://",
        android: "rabby://",
      },
      Frame: {
        ios: "frame://",
        android: "frame://",
      },
      "Brave Wallet": {
        ios: "brave://",
        android: "brave://",
      },
      "Opera Wallet": {
        ios: "opera://",
        android: "opera://",
      },
      "Edge Wallet": {
        ios: "edge://",
        android: "edge://",
      },
      "Atomic Wallet": {
        ios: "atomic://",
        android: "atomic://",
      },
      Unstoppable: {
        ios: "unstoppable://",
        android: "unstoppable://",
      },
      "Alpha Wallet": {
        ios: "alphawallet://",
        android: "alphawallet://",
      },
      Pillar: {
        ios: "pillar://",
        android: "pillar://",
      },
      Status: {
        ios: "status-im://",
        android: "status-im://",
      },
      WalletConnect: {
        ios: "wc:",
        android: "wc:",
      },
    };

    const userAgent = navigator.userAgent.toLowerCase();
    const isIOS = userAgent.includes("iphone") || userAgent.includes("ipad");
    const isAndroid = userAgent.includes("android");

    // Try exact match first
    let wallet = walletDeepLinks[finalWalletName];

    // If not found, try case-insensitive search
    if (!wallet) {
      const lowerWalletName = finalWalletName.toLowerCase();
      const foundKey = Object.keys(walletDeepLinks).find(
        (key) => key.toLowerCase() === lowerWalletName
      );
      if (foundKey) {
        wallet = walletDeepLinks[foundKey];
        console.log(`Found wallet with case-insensitive match: ${foundKey}`);
      }
    }

    // If still not found, try partial match (contains)
    if (!wallet) {
      const lowerWalletName = finalWalletName.toLowerCase();
      const foundKey = Object.keys(walletDeepLinks).find(
        (key) =>
          key.toLowerCase().includes(lowerWalletName) || lowerWalletName.includes(key.toLowerCase())
      );
      if (foundKey) {
        wallet = walletDeepLinks[foundKey];
        console.log(`Found wallet with partial match: ${foundKey} for ${finalWalletName}`);
      }
    }

    // Final fallback to generic WalletConnect
    if (!wallet) {
      console.warn(
        `Wallet "${finalWalletName}" not found in deep links list. Using generic WalletConnect.`
      );
      wallet = walletDeepLinks["WalletConnect"];
    }

    if (isIOS) return wallet.ios;
    if (isAndroid) return wallet.android;
    return wallet.ios; // default to iOS format
  };

  // States for loading and success
  const [lensLoginStatus, setLensLoginStatus] = useState<"idle" | "loading" | "success" | "error">(
    "idle"
  );
  const [disconnectStatus, setDisconnectStatus] = useState<"idle" | "loading" | "success">("idle");

  // Update ref when state changes
  useEffect(() => {
    lensLoginStatusRef.current = lensLoginStatus;
  }, [lensLoginStatus]);

  // Get available Lens accounts
  const { data: accountData } = useAccountsAvailableQuery(
    {
      accountsAvailableRequest: {
        managedBy: address,
        includeOwned: true,
        pageSize: PageSize.Fifty,
      },
      lastLoggedInAccountRequest: {
        address,
      },
    },
    {
      enabled: !!address && isConnected,
      refetchOnWindowFocus: false,
    }
  );

  const allProfiles = accountData?.accountsAvailable.items || [];
  const lastLogin = accountData?.lastLoggedInAccount;
  const remainingProfiles = lastLogin
    ? allProfiles
        .filter(({ account }) => account.address !== lastLogin.address)
        .map(({ account }) => account)
    : allProfiles.map(({ account }) => account);
  const accounts = lastLogin ? [lastLogin, ...remainingProfiles] : remainingProfiles;

  // Check if we're on a mobile device on component mount
  useEffect(() => {
    setIsMobile(isMobileDevice());
  }, []);

  // Detect and store wallet information when already connected (e.g., after page refresh)
  useEffect(() => {
    if (isConnected && !connectedWalletInfo && connector) {
      console.log(`Current connector details:`, {
        id: connector.id,
        name: connector.name,
        type: connector.type,
        uid: connector.uid,
      });

      // For WalletConnect, try to get more specific wallet info
      let walletName = connector.name;

      // Check if we can get more specific wallet info from the connector
      if (connector.id === "walletConnect") {
        // Try to get wallet info from localStorage or other sources
        const storedWalletInfo = localStorage.getItem("walletconnect-wallet-name");
        if (storedWalletInfo) {
          walletName = storedWalletInfo;
          console.log(`Found stored wallet name: ${walletName}`);
        } else {
          // Try to detect from user agent or other methods
          console.log(`WalletConnect detected but no specific wallet name found`);
        }
      }

      setConnectedWalletInfo({
        connectorId: connector.id,
        walletName: walletName,
      });
      console.log(`Detected connected wallet: ${walletName} (${connector.id})`);
    }
  }, [isConnected, connectedWalletInfo, connector]);

  // Hide full-screen loader when modal opens to prevent interference
  useEffect(() => {
    if (isOpen) {
      hideLoader();
    }
  }, [isOpen, hideLoader]);

  // Handle connection errors
  useEffect(() => {
    if (error) {
      setConnectionError(error.message);
      setIsConnecting(false);
    }
  }, [error]);

  // Handle connection success
  useEffect(() => {
    if (isConnected && isConnecting) {
      setIsConnecting(false);
      setIsOpen(false);
    }
  }, [isConnected, isConnecting, setIsOpen]);

  // Handle wallet disconnection
  const handleDisconnect = () => {
    setDisconnectStatus("loading");

    // Add a small delay to show the loading state
    setTimeout(() => {
      disconnect();
      setDisconnectStatus("success");

      // Show success state for 1.5 seconds before closing
      setTimeout(() => {
        setDisconnectStatus("idle");
        setIsPostConnectModalOpen(false);
      }, 1500);
    }, 500);
  };

  // Handle wallet connection
  const handleConnectWallet = (connector: Connector) => {
    try {
      setIsConnecting(true);
      setConnectionError(null);
      setIsWalletConnecting(true);

      console.log(`Connecting to wallet: ${connector.name} (${connector.id})`);

      // Store wallet information for proper deep linking later
      const walletInfo = {
        connectorId: connector.id,
        walletName: connector.name,
      };

      setConnectedWalletInfo(walletInfo);

      // Store wallet name in localStorage for persistence across sessions
      if (connector.name && connector.name !== "WalletConnect") {
        localStorage.setItem("walletconnect-wallet-name", connector.name);
        console.log(`Stored wallet name in localStorage: ${connector.name}`);
      }

      // Update global store to indicate wallet connection is in progress
      useSignInStore.getState().setIsWalletConnecting(true);

      // Hide the full-screen loader to prevent interference with wallet connection
      hideLoader();

      // Temporarily hide our modal to prevent overlay conflicts with WalletConnect
      if (connector.id === "walletConnect") {
        setIsOpen(false);
      }

      connect({ connector });
    } catch (err) {
      console.error("Connection error:", err);
      setIsConnecting(false);
      setIsWalletConnecting(false);

      // Update global store
      useSignInStore.getState().setIsWalletConnecting(false);

      setConnectionError(err instanceof Error ? err.message : "Failed to connect wallet");

      // Restore our modal if there was an error
      if (connector.id === "walletConnect") {
        setIsOpen(true);
      }
    }
  };

  // Filter available connectors
  const filteredConnectors = connectors
    .filter((connector: any) => availableConnectors.includes(connector.id))
    .sort(
      (a: Connector, b: Connector) =>
        availableConnectors.indexOf(a.id) - availableConnectors.indexOf(b.id)
    );

  // We're no longer doing aggressive cleanup as it affects other components

  // Show wallet reminder when account is selected on mobile
  useEffect(() => {
    if (selectedAccount && isMobile) {
      setShowWalletReminder(true);
      // Hide the reminder after 15 seconds to give users more time to notice it
      const timer = setTimeout(() => {
        setShowWalletReminder(false);
      }, 15000);

      return () => clearTimeout(timer);
    }
  }, [selectedAccount, isMobile]);

  // Open post-connect modal when wallet is connected
  useEffect(() => {
    if (isConnected && !isSignedInQuery?.data && !isPostConnectModalOpen) {
      // Open the post-connect modal immediately
      const timer = setTimeout(() => {
        setIsPostConnectModalOpen(true);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isConnected, isSignedInQuery?.data, isPostConnectModalOpen]);

  // Handle successful Lens connection
  useEffect(() => {
    if (isConnected && isSignedInQuery?.data && profileQuery?.data?.account) {
      // If we have a successful Lens connection, show success state
      if (lensLoginStatus === "loading") {
        setLensLoginStatus("success");

        // Show success state for 1.5 seconds before closing
        const successTimer = setTimeout(() => {
          setLensLoginStatus("idle");
          setIsPostConnectModalOpen(false);

          // Close the global SignInButton modal
          if (isOpen) {
            useSignInStore.getState().setIsOpen(false);

            // Reload the page to ensure all UI components update properly
            // This ensures the sidebar and navbar show the correct profile information
            window.location.reload();
          }
        }, 1500);

        return () => clearTimeout(successTimer);
      } else {
        // If we're not in loading state (e.g., already signed in), just close modals
        setIsPostConnectModalOpen(false);

        // Close the global SignInButton modal
        if (isOpen) {
          const timer = setTimeout(() => {
            useSignInStore.getState().setIsOpen(false);

            // Reload the page to ensure all UI components update properly
            window.location.reload();
          }, 500);

          return () => clearTimeout(timer);
        }
      }
    }
  }, [isConnected, isSignedInQuery?.data, profileQuery?.data, isOpen, lensLoginStatus]);

  // Handle wallet connection errors and restore modal if needed
  useEffect(() => {
    if (error && isWalletConnecting) {
      setIsConnecting(false);
      setIsWalletConnecting(false);

      // Update global store
      useSignInStore.getState().setIsWalletConnecting(false);

      setConnectionError(error.message);

      // Restore our modal if WalletConnect failed
      if (!isConnected) {
        setIsOpen(true);
      }
    }
  }, [error, isWalletConnecting, isConnected, setIsOpen]);

  // Handle successful wallet connection
  useEffect(() => {
    if (isConnected && isWalletConnecting) {
      setIsConnecting(false);
      setIsWalletConnecting(false);

      // Update global store
      useSignInStore.getState().setIsWalletConnecting(false);

      // Try to detect the actual wallet from WalletConnect session after connection
      if (connector?.id === "walletConnect") {
        setTimeout(() => {
          const detectedWallet = detectActualWallet();
          if (detectedWallet && detectedWallet !== "WalletConnect") {
            console.log(`Post-connection wallet detection: ${detectedWallet}`);
            setConnectedWalletInfo((prev) =>
              prev
                ? {
                    ...prev,
                    walletName: detectedWallet,
                  }
                : {
                    connectorId: "walletConnect",
                    walletName: detectedWallet,
                  }
            );
            // Store the detected wallet name
            localStorage.setItem("walletconnect-wallet-name", detectedWallet);
          }
        }, 2000); // Wait 2 seconds for WalletConnect session to be established
      }

      // Don't restore the modal here - let the normal flow handle it
    }
  }, [isConnected, isWalletConnecting, connector]);

  const updateAuthBridge = async (selectedAccount: any) => {
    try {
      const _wallet_id: string | null = address ?? (await GetWalletId());

      if (selectedAccount) {
        const { auth } = await initFirebase();
        const user = auth?.currentUser;
        if (!user?.uid || !_wallet_id) {
          return;
        }
        await UpdateAuthBridge_V2({
          wallet_id: _wallet_id,
          user_id: user.uid,
          email: user.email,
          lens_id: selectedAccount?.username?.value,
          lens_code: selectedAccount?.address,
        });
      }
    } catch (error) {
      console.log({ error });
    }
  };

  // Wallet connection component
  function WalletOption({ connector }: { connector: Connector }) {
    const [ready, setReady] = React.useState(false);

    React.useEffect(() => {
      (async () => {
        try {
          const provider = await connector.getProvider();
          setReady(!!provider);
        } catch (error) {
          console.error(`Error getting provider for ${connector.name}:`, error);
          setReady(false);
        }
      })();
    }, [connector]);

    return (
      <Button
        key={connector.uid}
        disabled={!ready || isConnecting}
        onClick={() => handleConnectWallet(connector)}
        className="w-full mb-2 py-6 flex items-center justify-center"
        variant="outline"
      >
        {isConnecting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
        {connector.name === "Injected" ? "Browser Wallet" : connector.name}
      </Button>
    );
  }

  // If not connected, show wallet connection modal
  if (!isConnected && isOpen) {
    return (
      <>
        <Modal
          isDismissable={true}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={setIsOpen}
          hideCloseButton={false}
        >
          <ModalContent className="modal-content py-6 px-5 sm:px-6 md:max-w-md w-full mx-auto bg-white shadow-lg border border-gray-100 md:rounded-xl">
            {() => (
              <>
                <ModalHeader>
                  <div className="text-xl font-semibold">Connect Wallet</div>
                </ModalHeader>
                <ModalBody>
                  <div className="mb-4">Connect your wallet to sign in with Lens</div>

                  <div className="space-y-2 mb-4">
                    {filteredConnectors.map((connector) => (
                      <WalletOption key={connector.uid} connector={connector} />
                    ))}
                  </div>

                  {connectionError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4 flex items-start">
                      <AlertCircle className="text-red-500 mr-2 mt-0.5 h-5 w-5 flex-shrink-0" />
                      <p className="text-red-700 text-sm">{connectionError}</p>
                    </div>
                  )}

                  {isConnecting && (
                    <div className="flex justify-center items-center py-3">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      <span className="ml-2">Connecting...</span>
                    </div>
                  )}

                  <div className="mt-4">
                    <p className="text-xs text-gray-500 text-center w-full">
                      By connecting your wallet, you agree to our Terms of Service and Privacy
                      Policy
                    </p>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </>
    );
  }

  return (
    <>
      {/* Wallet Reminder Notification for Mobile */}
      {showWalletReminder && isMobile && (
        <div className="fixed top-0 left-0 right-0 mx-auto w-full bg-blue-600 text-white p-6 shadow-lg z-[9999] animate-pulse">
          <div className="flex items-start gap-3 max-w-md mx-auto">
            <div className="mt-0.5">
              <span className="relative flex h-5 w-5">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                <span className="relative inline-flex rounded-full h-5 w-5 bg-white"></span>
              </span>
            </div>
            <div>
              <p className="font-bold text-lg mb-2">ACTION REQUIRED!</p>
              <p className="text-base opacity-90">
                Please open your wallet app now to check for pending confirmation requests for Lens
                sign-in. Your wallet app should have a notification waiting.
              </p>
              <button
                className="mt-3 bg-white text-blue-600 font-bold py-2 px-4 rounded-full"
                onClick={() => {
                  // Try to reopen wallet app again when button is clicked
                  try {
                    if (typeof window !== "undefined") {
                      const deepLink = getWalletDeepLink(connectedWalletInfo?.walletName);
                      console.log(
                        `Opening wallet from reminder: ${deepLink} (Wallet: ${connectedWalletInfo?.walletName})`
                      );
                      window.location.href = deepLink;
                    }
                  } catch (error) {
                    console.error("Error reopening wallet:", error);
                    // Fallback to generic WalletConnect
                    window.location.href = "wc:";
                  }
                }}
              >
                Open Wallet App
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Post-Connection Modal - Using HeroUI Modal */}
      <Modal
        isDismissable={false}
        isOpen={isPostConnectModalOpen}
        placement="auto"
        onOpenChange={setIsPostConnectModalOpen}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content py-6 px-5 sm:px-6 md:max-w-md w-full mx-auto bg-white shadow-lg border border-gray-100 md:rounded-xl">
          {() => (
            <>
              <ModalBody>
                {/* Loading State for Lens Login */}
                {lensLoginStatus === "loading" ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    {/* Custom animated connection visualization */}
                    <div className="relative w-64 h-32 mb-6">
                      {/* Wallet icon on the left */}
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-blue-500 to-purple-500 p-4 rounded-xl shadow-lg">
                        <div className="w-12 h-12 flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="white"
                            className="w-8 h-8"
                          >
                            <path d="M2.273 5.625A4.483 4.483 0 0 1 5.25 4.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0 0 18.75 3H5.25a3 3 0 0 0-2.977 2.625ZM2.273 8.625A4.483 4.483 0 0 1 5.25 7.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0 0 18.75 6H5.25a3 3 0 0 0-2.977 2.625ZM5.25 9a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h13.5a3 3 0 0 0 3-3v-6a3 3 0 0 0-3-3H15a.75.75 0 0 0-.75.75 2.25 2.25 0 0 1-4.5 0A.75.75 0 0 0 9 9H5.25Z" />
                          </svg>
                        </div>
                      </div>

                      {/* Lens icon on the right */}
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-green-400 to-emerald-500 p-4 rounded-xl shadow-lg">
                        <div className="w-12 h-12 flex items-center justify-center">
                          <img src="/assets/lens.png" alt="Lens Logo" className="w-10 " />
                        </div>
                      </div>

                      {/* Animated connection line */}
                      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-green-400 animate-pulse-gradient"></div>
                      </div>

                      {/* Animated dots */}
                      <div className="absolute left-[40%] top-1/2 transform -translate-y-1/2 flex space-x-1">
                        <div
                          className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                          style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                          style={{ animationDelay: "200ms" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                          style={{ animationDelay: "400ms" }}
                        ></div>
                      </div>
                    </div>

                    <h2 className="text-xl font-bold mb-2 text-center">
                      Waiting for Wallet Confirmation
                    </h2>
                    <p className="text-gray-500 text-center mb-4 max-w-sm">
                      Please check your wallet app and approve the signature request to connect your
                      Lens account.
                    </p>

                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-100 rounded-lg p-4 max-w-sm shadow-sm">
                      <div className="flex items-start">
                        <div className="flex-shrink-0 mt-0.5">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-purple-500"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-purple-700">
                            <span className="font-medium">Important:</span> Your wallet may open in
                            a separate window or require you to open your wallet app manually.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Custom CSS for animations */}
                    <style jsx>{`
                      @keyframes pulse-gradient {
                        0% {
                          transform: translateX(-100%);
                        }
                        100% {
                          transform: translateX(100%);
                        }
                      }
                      .animate-pulse-gradient {
                        animation: pulse-gradient 2s infinite;
                      }
                    `}</style>
                  </div>
                ) : /* Error State for Lens Login */
                lensLoginStatus === "error" ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="bg-red-50 rounded-full p-4 mb-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-12 w-12 text-red-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                    </div>
                    <h2 className="text-xl font-bold mb-2 text-center">Connection Issue</h2>
                    <p className="text-gray-500 text-center mb-4 max-w-sm">
                      {connectionError ||
                        "There was an issue connecting to your wallet. The request might have been canceled or timed out."}
                    </p>

                    <div className="flex flex-col gap-3 w-full max-w-xs">
                      <button
                        className="bg-primary text-white font-medium py-2.5 px-4 rounded-full hover:bg-primary/90 transition-colors"
                        onClick={() => {
                          // Clear error state
                          setConnectionError(null);
                          setLensLoginStatus("loading");
                          handleWrongNetwork();
                          // Try to reopen wallet app
                          try {
                            if (selectedAccount) {
                              // Request login with the selected account again
                              requestLogin(selectedAccount.address);
                              updateAuthBridge(selectedAccount);
                              // For mobile devices, try to reopen the wallet app
                              if (isMobile) {
                                try {
                                  const deepLink = getWalletDeepLink(
                                    connectedWalletInfo?.walletName
                                  );
                                  console.log(
                                    `Retrying with deep link: ${deepLink} (Wallet: ${connectedWalletInfo?.walletName})`
                                  );
                                  window.location.href = deepLink;
                                } catch (error) {
                                  console.error("Error reopening wallet during retry:", error);
                                  // Fallback to generic WalletConnect
                                  window.location.href = "wc:";
                                }
                              }

                              // Set a new timeout
                              setTimeout(() => {
                                if (lensLoginStatusRef.current === "loading") {
                                  setConnectionError(
                                    "The wallet confirmation is still taking longer than expected. Please check your wallet app."
                                  );
                                  setLensLoginStatus("error");
                                }
                              }, 30000);
                            }
                          } catch (error) {
                            console.error("Error retrying wallet connection:", error);
                            setConnectionError("Failed to reconnect. Please try again later.");
                            setLensLoginStatus("error");
                          }
                        }}
                      >
                        Retry Connection
                      </button>

                      <button
                        className="text-gray-600 font-medium py-2.5 px-4 rounded-full border border-gray-300 hover:bg-gray-50 transition-colors"
                        onClick={() => {
                          // Reset states and close modal
                          setLensLoginStatus("idle");
                          setConnectionError(null);
                          setIsPostConnectModalOpen(false);
                        }}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : /* Success State for Lens Login */
                lensLoginStatus === "success" ? (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="bg-green-100 rounded-full p-4 mb-4">
                      <Check size={48} className="text-green-500" />
                    </div>
                    <h2 className="text-xl font-bold mb-2 text-center">Successfully Connected!</h2>
                    <p className="text-gray-500 text-center">
                      Your Lens account has been connected successfully.
                    </p>
                  </div>
                ) : /* Loading State for Wallet Disconnect */
                disconnectStatus === "loading" ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    {/* Custom animated disconnection visualization */}
                    <div className="relative w-64 h-32 mb-6">
                      {/* Wallet icon with disconnect animation */}
                      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-blue-500 to-purple-500 p-4 rounded-xl shadow-lg animate-pulse">
                        <div className="w-16 h-16 flex items-center justify-center relative">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="white"
                            className="w-10 h-10"
                          >
                            <path d="M2.273 5.625A4.483 4.483 0 0 1 5.25 4.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0 0 18.75 3H5.25a3 3 0 0 0-2.977 2.625ZM2.273 8.625A4.483 4.483 0 0 1 5.25 7.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0 0 18.75 6H5.25a3 3 0 0 0-2.977 2.625ZM5.25 9a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h13.5a3 3 0 0 0 3-3v-6a3 3 0 0 0-3-3H15a.75.75 0 0 0-.75.75 2.25 2.25 0 0 1-4.5 0A.75.75 0 0 0 9 9H5.25Z" />
                          </svg>

                          {/* Animated disconnection symbol */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-20 h-1 bg-red-500 rotate-45 animate-fadeIn"></div>
                          </div>
                        </div>
                      </div>

                      {/* Animated disconnection circles */}
                      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div
                          className="w-24 h-24 rounded-full border-2 border-gray-200 opacity-0 animate-ping-slow"
                          style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                          className="absolute inset-0 w-24 h-24 rounded-full border-2 border-gray-200 opacity-0 animate-ping-slow"
                          style={{ animationDelay: "500ms" }}
                        ></div>
                        <div
                          className="absolute inset-0 w-24 h-24 rounded-full border-2 border-gray-200 opacity-0 animate-ping-slow"
                          style={{ animationDelay: "1000ms" }}
                        ></div>
                      </div>
                    </div>

                    <h2 className="text-xl font-bold mb-2 text-center">Disconnecting Wallet</h2>
                    <p className="text-gray-500 text-center mb-4 max-w-sm">
                      Please wait while we safely disconnect your wallet from the application.
                    </p>

                    {/* Custom CSS for animations */}
                    <style jsx>{`
                      @keyframes ping-slow {
                        0% {
                          transform: scale(0.8);
                          opacity: 0.8;
                        }
                        50% {
                          opacity: 0.5;
                        }
                        100% {
                          transform: scale(1.5);
                          opacity: 0;
                        }
                      }
                      .animate-ping-slow {
                        animation: ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite;
                      }
                      @keyframes fadeIn {
                        0% {
                          opacity: 0;
                        }
                        100% {
                          opacity: 1;
                        }
                      }
                      .animate-fadeIn {
                        animation: fadeIn 0.5s ease-in forwards;
                      }
                    `}</style>
                  </div>
                ) : /* Success State for Wallet Disconnect */
                disconnectStatus === "success" ? (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="bg-green-100 rounded-full p-4 mb-4">
                      <Check size={48} className="text-green-500" />
                    </div>
                    <h2 className="text-xl font-bold mb-2 text-center">Wallet Disconnected!</h2>
                    <p className="text-gray-500 text-center">
                      Your wallet has been disconnected successfully.
                    </p>
                  </div>
                ) : (
                  /* Normal State - Account Selection */
                  <>
                    <div className="flex justify-center items-center mb-0">
                      <div className="text-xl font-semibold text-black">Connect Lens Account</div>
                    </div>
                    <div className="border-b-1 mb-2">
                      {accounts && accounts.length > 0 ? (
                        <div className="flex flex-col gap-2.5 max-h-[30vh] overflow-y-auto pr-1">
                          {accounts.map((current) => (
                            <Badge
                              key={current.address}
                              className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer my-3 mb-5"
                              variant="outline"
                              onClick={() => {
                                // Clear any previous errors
                                setConnectionError(null);

                                // // Show loading state
                                setLensLoginStatus("loading");

                                // // Set the selected account
                                setSelectedAccount(current);

                                // Save the selected account to localStorage
                                localStorage.setItem("lens-user", JSON.stringify(current));

                                // For mobile devices, show the wallet reminder immediately
                                if (isMobile) {
                                  setShowWalletReminder(true);

                                  //   // Try to automatically reopen the wallet app
                                  try {
                                    // For injected wallets (like MetaMask in browser)
                                    if (
                                      connectedWalletInfo?.connectorId === "injected" &&
                                      typeof window !== "undefined" &&
                                      window.ethereum &&
                                      (window.ethereum as any).isMetaMask
                                    ) {
                                      (window.ethereum as any).request({
                                        method: "eth_requestAccounts",
                                      });
                                    } else {
                                      // For WalletConnect - use wallet-specific deep linking
                                      const deepLink = getWalletDeepLink(
                                        connectedWalletInfo?.walletName
                                      );
                                      console.log(
                                        `Opening wallet with deep link: ${deepLink} (Wallet: ${connectedWalletInfo?.walletName})`
                                      );
                                      window.location.href = deepLink;
                                    }
                                  } catch (error) {
                                    console.error("Error reopening wallet:", error);
                                  }
                                }
                                handleWrongNetwork();
                                // Request login with the selected account
                                // The success state will be handled by the useEffect that monitors isSignedInQuery
                                requestLogin(current.address);
                                updateAuthBridge(current);

                                // Set a timeout to detect if the user might have canceled the wallet connection
                                // If after 30 seconds we're still in loading state, show an error with retry option
                                const timeoutId = setTimeout(() => {
                                  setConnectionError(
                                    "The wallet confirmation is taking longer than expected. The request might have been canceled or is still pending."
                                  );
                                  setLensLoginStatus("error");
                                }, 30000);

                                // Clear timeout if component unmounts
                                return () => clearTimeout(timeoutId);
                              }}
                            >
                              {current.username?.localName || "Unnamed Account"}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 py-2 px-3 bg-gray-50 rounded-lg border border-gray-200">
                          No Lens accounts available
                        </p>
                      )}
                    </div>
                    <div className="flex flex-col gap-5">
                      <div className="p-3.5 border border-gray-200 rounded-lg bg-gray-50">
                        <p className="text-sm text-gray-500 mb-1 text-center">Connected Wallet</p>
                        <p className="font-mono text-sm sm:text-base truncate text-center">
                          {address
                            ? `${address.substring(0, 8)}...${address.substring(
                                address.length - 6
                              )}`
                            : ""}
                        </p>
                        <Badge
                          variant="outline"
                          className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-1"
                          onClick={handleDisconnect}
                        >
                          Disconnect Wallet
                        </Badge>
                      </div>

                      {isMobile && (
                        <div className="p-3.5 border border-gray-200 rounded-lg bg-blue-50 text-blue-700">
                          <p className="text-sm font-medium mb-1">
                            <span className="flex items-center gap-1.5 text-center justify-center">
                              <span className="relative flex h-2 w-2">
                                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                                <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                              </span>
                              Mobile Instructions
                            </span>
                          </p>
                          <p className="text-xs text-center mb-2">
                            After selecting a Lens account above, if your wallet app doesn't open
                            automatically, please manually open your wallet app and check for
                            pending confirmation requests.
                          </p>

                          {/* Debug Info */}
                          {/* <div className="mt-2 p-2 bg-blue-100 rounded text-xs">
                            <p>
                              <strong>Debug Info:</strong>
                            </p>
                            <p>Connector: {connectedWalletInfo?.connectorId || "None"}</p>
                            <p>Wallet: {connectedWalletInfo?.walletName || "None"}</p>
                            <p>Detected: {detectActualWallet()}</p>
                            <div className="flex gap-1">
                              <button
                                className="px-2 py-1 bg-blue-600 text-white rounded text-xs"
                                onClick={() => {
                                  const detected = detectActualWallet();
                                  const deepLink = getWalletDeepLink(detected);
                                  alert(
                                    `Detected: ${detected}\nDeep Link: ${deepLink}\nStored: ${connectedWalletInfo?.walletName}`
                                  );
                                }}
                              >
                                Test Detection
                              </button>
                              <button
                                className="px-2 py-1 bg-green-600 text-white rounded text-xs"
                                onClick={() => {
                                  // Force re-detection of wallet
                                  const detectedWallet = detectActualWallet();
                                  if (detectedWallet && detectedWallet !== "WalletConnect") {
                                    setConnectedWalletInfo((prev) =>
                                      prev
                                        ? {
                                            ...prev,
                                            walletName: detectedWallet,
                                          }
                                        : {
                                            connectorId: "walletConnect",
                                            walletName: detectedWallet,
                                          }
                                    );
                                    localStorage.setItem(
                                      "walletconnect-wallet-name",
                                      detectedWallet
                                    );
                                    alert(`Updated wallet to: ${detectedWallet}`);
                                  } else {
                                    alert("Could not detect specific wallet");
                                  }
                                }}
                              >
                                Refresh
                              </button>
                            </div>
                          </div> */}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default GlobalSignInButton;
