"use client";
import { Badge } from "@/components/ui/badge";
import { themes } from "../../../../../../../theme";
import useAuth from "@/hook";
import { useCallback, useEffect, useState } from "react";
import { FollowerManager } from "@/services/followServices";
import Link from "next/link";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { X } from "react-feather";
import AuthSignup from "@/screens/auth";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";
import { getUserId } from "@/utils/userUtils";
import EmptyState from "@/components/EmptyState";

const Following = (otherUserID: any) => {
  const user = useAuth();
  const [followingList, setFollowingList]: any = useState([]);
  const [followersList, setFollowersList]: any = useState([]);
  const [triggerEffect, setTriggerEffect] = useState(false);
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [loadingOnClick, setLoadingOnClick] = useState<{
    [key: string]: boolean;
  }>({});

  const generateFileUrl = (postFile?: string): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) return undefined;
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) return postFile;

    if (postFile.includes("https://ik.imagekit.io")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const handleFollowing = async (userId: string) => {
    try {
      const resp = await FollowerManager.getInstance().GetFollowersByUserId(userId);

      setFollowingList(resp || []);
      // Delay setting isDataLoaded to true to ensure the skeleton loader is shown
      setTimeout(() => {
        setIsDataLoaded(true);
      }, 500);
    } catch (error) {
      console.error("Error fetching followings:", error);
      setIsDataLoaded(true); // Set to true even on error to avoid infinite loading
    }
  };

  useEffect(() => {
    // Check if otherUserID is an object with otherUserID property
    if (otherUserID && typeof otherUserID === "object" && otherUserID.otherUserID) {
      // Check if it's not "my-profile" and is a valid string
      if (
        otherUserID.otherUserID !== "my-profile" &&
        typeof otherUserID.otherUserID === "string" &&
        otherUserID.otherUserID.trim() !== ""
      ) {
        handleFollowing(otherUserID.otherUserID);
      } else {
        // Use logged-in user's ID if available
        if (user?.userId) {
          handleFollowing(user.userId);
        }
      }
    }
    // Check if otherUserID is a string directly
    else if (typeof otherUserID === "string" && otherUserID.trim() !== "") {
      handleFollowing(otherUserID);
    }
    // Fallback to logged-in user's ID
    else if (user?.userId) {
      handleFollowing(user.userId);
    }
  }, [user?.userId, otherUserID]);

  // Get the list of profiles the logged-in user is following
  // This is used to determine the follow/unfollow button status
  const fetchLoggedInUserFollowings = async () => {
    try {
      if (!user?.userId) return;

      const resp = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);
      setFollowersList(resp || []);
    } catch (error) {
      console.error("Error fetching logged-in user followings:", error);
    }
  };

  // Get followers for the profile being viewed
  const handleFollowers = async (userId: any) => {
    try {
      const resp = await FollowerManager.getInstance().GetFollowersByUserId(userId);
      // console.log({ resp });

      setFollowingList(resp || []);

      // Set isDataLoaded to true after data is loaded
      setIsDataLoaded(true);
    } catch (error) {
      console.error("Error fetching followers:", error);
      // Set isDataLoaded to true even on error to avoid infinite loading
      setIsDataLoaded(true);
    }
  };

  // Always fetch the logged-in user's followings to determine follow/unfollow button status
  useEffect(() => {
    if (user?.userId) {
      // Fetch the logged-in user's followings
      fetchLoggedInUserFollowings();
    }
  }, [user?.userId, triggerEffect]);

  // Fetch the followers for the profile being viewed
  useEffect(() => {
    // Reset the data loaded state when the component mounts or when the user ID changes
    setIsDataLoaded(false);

    // Show the skeleton loader for at least 500ms
    const timer = setTimeout(() => {
      // Check if otherUserID is an object with otherUserID property
      if (otherUserID && typeof otherUserID === "object" && otherUserID.otherUserID) {
        // Check if it's not "my-profile" and is a valid string
        if (
          otherUserID.otherUserID !== "my-profile" &&
          typeof otherUserID.otherUserID === "string" &&
          otherUserID.otherUserID.trim() !== ""
        ) {
          handleFollowers(otherUserID.otherUserID);
        } else {
          // Use logged-in user's ID if available
          if (user?.userId) {
            handleFollowers(user.userId);
          }
        }
      }
      // Check if otherUserID is a string directly
      else if (typeof otherUserID === "string" && otherUserID.trim() !== "") {
        handleFollowers(otherUserID);
      }
      // Fallback to logged-in user's ID
      else if (user?.userId) {
        handleFollowers(user.userId);
      }
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [user?.userId, otherUserID]);

  // handle follow and unfollow
  // Handle follow action
  const handleFollow = useCallback(
    async (id: string) => {
      if (!user?.isLogin) {
        setIsSigninOpen(true);
        return;
      }
      setLoadingOnClick((prev) => ({ ...prev, [id]: true })); // Stop loading only for the clicked user

      const userId = getUserId(user?.userData);

      const resp = await FollowerManager.getInstance().FollowByUserId({
        src_id: userId,
        dest_id: id,
      });
      // console.log(resp);
      if (resp == "success") {
        // console.log("Follow", isFollowing);
        setTriggerEffect(true);

        // Refresh the logged-in user's followings to update the follow/unfollow button status
        fetchLoggedInUserFollowings();

        setLoadingOnClick((prev) => ({ ...prev, [id]: false })); // Stop loading only for the clicked user
      }
    },
    [user]
  );

  // Handle follow action
  const handleUnFollow = useCallback(
    async (id: string) => {
      if (!user?.isLogin) {
        setIsSigninOpen(true);
        return;
      }
      setLoadingOnClick((prev) => ({ ...prev, [id]: true })); // Stop loading only for the clicked user

      const userId = getUserId(user?.userData);

      const resp = await FollowerManager.getInstance().UnfollowByUserId({
        src_id: userId,
        dest_id: id,
      });
      console.log(resp);
      if (resp == "success") {
        // console.log("un-Follow", isFollowing);
        setTriggerEffect(false);

        // Refresh the logged-in user's followings to update the follow/unfollow button status
        fetchLoggedInUserFollowings();

        setLoadingOnClick((prev) => ({ ...prev, [id]: false })); // Stop loading only for the clicked user
      }
    },
    [user]
  );
  return (
    <>
      <div className="overflow-y-scroll hide-scroll-custom bg-white h-[calc(100vh-280px)] max-md:h-[calc(100vh-107px)]">
        {!isDataLoaded ? (
          <div className="p-4">
            <ProfileCardSkeleton count={6} columns={2} showGrid={true} />
          </div>
        ) : followingList.length === 0 ? (
          <EmptyState
            type="followers"
            title="No followers yet"
            message={
              (otherUserID &&
                typeof otherUserID === "object" &&
                otherUserID.otherUserID &&
                otherUserID.otherUserID !== "my-profile" &&
                otherUserID.otherUserID !== user?.userId) ||
              (typeof otherUserID === "string" && otherUserID !== user?.userId)
                ? "This user doesn't have any followers yet."
                : "When people follow you, they'll appear here. Share your profile to get more followers!"
            }
            isOwnProfile={
              !(
                (otherUserID &&
                  typeof otherUserID === "object" &&
                  otherUserID.otherUserID &&
                  otherUserID.otherUserID !== "my-profile" &&
                  otherUserID.otherUserID !== user?.userId) ||
                (typeof otherUserID === "string" && otherUserID !== user?.userId)
              )
            }
            customIcon="/assets/empty-followers.svg"
          />
        ) : (
          <div className="grid grid-cols-2 max-md:grid-cols-1 gap-3">
            {followingList.map((item: any, index: number) => (
              <div key={index} className="w-full">
                <div className="row justify-between mt-3">
                  <Link href={user.userId == item?.id ? `/profile/amuzn/${item?.profile_name?.replace(/\s+/g, '-')}` : `/profile/amuzn/${item?.profile_name?.replace(/\s+/g, '-')}`}>
                    <div className="row gap-2">
                      <div className="">
                        {item?.categories?.[0] && item.categories[0].length > 0 ? (
                          <div>
                            {Object.entries(themes).map(([themeName, themeProperties]) => (
                              <div
                                key={themeName}
                                // className="w-[40px] h-[40px] rounded-full object-cover"
                                className="max-w-[40px] max-h-[40px]"
                              >
                                {(item.categories[0] == "Storytelling"
                                  ? "Literature"
                                  : item.categories[0]) === themeProperties.title && (
                                  <img
                                    src={
                                      generateFileUrl(item?.avatar) || "/assets/profileAvatar.svg"
                                    }
                                    alt="Profile"
                                    className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] max-w-[40px] max-h-[40px] rounded-full object-cover"
                                    style={{
                                      border: "3px solid",
                                      borderColor: themeProperties.backgroundColor,
                                    }}
                                  />
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <img
                            src={generateFileUrl(item?.avatar) || "/assets/profileAvatar.svg"}
                            alt="Profile"
                            className="w-[40px] h-[40px] min-w-[40px] min-h-[40px] max-w-[40px] max-h-[40px] rounded-full object-cover"
                            style={{
                              border: "3px solid",
                              borderColor: "#00",
                            }}
                          />
                        )}
                      </div>
                      <div>
                        <p className="font-bold max-md:font-semibold max-md:text-sm ">
                          {item.profile_name || "Profile Name*"}
                        </p>
                        <p className="text-[#616770] -mt-1 max-md:text-sm">
                          {item.location || "Location*"}
                        </p>
                      </div>
                    </div>
                  </Link>
                  {user.userId !== item.id && (
                    <div>
                      {!followersList.some((follower: any) => follower.id === item.id) ? (
                        <Badge
                          className={
                            loadingOnClick[item.id]
                              ? "btn-xs text-white w-20 cursor-not-allowed"
                              : "btn-xs text-white w-20"
                          }
                          onClick={() => {
                            handleFollow(item.id);
                          }}
                        >
                          {loadingOnClick[item.id] ? (
                            <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                          ) : (
                            "Follow"
                          )}
                        </Badge>
                      ) : (
                        <Badge
                          className={
                            loadingOnClick[item.id]
                              ? "btn-xs  w-20 cursor-not-allowed"
                              : "btn-xs  w-20"
                          }
                          variant="outline"
                          onClick={() => {
                            handleUnFollow(item.id);
                          }}
                        >
                          {loadingOnClick[item.id] ? (
                            <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                          ) : (
                            "Unfollow"
                          )}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="max-md:h-full px-5">
        <AlertDialog
          open={isSigninOpen}
          onOpenChange={(val: any) => {
            console.log(val, "valvalvalvalval");
            // setIsSigninOpen(val)
          }}
        >
          <AlertDialogTrigger asChild>
            {/* Empty or hidden trigger since we're controlling externally */}
            {/* <span style={{ display: "none" }}></span> */}
            {/* <p className="z-20">clo</p> */}
          </AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl  max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className=" max-md: overflow-scroll h-full hide-scroll ">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => {
                    sessionStorage.removeItem("input");
                    sessionStorage.removeItem("openPost");
                    setIsSigninOpen(false);
                  }}
                >
                  <X />
                </div>
                <AuthSignup onClose={() => setIsSigninOpen(false)} />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};

export default Following;
