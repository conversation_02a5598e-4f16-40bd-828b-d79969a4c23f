import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { collection, getDocs } from "firebase/firestore";
import { initFirebase } from "../../../firebaseConfig";

interface DataState {
  items: any[];
  status: "idle" | "loading" | "failed";
}

const initialState: DataState = {
  items: [],
  status: "idle",
};

// Async thunk to fetch data
export const fetchData = createAsyncThunk("data/fetchData", async () => {
  const {db} =  await initFirebase();
  const querySnapshot = await getDocs(collection(db, "your-collection"));
  return querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
});

const dataSlice = createSlice({
  name: "data",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchData.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchData.fulfilled, (state, action) => {
        state.status = "idle";
        state.items = action.payload;
      })
      .addCase(fetchData.rejected, (state) => {
        state.status = "failed";
      });
  },
});

export default dataSlice.reducer;
