"use client";
import React, { useState } from "react";

type RichText = {
  plain_text: string;
  annotations: {
    bold: boolean;
    italic: boolean;
    underline: boolean;
    strikethrough: boolean;
    code: boolean;
    color: string;
  };
  href: string | null;
};

// Color mapping for Notion colors
const getColorClasses = (color: string) => {
  const colorMap: { [key: string]: string } = {
    default: "text-gray-900",
    gray: "text-gray-500",
    brown: "text-amber-700",
    orange: "text-orange-600",
    yellow: "text-yellow-600",
    green: "text-green-600",
    blue: "text-blue-600",
    purple: "text-purple-600",
    pink: "text-pink-600",
    red: "text-red-600",
    gray_background: "bg-gray-100 text-gray-900 px-1 rounded",
    brown_background: "bg-amber-100 text-amber-900 px-1 rounded",
    orange_background: "bg-orange-100 text-orange-900 px-1 rounded",
    yellow_background: "bg-yellow-100 text-yellow-900 px-1 rounded",
    green_background: "bg-green-100 text-green-900 px-1 rounded",
    blue_background: "bg-blue-100 text-blue-900 px-1 rounded",
    purple_background: "bg-purple-100 text-purple-900 px-1 rounded",
    pink_background: "bg-pink-100 text-pink-900 px-1 rounded",
    red_background: "bg-red-100 text-red-900 px-1 rounded",
  };
  return colorMap[color] || colorMap.default;
};

function renderRichText(richTextArray: RichText[]) {
  if (!richTextArray || richTextArray.length === 0) return null;

  return richTextArray.map((text, index) => {
    let content = <>{text.plain_text}</>;
    let className = "";

    // Apply color styling
    if (text.annotations.color && text.annotations.color !== "default") {
      className += getColorClasses(text.annotations.color);
    }

    // Apply text formatting
    if (text.annotations.code) {
      content = (
        <code className="bg-gray-100 text-red-600 px-1.5 py-0.5 rounded text-sm font-mono">
          {content}
        </code>
      );
    }
    if (text.annotations.bold) {
      content = <strong className="font-semibold">{content}</strong>;
    }
    if (text.annotations.italic) {
      content = <em className="italic">{content}</em>;
    }
    if (text.annotations.underline) {
      content = <u className="underline">{content}</u>;
    }
    if (text.annotations.strikethrough) {
      content = <s className="line-through">{content}</s>;
    }

    if (text.href) {
      return (
        <a
          key={index}
          href={text.href}
          className="text-blue-600 underline hover:text-blue-800 transition-colors"
          target="_blank"
          rel="noopener noreferrer"
        >
          {content}
        </a>
      );
    }

    return (
      <span key={index} className={className}>
        {content}
      </span>
    );
  });
}

type Block = {
  id: string;
  type: string;
  has_children?: boolean;
  children?: Block[];
  [key: string]: any;
};

// Toggle/Accordion component for Notion toggle blocks
const ToggleBlock = ({ block, children }: { block: Block; children?: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const richText = block.toggle?.rich_text;

  // Determine if this is a heading toggle and get the appropriate styling
  const isHeadingToggle = block.type?.startsWith("heading_");
  const headingLevel = isHeadingToggle ? block.type : null;

  // Get heading-specific styles
  const getHeadingStyles = () => {
    switch (headingLevel) {
      case "heading_1":
        return "text-3xl font-bold text-gray-900 leading-tight";
      case "heading_2":
        return "text-2xl font-semibold text-gray-900 leading-tight";
      case "heading_3":
        return "text-xl font-medium text-gray-900 leading-tight";
      default:
        return "text-base font-medium text-gray-900";
    }
  };

  return (
    <div className="my-2">
      <div
        className={`flex items-center cursor-pointer hover:bg-gray-50 p-0 rounded transition-colors ${
          isHeadingToggle ? "py-3" : "py-1"
        }`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="mr-3 text-gray-500 flex-shrink-0">
          <svg
            className={`w-4 h-4 transition-transform duration-200 ${isOpen ? "rotate-90" : "rotate-0"}`}
            viewBox="0 0 16 16"
            fill="currentColor"
          >
            <path d="M6 4l4 4-4 4V4z" />
          </svg>
        </div>
        <div className={`flex-1 ${isHeadingToggle ? getHeadingStyles() : ""}`}>
          {renderRichText(richText)}
        </div>
      </div>
      {isOpen && (
        <div
          className={`mt-2 ${isHeadingToggle ? "ml-0" : "ml-0"} border-l-2 border-gray-100 pl-2`}
        >
          {children}
        </div>
      )}
    </div>
  );
};

// Group consecutive list items
const groupListItems = (blocks: Block[]) => {
  const grouped: (Block | Block[])[] = [];
  let currentList: Block[] = [];
  let currentListType: string | null = null;

  blocks.forEach((block) => {
    if (block.type === "bulleted_list_item" || block.type === "numbered_list_item") {
      if (currentListType === block.type) {
        currentList.push(block);
      } else {
        if (currentList.length > 0) {
          grouped.push([...currentList]);
        }
        currentList = [block];
        currentListType = block.type;
      }
    } else {
      if (currentList.length > 0) {
        grouped.push([...currentList]);
        currentList = [];
        currentListType = null;
      }
      grouped.push(block);
    }
  });

  if (currentList.length > 0) {
    grouped.push([...currentList]);
  }

  return grouped;
};

export default function NotionRenderer({ blocks }: { blocks: Block[] }) {
  // Debug: Log all block types to see what we're receiving
  React.useEffect(() => {
    if (blocks && blocks.length > 0) {
      console.log("");
    }
  }, [blocks]);

  const groupedBlocks = groupListItems(blocks);

  const renderBlock = (block: Block): React.ReactNode => {
    const richText = block[block.type]?.rich_text;

    switch (block.type) {
      case "paragraph":
        if (
          !richText ||
          richText.length === 0 ||
          richText.every((t: RichText) => !t.plain_text.trim())
        ) {
          return <div key={block.id} className="h-4" />; // Empty line spacing
        }
        return (
          <p key={block.id} className="text-gray-900 leading-relaxed my-2">
            {renderRichText(richText)}
          </p>
        );

      case "heading_1":
        // Check if this heading is toggleable
        if (block.heading_1?.is_toggleable && block.has_children) {
          // console.log("Rendering toggleable heading_1:", block);
          return (
            <div key={block.id} className="my-4">
              <ToggleBlock block={{ ...block, toggle: { rich_text: richText } }}>
                {block.children && <NotionRenderer blocks={block.children} />}
              </ToggleBlock>
            </div>
          );
        }
        return (
          <h1 key={block.id} className="text-3xl font-bold text-gray-900 mt-8 mb-4 leading-tight">
            {renderRichText(richText)}
          </h1>
        );

      case "heading_2":
        // Check if this heading is toggleable
        if (block.heading_2?.is_toggleable && block.has_children) {
          // console.log("Rendering toggleable heading_2:", block);
          return (
            <div key={block.id} className="my-4">
              <ToggleBlock block={{ ...block, toggle: { rich_text: richText } }}>
                {block.children && <NotionRenderer blocks={block.children} />}
              </ToggleBlock>
            </div>
          );
        }
        return (
          <h2
            key={block.id}
            className="text-2xl font-semibold text-gray-900 mt-6 mb-3 leading-tight"
          >
            {renderRichText(richText)}
          </h2>
        );

      case "heading_3":
        // Check if this heading is toggleable
        if (block.heading_3?.is_toggleable && block.has_children) {
          // console.log("Rendering toggleable heading_3:", block);
          return (
            <div key={block.id} className="my-4">
              <ToggleBlock block={{ ...block, toggle: { rich_text: richText } }}>
                {block.children && <NotionRenderer blocks={block.children} />}
              </ToggleBlock>
            </div>
          );
        }
        return (
          <h3 key={block.id} className="text-xl font-medium text-gray-900 mt-5 mb-2 leading-tight">
            {renderRichText(richText)}
          </h3>
        );

      case "bulleted_list_item":
        return (
          <li key={block.id} className="text-gray-900 leading-relaxed my-1">
            {renderRichText(richText)}
          </li>
        );

      case "numbered_list_item":
        return (
          <li key={block.id} className="text-gray-900 leading-relaxed my-1">
            {renderRichText(richText)}
          </li>
        );

      case "quote":
        return (
          <blockquote
            key={block.id}
            className="border-l-4 border-gray-300 pl-4 py-2 my-4 text-gray-700 italic bg-gray-50 rounded-r"
          >
            {renderRichText(richText)}
          </blockquote>
        );

      case "divider":
        return <hr key={block.id} className="border-gray-200 my-6" />;

      case "toggle":
        // console.log("Rendering toggle block:", block);
        return (
          <ToggleBlock key={block.id} block={block}>
            {block.children && <NotionRenderer blocks={block.children} />}
          </ToggleBlock>
        );

      case "column_list":
        return (
          <div key={block.id} className="flex gap-4 my-4">
            {block.children && <NotionRenderer blocks={block.children} />}
          </div>
        );

      case "column":
        return (
          <div key={block.id} className="flex-1">
            {block.children && <NotionRenderer blocks={block.children} />}
          </div>
        );

      case "child_page":
        return (
          <div key={block.id} className="border border-gray-200 rounded p-4 my-4 bg-gray-50">
            <div className="flex items-center">
              <span className="mr-2">📄</span>
              <span className="font-medium">{block.child_page?.title || "Untitled Page"}</span>
            </div>
          </div>
        );

      case "child_database":
        return (
          <div key={block.id} className="border border-gray-200 rounded p-4 my-4 bg-gray-50">
            <div className="flex items-center">
              <span className="mr-2">🗃️</span>
              <span className="font-medium">
                {block.child_database?.title || "Untitled Database"}
              </span>
            </div>
          </div>
        );

      case "embed":
        const embedUrl = block.embed?.url;
        return (
          <div key={block.id} className="my-4">
            <iframe
              src={embedUrl}
              className="w-full h-64 border border-gray-200 rounded"
              title="Embedded content"
            />
          </div>
        );

      case "pdf":
        const pdfUrl = block.pdf?.external?.url || block.pdf?.file?.url;
        return (
          <div key={block.id} className="my-4">
            <a
              href={pdfUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-red-50 border border-red-200 rounded hover:bg-red-100 transition-colors"
            >
              <span className="mr-2">📄</span>
              <span className="text-red-700 font-medium">View PDF</span>
            </a>
          </div>
        );

      case "code":
        const codeText = block.code?.rich_text?.map((t: RichText) => t.plain_text).join("") || "";
        return (
          <pre
            key={block.id}
            className="bg-gray-100 border border-gray-200 rounded p-4 my-4 overflow-x-auto"
          >
            <code className="text-sm font-mono text-gray-800">{codeText}</code>
          </pre>
        );

      case "callout":
        const calloutIcon = block.callout?.icon?.emoji || "💡";
        return (
          <div
            key={block.id}
            className="bg-gray-50 border border-gray-200 rounded p-4 my-4 flex items-start"
          >
            <span className="text-xl mr-3 mt-0.5">{calloutIcon}</span>
            <div className="flex-1 text-gray-900 leading-relaxed">
              {renderRichText(block.callout?.rich_text)}
            </div>
          </div>
        );

      case "image":
        const imageUrl = block.image?.external?.url || block.image?.file?.url;
        const caption = block.image?.caption;
        return (
          <div key={block.id} className="my-4">
            {imageUrl && (
              <img
                src={imageUrl}
                alt={caption ? renderRichText(caption)?.toString() : ""}
                className="max-w-full h-auto rounded border border-gray-200"
              />
            )}
            {caption && caption.length > 0 && (
              <p className="text-sm text-gray-600 mt-2 text-center italic">
                {renderRichText(caption)}
              </p>
            )}
          </div>
        );

      case "video":
        const videoUrl = block.video?.external?.url || block.video?.file?.url;
        const videoCaption = block.video?.caption;
        return (
          <div key={block.id} className="my-4">
            {videoUrl && (
              <video
                src={videoUrl}
                controls
                className="max-w-full h-auto rounded border border-gray-200"
              />
            )}
            {videoCaption && videoCaption.length > 0 && (
              <p className="text-sm text-gray-600 mt-2 text-center italic">
                {renderRichText(videoCaption)}
              </p>
            )}
          </div>
        );

      case "file":
        const fileUrl = block.file?.external?.url || block.file?.file?.url;
        const fileName = block.file?.name || "Download file";
        return (
          <div key={block.id} className="my-4">
            <a
              href={fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
            >
              <span className="mr-2">📎</span>
              <span className="text-blue-700 font-medium">{fileName}</span>
            </a>
          </div>
        );

      case "bookmark":
        const bookmarkUrl = block.bookmark?.url;
        const bookmarkCaption = block.bookmark?.caption;
        return (
          <div key={block.id} className="my-4">
            <a
              href={bookmarkUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 border border-gray-200 rounded hover:bg-gray-50 transition-colors"
            >
              <div className="text-blue-600 font-medium">{bookmarkUrl}</div>
              {bookmarkCaption && bookmarkCaption.length > 0 && (
                <div className="text-sm text-gray-600 mt-1">{renderRichText(bookmarkCaption)}</div>
              )}
            </a>
          </div>
        );

      case "table":
        const tableRows = block.children || [];
        return (
          <div key={block.id} className="my-4 overflow-x-auto">
            <table className="min-w-full border border-gray-200 rounded">
              <tbody>
                {tableRows.map((row: any, rowIndex: number) => (
                  <tr key={row.id || rowIndex} className="border-b border-gray-200">
                    {row.table_row?.cells?.map((cell: RichText[], cellIndex: number) => (
                      <td
                        key={cellIndex}
                        className="px-4 py-2 border-r border-gray-200 last:border-r-0"
                      >
                        {renderRichText(cell)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case "table_row":
        // Table rows are handled by the table case above
        return null;

      case "to_do":
        const isChecked = block.to_do?.checked || false;
        return (
          <div key={block.id} className="flex items-start my-2">
            <input type="checkbox" checked={isChecked} readOnly className="mt-1 mr-3 rounded" />
            <div className={`flex-1 ${isChecked ? "line-through text-gray-500" : "text-gray-900"}`}>
              {renderRichText(block.to_do?.rich_text)}
            </div>
          </div>
        );

      default:
        return (
          <div key={block.id} className="bg-yellow-50 border border-yellow-200 rounded p-3 my-2">
            <p className="text-yellow-800 text-sm">
              ❓ Unsupported block type:{" "}
              <code className="bg-yellow-100 px-1 rounded">{block.type}</code>
            </p>
            <details className="mt-2">
              <summary className="text-xs text-yellow-600 cursor-pointer">
                Show raw block data
              </summary>
              <pre className="text-xs mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(block, null, 2)}
              </pre>
            </details>
          </div>
        );
    }
  };

  return (
    <div className="notion-content max-w-none w-full px-2 py-2 font-sans text-base text-gray-900 leading-relaxed">
      <style jsx>{`
        .notion-content {
          line-height: 1.6;
          font-family:
            -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial,
            sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
        }
        .notion-content h1 {
          font-size: 2rem;
          font-weight: 700;
          margin: 2rem 0 1rem 0;
          line-height: 1.2;
        }
        .notion-content h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin: 1.5rem 0 0.75rem 0;
          line-height: 1.3;
        }
        .notion-content h3 {
          font-size: 1.25rem;
          font-weight: 500;
          margin: 1.25rem 0 0.5rem 0;
          line-height: 1.4;
        }
        .notion-content p {
          margin: 0.5rem 0;
        }
        .notion-content ul,
        .notion-content ol {
          margin: 0.5rem 0;
          padding-left: 1.5rem;
        }
        .notion-content li {
          margin: 0.25rem 0;
        }
        .notion-content blockquote {
          margin: 1rem 0;
          padding-left: 1rem;
          border-left: 3px solid #e5e7eb;
          font-style: italic;
          color: #6b7280;
        }
        .notion-content code {
          background-color: #f3f4f6;
          color: #dc2626;
          padding: 0.125rem 0.25rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          font-family:
            "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
        }
        .notion-content pre {
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          padding: 1rem;
          margin: 1rem 0;
          overflow-x: auto;
        }
        .notion-content pre code {
          background: none;
          color: #374151;
          padding: 0;
        }
      `}</style>
      {groupedBlocks.map((item, index) => {
        if (Array.isArray(item)) {
          // Render grouped list items
          const listType = item[0].type;
          const ListComponent = listType === "numbered_list_item" ? "ol" : "ul";
          const listClassName =
            listType === "numbered_list_item"
              ? "list-decimal space-y-1 my-3"
              : "list-disc space-y-1 my-3";

          return (
            <ListComponent key={`list-${index}`} className={listClassName}>
              {item.map((block) => renderBlock(block))}
            </ListComponent>
          );
        } else {
          // Render individual block
          return renderBlock(item);
        }
      })}
    </div>
  );
}
