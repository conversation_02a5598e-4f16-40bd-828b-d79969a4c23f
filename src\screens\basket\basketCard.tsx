"use client";
import * as React from "react";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ConfirmPayments } from "./confirmpayment";
const data = [
  {
    status: "New",
  },
  {
    status: "Completed",
  },
];

const BasketCard = () => {
  const [isSheetOpen, setSheetOpen] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<number | null>(null); // To store the selected user ID

  const handleUserClick = (id: any) => {
    setSelectedCardId(id);
    setSheetOpen(true);
  };
  return (
    <>
    {isSheetOpen && (
      <ConfirmPayments
        open={isSheetOpen}
        onOpenChange={setSheetOpen}
        cardId={selectedCardId}
      />)}
      <div className="h-[100vh] pb-40">
        {data.map((item, index) => {
          return (
            <Card
              className={
                selectedCardId == index && isSheetOpen
                  ? "w-full mt-3 p-0 border-b-2"
                  : "w-full mt-3 p-0 border-b-2"
              }
              style={{ padding: 0, border: "none", boxShadow: "none" }}
              // onClick={() => handleUserClick(index)}
            >
              <CardHeader style={{ padding: 0 }}>
                <CardDescription className="border-b-2 border-[#7C7C7C] pb-4">
                  <div>
                    <p className="text-base font-bold text-primary my-2">
                      LIVE MUSIC PERFORMANCE
                    </p>
                  </div>
                  <div className="row justify-between">
                    <p className="text-subtitle">Approximate time</p>
                    <p className="text-lg font-bold text-subtitle">2 hours</p>
                  </div>
                  <div className="row justify-between">
                    <p className="text-subtitle">Service subtotal</p>
                    <p className="text-lg text-subtitle">$100.00 </p>
                  </div>
                  <div className="row gap-3 mt-2">
                    <Badge
                      className=" btn-xs border-primary btn w-full"
                      variant="outline"
                    >
                      Edit
                    </Badge>
                    <Badge
                      className=" btn-xs border-primary btn w-full"
                      variant="outline"
                    >
                      Delete
                    </Badge>
                  </div>
                </CardDescription>
              </CardHeader>
            </Card>
          );
        })}

        <div className="mt-4 row gap-3">
          <div>
            <p>Switch</p>
          </div>
          <div>
            <div className="row justify-between">
              <p>Order subtotal</p>
              <p>$ 100.00</p>
            </div>
            <p>Excludes Transaction Fee (4%) </p>
            <p className="text-titleLabel font-bold my-2">
              Request specific delivery due date
            </p>
            <p className="text-[#969696]">
              Note: order due date is calculated based on the time required to
              deliver specific service(s). Here you can request a specific
              delivery due date for this order.
            </p>
          </div>
        </div>
        <div className="mt-4 px-[2px]">
          <Label
            htmlFor="message"
            className="text-primary text-base font-bold mb-1"
          >
            Order delivery details
          </Label>
          <Textarea
            placeholder="Order delivery details"
            id="message"
            className="text-primary text-base h-12"
          />
        </div>
        <div className="mt-6" onClick={() => handleUserClick(0)}>
          <Badge className=" btn-xs text-white btn py-4 ">Place order</Badge>
        </div>
      </div>
    </>
  );
};

export default BasketCard;
