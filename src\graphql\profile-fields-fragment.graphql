#import "../../common/graphql/common.graphql"
#import "./follow-module-fragment.graphql"

fragment HandleInfo on HandleInfo {
  __typename
  id
  fullHandle
  namespace
  localName
  suggestedFormatted {
    full
    localName
  }
  linkedTo {
    contract {
      ...NetworkAddressFields
    }
    nftTokenId
  }
  ownedBy
}

fragment ProfileFields on Profile {
  id
  ownedBy {
    ...NetworkAddressFields
  }
  signless
  sponsor
  txHash
  createdAt
  stats {
    id
    followers
    following
    comments
    posts
    mirrors
    quotes
    publications
    reactions
    countOpenActions
  }
  operations {
    id
    isBlockedByMe {
      value
      isFinalisedOnchain
    }
    isFollowedByMe {
      value
      isFinalisedOnchain
    }
    isFollowingMe {
      value
      isFinalisedOnchain
    }
    canBlock
    canUnblock
    canFollow
    canUnfollow
  }
  interests
  guardian {
    protected
    cooldownEndsOn
  }
  invitedBy {
    id
  }
  invitesLeft
  onchainIdentity {
    proofOfHumanity
    ens {
      name
    }
    sybilDotOrg {
      verified
      source {
        twitter {
          handle
        }
      }
    }
    worldcoin {
      isHuman
    }
  }
  followNftAddress {
    address
    chainId
  }
  metadata {
    displayName
    bio
    picture {
      ...ImageSetFields
    }
    coverPicture {
      ...ImageSetFields
    }
    attributes {
      ...MetadataAttributeFields
    }
    
  }
  followModule {
    ...FollowModuleFields
  }
  handle {
    ...HandleInfo
  }
}
