"use client";
import Browse from "@/screens/browse";

import { useParams } from "next/navigation";

export default function BrowsePage() {
  const params = useParams();
  const { categoryName }: any = params;
  const decodedCategoryName = decodeURIComponent(categoryName);

  // console.log(decodedCategoryName);

  return (
    <div>
      <Browse categoryName={decodedCategoryName} />
    </div>
  );
}
