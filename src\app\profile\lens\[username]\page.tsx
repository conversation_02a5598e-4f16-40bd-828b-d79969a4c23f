
"use client";

import { useSearchParams } from "next/navigation";
import Profile from "@/screens/profile";

interface LensProfilePageProps {
  params: {
    username: string;
  };
}

const LensProfilePage = ({ params }: LensProfilePageProps) => {

  return (
    <Profile 
      userId='test'
      profileType="lens"
      profile_name={params.username}
    />
  );
};

export default LensProfilePage;
