const STORAGE_KEY = "LH_STORAGE_KEY";

// check token is expired or not

export function isTokenExpired(exp: number) {
  if (!exp) return true;

  if (Date.now() <= exp * 1000) {
    // if (Date.now() >= exp * 1000 - 2 * 60 * 1000) {

    return false;
  }
  return true;
}

// 1. Reading the access token from storage
export function readAccessToken() {
  // Make sure we're on a client-side environment
  if (typeof window === "undefined") return null;

  const ls = localStorage || window.localStorage;

  if (!ls) {
    throw new Error("LocalStorage is not available");
  }

  const data = ls.getItem(STORAGE_KEY);

  if (!data) return null;

  const resp = JSON.parse(data) as {
    accessToken: string;
    exp: number;
    identityToken: string;
    refreshToken: string;
  };
  // console.log({exp : resp.exp ,fn : isTokenExpired(resp.exp) });

  // if (isTokenExpired(resp.exp)) {
  //   return null;
  // }

  return JSON.parse(data) as {
    accessToken: string;
    exp: number;
    identityToken: string;
    refreshToken: string;
  };
}

// 2. Setting the  access token in storage
export function setAccessToken(
  accessToken: string,
  identityToken: string,
  refreshToken: string
) {
  const tt = parseJwt(accessToken);

  const ls = localStorage || window.localStorage;

  if (!ls) {
    throw new Error("LocalStorage is not available");
  }

  const resp = ls.setItem(
    STORAGE_KEY,
    JSON.stringify({ accessToken, identityToken, refreshToken, exp: tt.exp })
  );
}
const decoded = (str: string): string =>
  Buffer.from(str, "base64").toString("binary");

// 3. Parse the JWT token that comes back and extract the exp date field
function parseJwt(token: string) {
  return JSON.parse(decoded(token.split(".")[1]));
}
