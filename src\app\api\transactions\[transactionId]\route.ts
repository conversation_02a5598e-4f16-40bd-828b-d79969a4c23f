import { NextRequest, NextResponse } from "next/server";
import { getTransaction } from "@/services/transactionService";
import { updateOrder, getOrderById } from "@/services/ordersServices";
import Strip<PERSON> from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    const { transactionId } = await params;

    console.log("🔍 ===== TRANSACTION API CALLED =====");
    console.log("📋 Transaction ID:", transactionId);
    console.log("🕐 Timestamp:", new Date().toISOString());

    if (!transactionId) {
      console.log("❌ No transaction ID provided");
      return NextResponse.json({ error: "Transaction ID is required" }, { status: 400 });
    }

    console.log("🔄 Fetching transaction from Firebase...");
    // Get transaction from Firebase
    const transactionResult = await getTransaction(transactionId);

    console.log("📊 Firebase Transaction Result:", {
      success: transactionResult.success,
      hasTransaction: !!transactionResult.transaction,
      transactionData: transactionResult.transaction
        ? {
            id: transactionResult.transaction.id,
            orderId: transactionResult.transaction.orderId,
            stripeSessionId: transactionResult.transaction.stripeSessionId,
            stripePaymentIntentId: transactionResult.transaction.stripePaymentIntentId,
            amount: transactionResult.transaction.amount,
            status: transactionResult.transaction.status,
          }
        : null,
    });

    if (!transactionResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: transactionResult.error || "Transaction not found",
        },
        { status: 404 }
      );
    }

    const transaction = transactionResult.transaction;

    if (!transaction) {
      return NextResponse.json(
        {
          success: false,
          error: "Transaction data is null",
        },
        { status: 404 }
      );
    }

    // Get additional Stripe data if available
    let stripeSession = null;
    let paymentIntent = null;

    try {
      console.log("🔄 Fetching Stripe data...");
      console.log("🎫 Stripe Session ID:", transaction.stripeSessionId);
      console.log("💳 Stripe Payment Intent ID:", transaction.stripePaymentIntentId);

      // Get session details if session ID exists
      if (transaction.stripeSessionId) {
        console.log("🔄 Retrieving Stripe session...");
        stripeSession = await stripe.checkout.sessions.retrieve(transaction.stripeSessionId, {
          expand: ["payment_intent", "customer"],
        });
        console.log("✅ Stripe Session Retrieved:", {
          id: stripeSession.id,
          payment_status: stripeSession.payment_status,
          amount_total: stripeSession.amount_total,
          currency: stripeSession.currency,
          customer: stripeSession.customer,
          payment_intent: stripeSession.payment_intent,
        });
      }

      // Get payment intent if available
      if (transaction.stripePaymentIntentId) {
        console.log("🔄 Retrieving Payment Intent by ID...");
        paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId, {
          expand: ["charges.data"],
        });
      } else if (stripeSession?.payment_intent) {
        // Get payment intent from session
        const piId =
          typeof stripeSession.payment_intent === "string"
            ? stripeSession.payment_intent
            : stripeSession.payment_intent.id;

        console.log("🔄 Retrieving Payment Intent from session:", piId);
        paymentIntent = await stripe.paymentIntents.retrieve(piId, {
          expand: ["charges.data"],
        });
      }

      if (paymentIntent) {
        console.log("✅ Payment Intent Retrieved:", {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          amount_capturable: paymentIntent.amount_capturable,
          amount_received: paymentIntent.amount_received,
          currency: paymentIntent.currency,
          latest_charge: paymentIntent.latest_charge,
          charges_count: (paymentIntent as any).charges?.data?.length || 0,
          capture_method: paymentIntent.capture_method,
          customer: paymentIntent.customer,
          payment_method: paymentIntent.payment_method,
          receipt_email: paymentIntent.receipt_email,
        });

        if ((paymentIntent as any).charges?.data?.length > 0) {
          console.log("💳 CHARGES FOUND:", (paymentIntent as any).charges.data.length);
          (paymentIntent as any).charges.data.forEach((charge: any, index: number) => {
            console.log(`🎯 CHARGE ${index + 1} DETAILS:`, {
              id: charge.id,
              status: charge.status,
              amount: charge.amount,
              amount_captured: charge.amount_captured,
              amount_refunded: charge.amount_refunded,
              currency: charge.currency,
              paid: charge.paid,
              captured: charge.captured,
              refunded: charge.refunded,
              created: new Date(charge.created * 1000).toISOString(),
              payment_method: charge.payment_method,
              receipt_email: charge.receipt_email,
              receipt_url: charge.receipt_url,
            });

            if (charge.payment_method_details?.card) {
              console.log(`💳 CARD DETAILS for ${charge.id}:`, {
                brand: charge.payment_method_details.card.brand,
                last4: charge.payment_method_details.card.last4,
                exp_month: charge.payment_method_details.card.exp_month,
                exp_year: charge.payment_method_details.card.exp_year,
                country: charge.payment_method_details.card.country,
              });
            }

            if (charge.billing_details) {
              console.log(`📍 BILLING DETAILS for ${charge.id}:`, {
                name: charge.billing_details.name,
                email: charge.billing_details.email,
                phone: charge.billing_details.phone,
                address: charge.billing_details.address,
              });
            }

            if (charge.outcome) {
              console.log(`🔒 OUTCOME for ${charge.id}:`, charge.outcome);
            }
          });
        } else {
          console.log("⚠️ NO CHARGES FOUND in payment intent");
        }
      } else {
        console.log("⚠️ NO PAYMENT INTENT FOUND");
      }
    } catch (stripeError) {
      console.error("❌ ERROR fetching Stripe data:", stripeError);
      // Continue without Stripe data
    }

    return NextResponse.json({
      success: true,
      transaction,
      stripeSession: stripeSession
        ? {
            id: stripeSession.id,
            amount_total: stripeSession.amount_total,
            currency: stripeSession.currency,
            customer: stripeSession.customer,
            payment_status: stripeSession.payment_status,
            payment_intent: stripeSession.payment_intent,
            created: stripeSession.created,
            expires_at: stripeSession.expires_at,
          }
        : null,
      paymentIntent: paymentIntent
        ? {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount,
            amount_capturable: paymentIntent.amount_capturable,
            amount_received: paymentIntent.amount_received,
            currency: paymentIntent.currency,
            latest_charge: paymentIntent.latest_charge,
            charges: (paymentIntent as any).charges,
            created: paymentIntent.created,
            capture_method: paymentIntent.capture_method,
          }
        : null,
    });
  } catch (error) {
    console.error("Error fetching transaction:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch transaction",
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    const { transactionId } = await params;

    console.log("🔄 ===== PAYMENT SUCCESS PROCESSING =====");
    console.log("📋 Transaction ID:", transactionId);
    console.log("🕐 Timestamp:", new Date().toISOString());

    if (!transactionId) {
      console.log("❌ No transaction ID provided");
      return NextResponse.json({ error: "Transaction ID is required" }, { status: 400 });
    }

    // Step 1: Get transaction from Firebase to get order ID
    console.log("🔄 Fetching transaction from Firebase...");
    const transactionResult = await getTransaction(transactionId);

    if (!transactionResult.success || !transactionResult.transaction) {
      console.log("❌ Transaction not found");
      return NextResponse.json(
        {
          success: false,
          error: "Transaction not found",
        },
        { status: 404 }
      );
    }

    const transaction = transactionResult.transaction!;
    console.log("✅ Transaction found:", {
      id: transaction.id,
      orderId: transaction.orderId,
      stripePaymentIntentId: transaction.stripePaymentIntentId,
      status: transaction.status,
    });

    if (!transaction.orderId) {
      console.log("❌ No order ID found in transaction");
      return NextResponse.json(
        {
          success: false,
          error: "No order ID found in transaction",
        },
        { status: 400 }
      );
    }

    // Step 1.5: Check if payment details are already stored in the order
    console.log("🔍 Checking if payment details already exist in order...");
    const orderResult = await getOrderById(transaction.orderId);

    if (!orderResult.success || !orderResult.order) {
      console.log("❌ Order not found");
      return NextResponse.json(
        {
          success: false,
          error: "Order not found",
        },
        { status: 404 }
      );
    }

    const order = orderResult.order;

    // Check if chargeId already exists - if yes, skip ALL database updates
    if (order.chargeId) {
      console.log("✅ chargeId already exists in order, skipping ALL database updates");
      console.log("💾 Existing payment data:", {
        chargeId: order.chargeId,
        payment_intent_id: (order as any).payment_intent_id,
        transactionId: order.transactionId,
      });

      return NextResponse.json({
        success: true,
        message: "chargeId already exists, no database updates performed",
        data: {
          orderId: transaction.orderId,
          transactionId,
          chargeId: order.chargeId,
          paymentIntentId: (order as any).payment_intent_id,
          alreadyExists: true,
          skippedReason: "chargeId_exists",
        },
      });
    }

    console.log("🔄 No existing payment details found, proceeding with update...");

    // Step 2: Get payment intent details from Stripe
    let paymentIntent = null;
    let latestChargeId = null;
    let paymentIntentId = null;

    try {
      // Try to get payment intent from transaction first
      if (transaction.stripePaymentIntentId) {
        console.log("🔄 Retrieving Payment Intent from transaction...");
        paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId, {
          expand: ["charges.data"],
        });
      }
      // If no payment intent ID in transaction, try to get it from session
      else if (transaction.stripeSessionId) {
        console.log("🔄 No payment intent in transaction, trying to get from session...");
        const session = await stripe.checkout.sessions.retrieve(transaction.stripeSessionId, {
          expand: ["payment_intent"],
        });

        console.log("📋 Session Details:", {
          id: session.id,
          payment_status: session.payment_status,
          payment_intent: session.payment_intent,
        });

        if (session.payment_intent) {
          const piId =
            typeof session.payment_intent === "string"
              ? session.payment_intent
              : session.payment_intent.id;

          console.log("🔄 Retrieving Payment Intent from session:", piId);
          paymentIntent = await stripe.paymentIntents.retrieve(piId, {
            expand: ["charges.data"],
          });
        }
      }

      if (paymentIntent) {
        paymentIntentId = paymentIntent.id;
        latestChargeId = paymentIntent.latest_charge as string;

        console.log("✅ Payment Intent Retrieved:", {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          latest_charge: paymentIntent.latest_charge,
          charges_count: (paymentIntent as any).charges?.data?.length || 0,
        });

        // Log charge details if available
        if ((paymentIntent as any).charges?.data?.length > 0) {
          const latestCharge = (paymentIntent as any).charges.data[0];
          console.log("💳 Latest Charge Details:", {
            id: latestCharge.id,
            status: latestCharge.status,
            amount: latestCharge.amount,
            paid: latestCharge.paid,
            captured: latestCharge.captured,
          });
        }
      } else {
        console.log("⚠️ No payment intent found in transaction or session");
        return NextResponse.json(
          {
            success: false,
            error: "No payment intent found in transaction or session",
          },
          { status: 400 }
        );
      }
    } catch (stripeError) {
      console.error("❌ Error fetching Stripe data:", stripeError);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch payment data from Stripe",
        },
        { status: 500 }
      );
    }

    // Step 3: Update order with payment details
    console.log("🔄 Updating order with payment details...");
    const updateData: any = {};

    if (latestChargeId) {
      updateData.chargeId = latestChargeId;
      console.log("💳 Setting chargeId:", latestChargeId);
    }

    if (paymentIntentId) {
      updateData.payment_intent_id = paymentIntentId;
      console.log("🎯 Setting payment_intent_id:", paymentIntentId);
    }

    if (transactionId) {
      updateData.transactionId = transactionId;
      console.log("📋 Setting transactionId:", transactionId);
    }

    const updateResult = await updateOrder(transaction.orderId, updateData);

    if (!updateResult.success) {
      console.error("❌ Failed to update order:", updateResult.error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update order with payment details",
        },
        { status: 500 }
      );
    }

    console.log("✅ Order updated successfully with payment details");

    return NextResponse.json({
      success: true,
      message: "Payment details stored successfully",
      data: {
        orderId: transaction.orderId,
        transactionId,
        chargeId: latestChargeId,
        paymentIntentId,
        paymentStatus: paymentIntent?.status,
        amount: paymentIntent?.amount,
        currency: paymentIntent?.currency,
      },
    });
  } catch (error) {
    console.error("❌ Error processing payment success:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to process payment success",
      },
      { status: 500 }
    );
  }
}
