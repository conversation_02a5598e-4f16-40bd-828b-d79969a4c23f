/**
 * Client-side authentication utilities for handling user data from localStorage
 * Production-ready implementation for real user authentication
 */

export interface ClientUser {
  uid: string;
  email?: string;
  displayName?: string;
  emailVerified?: boolean;
}

/**
 * Get the authenticated user ID from localStorage
 * Returns null if no user is found or user data is invalid
 */
export function getAuthenticatedUserId(): string | null {
  try {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      console.log('No user data found in localStorage');
      return null;
    }

    const user = JSON.parse(storedUser);
    if (!user || !user.uid || typeof user.uid !== 'string' || user.uid.length < 10) {
      console.warn('Invalid user data in localStorage:', user);
      return null;
    }

    console.log('Retrieved authenticated user ID:', user.uid);
    return user.uid;
  } catch (error) {
    console.error('Error getting user from localStorage:', error);
    return null;
  }
}

/**
 * Get the full authenticated user object from localStorage
 * Returns null if no user is found or user data is invalid
 */
export function getAuthenticatedUser(): ClientUser | null {
  try {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      console.log('No user data found in localStorage');
      return null;
    }

    const user = JSON.parse(storedUser);
    if (!user || !user.uid || typeof user.uid !== 'string' || user.uid.length < 10) {
      console.warn('Invalid user data in localStorage:', user);
      return null;
    }

    console.log('Retrieved authenticated user:', user.uid);
    return {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      emailVerified: user.emailVerified
    };
  } catch (error) {
    console.error('Error getting user from localStorage:', error);
    return null;
  }
}

/**
 * Create headers object with authentication for API requests
 * Returns headers object with x-user-id if user is authenticated
 */
export function getAuthHeaders(): Record<string, string> {
  const userId = getAuthenticatedUserId();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  if (userId) {
    headers['x-user-id'] = userId;
  }

  return headers;
}

/**
 * Check if user is authenticated
 * Returns true if valid user data exists in localStorage
 */
export function isAuthenticated(): boolean {
  return getAuthenticatedUserId() !== null;
}

/**
 * Clear user authentication data from localStorage
 */
export function clearAuthentication(): void {
  try {
    localStorage.removeItem('user');
    console.log('User authentication data cleared');
  } catch (error) {
    console.error('Error clearing authentication data:', error);
  }
}
