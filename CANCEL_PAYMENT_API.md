# Cancel Payment API Documentation

## Overview
The Cancel Payment API allows you to cancel authorized payment intents in your escrow system. This is useful when you need to cancel a payment before it's captured, such as when an order is cancelled or there's an issue with the transaction.

## Endpoint
- **URL**: `/api/stripe/cancel`
- **Methods**: `POST`, `GET`
- **Description**: Cancel an authorized payment intent

## Supported Payment Intent Statuses
You can cancel a PaymentIntent when it's in one of these statuses:
- `requires_payment_method`
- `requires_capture` (most common for escrow)
- `requires_confirmation`
- `requires_action`
- `processing` (rare cases)

## Request Parameters

### Multiple Ways to Identify Payment
You can provide **any one** of the following to identify the payment to cancel:

1. **Payment Intent ID** (Direct)
   ```json
   {
     "paymentIntentId": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
   }
   ```

2. **Charge ID** (Auto-resolves to Payment Intent)
   ```json
   {
     "chargeId": "ch_3MtwBwLkdIwHu7ix28a3tqPa"
   }
   ```

3. **Transaction ID** (Auto-resolves to Payment Intent)
   ```json
   {
     "transactionId": "EmNgsYGATgJ0pOo4JC25"
   }
   ```

### Optional Parameters
- `cancellationReason` (string, optional): Reason for cancellation
  - `requested_by_customer`
  - `duplicate`
  - `fraudulent`
  - `abandoned`
- `currency` (string, optional): Currency code (e.g., "usd", "gbp")
- `isUS` (boolean, optional): Force US Stripe instance usage

## Examples

### Cancel by Payment Intent ID
```bash
curl -X POST /api/stripe/cancel \
  -H "Content-Type: application/json" \
  -d '{
    "paymentIntentId": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "cancellationReason": "requested_by_customer"
  }'
```

### Cancel by Transaction ID (Escrow)
```bash
curl -X POST /api/stripe/cancel \
  -H "Content-Type: application/json" \
  -d '{
    "transactionId": "EmNgsYGATgJ0pOo4JC25",
    "cancellationReason": "abandoned"
  }'
```

### Cancel by Charge ID
```bash
curl -X POST /api/stripe/cancel \
  -H "Content-Type: application/json" \
  -d '{
    "chargeId": "ch_3MtwBwLkdIwHu7ix28a3tqPa",
    "currency": "gbp"
  }'
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Payment intent canceled successfully",
  "paymentIntent": {
    "id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "status": "canceled",
    "originalAmount": 2000,
    "originalAmountFormatted": "20.00 USD",
    "currency": "usd",
    "canceled_at": 1680801569,
    "cancellation_reason": "requested_by_customer",
    "capture_method": "manual",
    "latest_charge": null
  },
  "refundInfo": {
    "message": "Payment was authorized but not captured. The remaining amount_capturable has been automatically refunded.",
    "refundedAmount": 2000,
    "refundedAmountFormatted": "20.00 USD"
  },
  "resolvedFrom": {
    "paymentIntentId": "direct",
    "chargeId": null,
    "transactionId": null,
    "resolvedPaymentIntentId": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
  },
  "stripeInstance": {
    "isUS": false,
    "currency": "usd"
  },
  "timestamp": "2024-04-06T10:30:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Cannot cancel payment intent with status 'succeeded'. Cancelable statuses are: requires_payment_method, requires_capture, requires_confirmation, requires_action, processing",
  "paymentIntent": {
    "id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "status": "succeeded",
    "amount": 2000,
    "amountFormatted": "20.00 USD",
    "currency": "usd"
  }
}
```

## Integration with Escrow System

### Typical Escrow Flow with Cancellation
1. **Create Escrow**: `/api/escrow/create` → Get `transactionId` and `paymentIntentId`
2. **User Authorizes Payment**: User completes checkout → Payment status becomes `requires_capture`
3. **Cancel if Needed**: `/api/stripe/cancel` with `transactionId` → Payment cancelled and refunded
4. **OR Continue with Capture**: `/api/escrow/capture-and-release` → Capture and release first stage

### When to Use Cancel vs Refund
- **Cancel**: Use when payment is authorized but not yet captured (`requires_capture` status)
- **Refund**: Use when payment has been captured and you need to return money (`succeeded` status)

## UI Integration

The cancel functionality is available in:
1. **Escrow Management Page** (`/escrow`) - Dedicated cancel payment section
2. **Payment Tester Page** (`/payment`) - Under "Escrow" tab

Both interfaces support all three identification methods (Payment Intent ID, Charge ID, Transaction ID).

## Error Handling

Common error scenarios:
- Payment intent not found
- Payment intent already canceled
- Payment intent in non-cancelable status
- Invalid transaction/charge ID
- Network/Stripe API errors

All errors include detailed messages and relevant context for debugging.
