"use client";
import useAuth from "@/hook";
import { createPost, Post } from "@/services/postService";
import { arrayUnion, Timestamp } from "firebase/firestore";
import { useRef, useState, useEffect } from "react";
import { Check, FilePlus, Loader, Trash, X } from "react-feather";
import { createUser, updateUser } from "@/services/usersServices";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { initFirebase } from "../../../../../../firebaseConfig";
import LocationDropdown from "@/components/LocationDropdown";
import { Input } from "@/components/ui/input";

const CreatePost = (props: any) => {
  const [category, setCategory] = useState<string | null>(null);
  const [about, setAbout] = useState("");
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [geoTags, setGeoTags] = useState<string[]>([]);
  const [media, setMedia] = useState<File | null>(null);
  const [isMedia, setIsMedia] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");

  // Google location API states
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceLoaded = useRef(false);
  const [placePredictions, setPlacePredictions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showPlacesDropdown, setShowPlacesDropdown] = useState(false);
  const locationInputRef = useRef<HTMLInputElement>(null);
  const [isPersonalInfoSaving, setIsPersonalInfoSaving] = useState(false);
  const [personalInfoSaveSuccess, setPersonalInfoSaveSuccess] = useState(false);
  const [locationInput, setLocationInput] = useState("");
  const [location, setLocation] = useState("");
  const [hashtagInput, setHashtagInput] = useState("");

  // Handle location input change and fetch place predictions
  const handleLocationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocationInput(value);
    setLocation(value);

    if (value.trim() === "") {
      setPlacePredictions([]);
      setShowPlacesDropdown(false);
      return;
    }

    if (autocompleteService.current && placesServiceLoaded.current) {
      autocompleteService.current.getPlacePredictions(
        {
          input: value,
          types: ["(cities)"],
        },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setPlacePredictions(predictions);
            setShowPlacesDropdown(true);
          } else {
            setPlacePredictions([]);
            setShowPlacesDropdown(false);
          }
        }
      );
    }
  };

  // Select a place from the dropdown
  const selectPlace = (place: google.maps.places.AutocompletePrediction) => {
    if (!geoTags.includes(place.description)) {
      setGeoTags([...geoTags, place.description]);
    }
    setLocationInput("");
    setShowPlacesDropdown(false);
  };

  // Initialize Google Maps API
  useEffect(() => {
    if (window.google && window.google.maps && !placesServiceLoaded.current) {
      placesServiceLoaded.current = true;
      autocompleteService.current = new google.maps.places.AutocompleteService();
    }
  }, []);

  // Handle post creation
  const auth = useAuth();

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === "success") {
      // Wait 1.5 seconds before closing the modal to show success state
      const timer = setTimeout(() => {
        props.setIsOpen(false);
        props.setIsRefatch((e: boolean) => !e);
        props.setToggleMain((e: boolean) => !e);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, props]);

  const handleSubmit = async () => {
    setLoading(true);
    setUploadStatus("uploading");

    if (!category || !media) {
      setLoading(false);
      setUploadStatus("idle");
      alert("Please fill out all fields.");
      return;
    }

    const { storage } = await initFirebase();

    try {
      // Upload media to Firebase Storage
      const storageRef = ref(storage, `images/${Date.now()}_${media.name}`);
      const uploadTaskSnapshot = await uploadBytes(storageRef, media);
      const mediaURL = await getDownloadURL(uploadTaskSnapshot.ref);

      const post: Post = {
        // id: auth?.userData?.uid,
        category: category || "Uncategorized", // Default to Uncategorized if no category
        about_project: about,
        hashtags,
        geotags: geoTags,
        postFile: mediaURL,
        mediaType: media.type.includes("image") ? "image" : "video",
        starred: 0, // Initial value for stars, could be updated later
        added_at: Timestamp.fromDate(new Date()),
        user_id: auth.userId,
      };

      // Call createPost for the single post
      const response = await createPost(post);

      // Check if the post was created successfully
      if (response.success) {
        const postId = response.id; // Assuming `response.id` contains the created post's ID
        const userId = auth?.userData?.uid; // Assuming `auth.userData.uid` contains the current user's ID

        if (userId && postId) {
          await updateUser(userId, { posts: arrayUnion(postId) });
          // Set success state - modal will close after delay via useEffect
          setUploadStatus("success");
        }
      } else {
        setUploadStatus("idle");
        setLoading(false);
        console.error("Error creating post:", response);
      }
    } catch (error) {
      setUploadStatus("idle");
      setLoading(false);
      console.error("Error creating post:", error);
    }
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setMedia(event.target.files[0]);
      setIsMedia(true);
    }
  };

  const handleHashtagSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (hashtagInput.trim()) {
      setHashtags([...hashtags, hashtagInput.trim()]);
      setHashtagInput("");
    }
  };

  const handleHashtagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.keyCode === 13)) {
      e.preventDefault();
      handleHashtagSubmit(e);
    }
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter((hashtag) => hashtag !== tag));
  };

  const addGeoTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.keyCode === 13) && e.currentTarget.value.trim()) {
      e.preventDefault();
      setGeoTags([...geoTags, e.currentTarget.value.trim()]);
      e.currentTarget.value = ""; // Clear input
    }
  };

  const removeGeoTag = (tag: string) => {
    setGeoTags(geoTags.filter((geoTag) => geoTag !== tag));
  };

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === "success") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">Post Uploaded Successfully!</h2>
          <p className="text-gray-500">
            Your post has been created and will be visible on your profile.
          </p>
        </div>
      );
    }

    if (uploadStatus === "uploading") {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Uploading Your Post...</h2>
          <p className="text-gray-500">
            Please wait while we upload your post. This may take a moment.
          </p>
        </div>
      );
    }

    return (
      <>
        <div className="flex flex-row justify-between items-center mb-4 sticky top-0 bg-white z-[9999]">
          <div
            onClick={() => !loading && props.setIsOpen(false)}
            className={loading ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
          >
            <X />
          </div>
          <h2 className="text-xl font-bold">Create Posts</h2>
          <p
            onClick={() =>
              !loading && category && about && hashtags.length > 0 && geoTags.length > 0 && media
                ? handleSubmit()
                : ""
            }
            className={
              !loading && category && about && hashtags.length > 0 && geoTags.length > 0 && media
                ? "font-bold text-primary cursor-pointer"
                : "font-bold text-borderColor cursor-not-allowed"
            }
          >
            Save
          </p>
        </div>
      </>
    );
  };

  return (
    <>
      {renderContent()}
      {uploadStatus === "idle" && (
        <div className=" bg-gray-50">
          <div className=" bg-white  rounded-md p-2 py-4">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (!loading) handleSubmit();
              }}
              className="space-y-6"
            >
              {/* Choose Category */}
              <div>
                <p className="text-primary mb-2 font-[600] text-start">Choose category</p>
                <div className="flex space-x-2">
                  {props.category.map((cat: any, index: any) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => !loading && setCategory(cat)}
                      className={`px-4 py-2 rounded-md ${
                        category === cat ? "bg-primary text-white" : "bg-[#EEEEEE]  text-primary"
                      } ${loading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
                      disabled={loading}
                    >
                      {cat}
                    </button>
                  ))}
                </div>

                <div></div>
              </div>

              {/* About Project */}
              <div>
                <label htmlFor="about" className="text-primary mb-2 font-[600] block text-start">
                  About Project
                </label>
                <textarea
                  id="about"
                  value={about}
                  onChange={(e) => !loading && setAbout(e.target.value)}
                  placeholder="What was this project about?"
                  className="w-full p-2 border border-gray-300 rounded-md"
                  disabled={loading}
                ></textarea>
                <p className="text-sm text-gray-500"></p>
              </div>

              {/* Media */}
              <div>
                <label className="text-primary mb-2 font-[600] block text-start">Media</label>
                {!isMedia ? (
                  <div className="w-full ">
                    <label
                      htmlFor="media-upload"
                      className={`row gap-4 ${
                        loading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                      }`}
                    >
                      <span className="bg-primary text-white p-2 rounded-xl h-[60px] w-[100px] row items-center justify-center">
                        <FilePlus />
                      </span>
                      <div className="flex flex-col">
                        <span className="text-subtitle text-sm text-start">
                          File size max 4MB per photo
                        </span>
                        <span className="text-subtitle mt-1 text-sm text-start">
                          File size max 8MB / 15 seconds per video
                        </span>
                      </div>
                      <input
                        type="file"
                        id="media-upload"
                        className="hidden"
                        accept="image/*,video/*"
                        onChange={handleMediaUpload}
                        disabled={loading}
                      />
                    </label>
                  </div>
                ) : (
                  media && (
                    <div className="mt-2 relative">
                      <img
                        src={URL.createObjectURL(media)}
                        alt="Media Preview"
                        className="w-full h-auto rounded-md max-h-[300px] "
                      />
                      <div
                        className={`bg-white p-2 text-primary rounded-full absolute top-2 right-2 ${
                          loading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                        }`}
                        onClick={() => {
                          if (!loading) setIsMedia(false);
                        }}
                      >
                        <Trash />
                      </div>
                    </div>
                  )
                )}
              </div>

              {/* Hashtags */}
              <div>
                <label className="text-primary mb-2 font-[600] block text-start">
                  Hashtags<span className="text-primary">*</span>
                </label>
                <div className="border-2 rounded-lg p-3">
                  <div className="flex flex-wrap gap-2 mb-2">
                    {hashtags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => !loading && removeHashtag(tag)}
                          className={`text-primary pt-[2px] pl-2 ${
                            loading ? "cursor-not-allowed opacity-50" : ""
                          }`}
                          disabled={loading}
                        >
                          <X size={15} strokeWidth="3px" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <form onSubmit={handleHashtagSubmit} className="m-0 p-0">
                    <input
                      type="text"
                      placeholder="Enter hashtags here"
                      className="w-full pt-2 border-none border-gray-300 rounded-md outline-none"
                      value={hashtagInput}
                      onChange={(e) => !loading && setHashtagInput(e.target.value)}
                      onKeyDown={!loading ? handleHashtagKeyDown : undefined}
                      disabled={loading}
                      enterKeyHint="done"
                    />
                  </form>
                </div>
                <p className="text-sm text-gray-500">
                  Hashtag is a word, which allows users to discover your posts and services.
                </p>
              </div>

              {/* Geo-tags with Google Autocomplete */}
              <div>
                <label className="text-primary mb-1 font-[600] block text-start">Geo-tag</label>
                <div className="border-2 rounded-lg p-3">
                  <div className="flex flex-wrap gap-2 mb-2">
                    {geoTags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => !loading && removeGeoTag(tag)}
                          className={`text-primary pt-[2px] pl-2 ${
                            loading ? "cursor-not-allowed opacity-50" : ""
                          }`}
                          disabled={loading}
                        >
                          <X size={15} strokeWidth="3px" />
                        </button>
                      </span>
                    ))}
                  </div>

                  <div className="relative">
                    <Input
                      ref={locationInputRef}
                      placeholder="Start typing location name..."
                      className={`resize-none h-[40px] outline-none text-lg text-primary ${
                        isPersonalInfoSaving || personalInfoSaveSuccess || loading
                          ? "opacity-70 cursor-not-allowed"
                          : ""
                      }`}
                      value={locationInput}
                      onChange={
                        !isPersonalInfoSaving && !personalInfoSaveSuccess && !loading
                          ? handleLocationInputChange
                          : undefined
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && locationInput.trim() && !loading) {
                          e.preventDefault();
                          if (!geoTags.includes(locationInput.trim())) {
                            setGeoTags([...geoTags, locationInput.trim()]);
                          }
                          setLocationInput("");
                          setShowPlacesDropdown(false);
                        }
                      }}
                      disabled={isPersonalInfoSaving || personalInfoSaveSuccess || loading}
                      aria-label="location"
                    />
                    <LocationDropdown
                      predictions={placePredictions}
                      onSelect={selectPlace}
                      inputRef={locationInputRef}
                      isVisible={
                        !isPersonalInfoSaving &&
                        !personalInfoSaveSuccess &&
                        !loading &&
                        showPlacesDropdown &&
                        placePredictions.length > 0
                      }
                    />
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default CreatePost;
