"use client";
import React from "react";
import { X } from "react-feather";

interface CustomCloseButtonProps {
  onClick: () => void;
  className?: string;
  size?: number;
  color?: string;
}

const CustomCloseButton: React.FC<CustomCloseButtonProps> = ({
  onClick,
  className = "",
  size = 24,
  color = "#333333",
}) => {
  return (
    <div
      className={`flex items-center justify-center p-1.5 cursor-pointer hover:bg-gray-100 transition-colors ${className}`}
      onClick={onClick}
      role="button"
      aria-label="Close"
    >
      <X size={size} color={color} strokeWidth={2} />
    </div>
  );
};

export default CustomCloseButton;
