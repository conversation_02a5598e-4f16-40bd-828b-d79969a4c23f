import { NextRequest, NextResponse } from 'next/server';
import { getTransactionByStripeSessionId } from '@/services/transactionService';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Get transaction from Firebase
    const transactionResult = await getTransactionByStripeSessionId(sessionId);

    // Get session details from Stripe
    let stripeSession;
    try {
      stripeSession = await stripe.checkout.sessions.retrieve(sessionId, {
        expand: ['payment_intent', 'customer'],
      });
    } catch (error) {
      console.error('Error retrieving Stripe session:', error);
    }

    if (transactionResult.success) {
      return NextResponse.json({
        success: true,
        transaction: transactionResult.transaction,
        stripeSession: stripeSession ? {
          id: stripeSession.id,
          amount_total: stripeSession.amount_total,
          currency: stripeSession.currency,
          customer: stripeSession.customer,
          payment_status: stripeSession.payment_status,
          payment_intent: stripeSession.payment_intent,
          created: stripeSession.created,
          expires_at: stripeSession.expires_at,
        } : null,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: transactionResult.error,
        stripeSession: stripeSession ? {
          id: stripeSession.id,
          amount_total: stripeSession.amount_total,
          currency: stripeSession.currency,
          customer: stripeSession.customer,
          payment_status: stripeSession.payment_status,
          payment_intent: stripeSession.payment_intent,
          created: stripeSession.created,
          expires_at: stripeSession.expires_at,
        } : null,
      }, { status: 404 });
    }
  } catch (error) {
    console.error('Error fetching transaction by session ID:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch transaction',
    }, { status: 500 });
  }
}
