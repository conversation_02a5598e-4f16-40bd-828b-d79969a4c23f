import { useEffect, useState } from "react";
import { ChevronsDown } from "react-feather";

interface ScrollButtonProps {
  scrollRef: React.RefObject<HTMLDivElement>;
}

const ScrollButton = ({ scrollRef }: ScrollButtonProps) => {
  const [isAtBottom, setIsAtBottom] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const handleScrollDown = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ top: 150, behavior: "smooth" });
    }
  };

  const handleScrollTop = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  useEffect(() => {
    const container = scrollRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 10;

      setIsAtBottom(atBottom);
      setIsVisible(!atBottom);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [scrollRef]);
  return (
    <>
      <div className="relative">
        <div className=" cursor-pointer fixed bottom-0 left-0 right-0 h-[100px] bg-gradient-to-t from-white flex flex-row justify-center items-center z-[49]" />
        {isVisible && (
          <button
            className="fixed bottom-0 left-1/2 transform -translate-x-1/2  rounded  z-[50]"
            onClick={handleScrollDown}
            style={{ height: "80px" }}
          >
            <img src="/assets/ChevronsDown.svg" className="h-[50px] w-[50px]" />
          </button>
        )}
        {isAtBottom && (
          <button
            className="fixed bottom-0 left-1/2 transform -translate-x-1/2  rounded  z-[50]"
            onClick={handleScrollTop}
            style={{ height: "80px" }}
          >
            <img
              src="/assets/ChevronsDown.svg"
              className="h-[50px] w-[50px] rotate-180"
            />
          </button>
        )}
      </div>
    </>
  );
};

export default ScrollButton;
