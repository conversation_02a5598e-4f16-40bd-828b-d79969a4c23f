import { NextRequest, NextResponse } from 'next/server';
import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { initFirebase } from '../../../../firebaseConfig';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Initialize Firebase and get Firestore instance
    const { db } = await initFirebase();

    // Add payment record to Firestore
    const paymentsRef = collection(db, 'payments');
    const docRef = await addDoc(paymentsRef, {
      userId,
      amount: 10,
      status: 'success',
      timestamp: Timestamp.now(),
    });

    return NextResponse.json({
      message: 'Payment stored successfully',
      paymentId: docRef.id
    });
  } catch (error) {
    console.error('Error storing payment:', error);
    return NextResponse.json(
      {
        error: 'Failed to store payment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
