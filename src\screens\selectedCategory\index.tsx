"use client";
import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import PostsHome from "./posts";
import ProfileHome from "./profiles";
import EventsHome from "./events";
import ServicesHome from "./services";
import { ChevronDown, ChevronsDown } from "react-feather";
import * as Tabs from "@radix-ui/react-tabs";
import { themes } from "../../../theme";
import { Item } from "react-photoswipe-gallery";

const data = [
  {
    img: "/assets/img.svg",
    title: "My Feed",
    backgroundColor: "#000000",
  },
  {
    img: "/assets/music.svg",
    title: "Music",
    backgroundColor: "#E5B045",
  },
  {
    img: "/assets/litrature.svg",
    title: "Literature",
    backgroundColor: "#CF5943",
  },
  {
    img: "/assets/art.svg",
    backgroundColor: "#3C5F9A",
    title: "Art",
  },
  {
    img: "/assets/film.svg",
    title: "Film & Photography",
    backgroundColor: "#46B933",
  },
  {
    img: "/assets/Theatre.svg",
    title: "Theatre & Performance",
    backgroundColor: "#E073D2",
  },
  {
    img: "/assets/multi.svg",
    title: "Multidisciplinary",
    backgroundColor: "#5331BC",
  },
  {
    img: "/assets/groups.svg",
    title: "Groups",
    backgroundColor: "#616770",
  },
];
type SelectedCategoryProps = {
  subcategory: string;
};

export function SelectedCategory({ subcategory }: SelectedCategoryProps) {
  const postsRef = useRef<HTMLDivElement>(null);
  const [color, setColor] = useState(String);
  const [categoryTitle, setCategoryTitle] = useState(String);
  const handleScroll = () => {
    if (postsRef.current) {
      postsRef.current.scrollLeft += 400;
    }
  };
  subcategory = decodeURIComponent(subcategory);
  const handle = () => {
    // console.log(subcategory);
    const decodedString = subcategory; // No need to slice off the first character

    // console.log(decodedString);

    Object.entries(themes).forEach(([themeName, themeProperties]) => {
      if (themeProperties.title === decodedString) {
        // Match found: Set the color and category title
        setColor(themeProperties.backgroundColor);
        setCategoryTitle(themeProperties.title);
      } else {
        // No match: Redirect to 404
        // console.log(decodedString, themeProperties.title);
        // console.log("redirect to 404");
      }
    });
  };

  function hexToRgba(hex: any, alpha = 1) {
    if (hex) {
      const [r, g, b] = hex
        .replace(/^#/, "")
        .match(/.{1,2}/g)
        .map((val: any) => parseInt(val, 16));
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
  }
  useEffect(() => {
    handle();
  });
  return (
    <div className="relative w-full px-3 max-md:px-1 mt-3 max-md:mt-1 overflow-hidden h-[calc(100vh-160px)]">
      <Tabs.Root
        defaultValue="Posts"
        className="overflow-scroll h-full hide-scroll max-md:overflow-x-hidden"
      >
        <div className="sticky top-0 bg-white z-50 pb-2 ">
          <Tabs.List
            className="TabsListBg w-[400px] max-md:w-full"
            aria-label="Manage your account"
            style={
              {
                "--active-bg-color": color,
              } as React.CSSProperties
            }
          >
            <Tabs.Trigger className={"TabsTriggerBg"} value="Posts" style={{}}>
              Posts
            </Tabs.Trigger>
            <Tabs.Trigger className="TabsTriggerBg" value="Services">
              Services
            </Tabs.Trigger>
            <Tabs.Trigger className="TabsTriggerBg" value="Events">
              Events
            </Tabs.Trigger>
            <Tabs.Trigger className="TabsTriggerBg" value="Profiles">
              Profiles
            </Tabs.Trigger>
          </Tabs.List>
        </div>

        <div className="sticky top-[2.55rem] w-full bg-white z-20 pb-[2px] pt-1">
          <div className="relative w-full md:mb-3 h-[50px]">
            <div className=" absolute bottom-[0.6rem] z-20 left-8">
              <p className="text-white text-2xl font-bold">{categoryTitle && categoryTitle}</p>
            </div>
            {data.map((item, index) => (
              <div key={index}>
                {item.title == categoryTitle && (
                  <img
                    src={item.img}
                    alt="Sample Image"
                    className="w-full h-[50px] object-cover  md:rounded-md "
                  />
                )}
              </div>
            ))}

            <div
              className="absolute inset-0 z-10 md:rounded-md "
              style={{
                background: `linear-gradient(83.81deg, ${hexToRgba(
                  color,
                  0.9
                )} 12.75%, ${hexToRgba(color, 0)} 49.03%, ${hexToRgba(color, 1)} 87.61%)`,
              }}
            />
          </div>
          <TabsContent
            value="Posts"
            className="w-full"
            style={{ background: "white", width: "100%" }}
            ref={postsRef}
          >
            <PostsHome color={color} subcategory={subcategory} />
          </TabsContent>
          <TabsContent value="Services" ref={postsRef}>
            <ServicesHome color={color} subcategory={subcategory} />
          </TabsContent>
          <TabsContent value="Events" ref={postsRef}>
            <EventsHome color={color} subcategory={subcategory} />
          </TabsContent>
          <TabsContent value="Profiles" ref={postsRef}>
            <ProfileHome color={color} subcategory={subcategory} />
          </TabsContent>
        </div>
      </Tabs.Root>
    </div>
  );
}

export default SelectedCategory;
