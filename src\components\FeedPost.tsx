"use client";
import { ExplorePublicationsQuery, PublicationsQuery } from "@/graphql/generated";
import { PostsQuery } from "@/graphql/test/generated";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";

type Props = {
  publication:
    | ExplorePublicationsQuery["explorePublications"]["items"][0]
    | PublicationsQuery["publications"]["items"][0];
};

export default function FeedPost({
  publication,
  data,
}: {
  publication: PostsQuery["posts"]["items"][0];
  data: any;
}) {
  return (
    <>
      <div key={publication.id} className="flex flex-col">
        <div className="flex flex-col gap-8">
          <div className="flex font-bold">
            {publication?.author?.username?.localName}
            {/* {publication?.by?.handle?.fullHandle} */}
          </div>
          <div className="flex"></div>
        </div>

        <div className="flex">
          {
            // only render images and videos
            // @ts-ignore
            publication?.__typename === "Post" &&
              (publication?.metadata?.__typename === "ImageMetadata" ||
                publication?.metadata?.__typename === "VideoMetadata") &&
              ((publication?.metadata?.__typename === "ImageMetadata" &&
                publication?.metadata?.image?.item) ||
                (publication?.metadata?.__typename === "VideoMetadata" &&
                  publication?.metadata?.video?.item)) && (
                <img
                  src={sanitizeDStorageUrl(
                    publication?.metadata?.__typename === "ImageMetadata"
                      ? publication?.metadata?.image?.item
                      : publication?.metadata?.__typename === "VideoMetadata"
                        ? publication?.metadata?.video?.item
                        : ""
                  )}
                  alt={""}
                  style={{
                    width: 490,
                    height: 250,
                    borderRadius: "50%",
                  }}
                />
              )
          }
        </div>
      </div>
    </>
  );
}
