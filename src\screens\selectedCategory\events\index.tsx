import { themes } from "../../../../theme";
import { useRef } from "react";
import ScrollButton from "@/components/bottomArrow";
import EventsCardSC from "./eventsCardSC";

const EventsHome = (props: any) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  const postsRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <div className="relative p-0 ">
        <div className="max-md:hidden">
          <ScrollButton scrollRef={scrollRef} />
        </div>

        <div
          ref={scrollRef}
          className="overflow-y-auto p-0 md:h-[calc(100vh-290px)]  max-md:h-screen hide-scroll"
        >
          <div
            className="flex flex-row max-md:flex-col  w-full  gap-3  bg-white h-full"
            // ref={postsRef}
          >
            <div className="w-full mb-[1px]">
              {Array.from({ length: 1 }).map((_, indexs) => (
                <div className="mb-3" key={indexs}>
                  {Object.entries(themes).map(
                    ([themeName, themeProperties]) =>
                      themeProperties.title === props.subcategory && (
                        <div key={themeName}>
                          <EventsCardSC
                            border={themeProperties.backgroundColor}
                            themeProperties={themeProperties}
                            isMyfeed={true}
                          />
                        </div>
                      )
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventsHome;
