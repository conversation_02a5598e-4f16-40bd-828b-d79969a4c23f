"use client";

import { useTheme } from "../../themeContext";
import { themes } from "../../theme";
import { Button } from "./ui/button";
import useAuth from "@/hook";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { useRef, useState, useEffect } from "react";
import Link from "next/link";

export default function ThemeSwitcher() {
  const { changeTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  const user = useAuth();
  const scrollContainerRef = useRef(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isEnd, setIsEnd] = useState(false);

  const pathSegments = pathname.split("/").filter(Boolean);

  const handleRougth = (title) => {
    router.push(`/browse/${title}`);
  };

  const handleScrollNext = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  const handleScrollPrev = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  useEffect(() => {
    const handleScrollEvent = () => {
      if (scrollContainerRef.current) {
        setIsScrolled(scrollContainerRef.current.scrollLeft > 0);
        setIsEnd(
          scrollContainerRef.current.scrollLeft +
            scrollContainerRef.current.clientWidth >=
            scrollContainerRef.current.scrollWidth
        );
      }
    };

    const scrollRef = scrollContainerRef.current;
    if (scrollRef) {
      scrollRef.addEventListener("scroll", handleScrollEvent);
    }

    return () => {
      if (scrollRef) {
        scrollRef.removeEventListener("scroll", handleScrollEvent);
      }
    };
  }, []);

  return (
    <div className="relative pr-10 max-md:pr-5 mb-3 overflow-x-auto">
      <div className="absolute top-0 right-0 bottom-0 w-[80px] bg-gradient-to-l from-white pointer-events-none z-50 max-md:hidden" />
      {isScrolled && (
        <div className="absolute top-0 left-0 bottom-0 w-[80px] bg-gradient-to-r from-white pointer-events-none z-50 max-md:hidden" />
      )}

      {isScrolled && (
        <button
          onClick={handleScrollPrev}
          className="absolute left-5 max-md:left-2 top-3 rounded-full z-50 max-md:hidden"
        >
          <img
            src="/assets/ChevronsDown.svg"
            className="h-[30px] w-[30px] rotate-90"
          />
        </button>
      )}
      {!isEnd && (
        <button
          onClick={handleScrollNext}
          className="absolute right-5 max-md:right-2 top-3 rounded-full z-50 max-md:hidden"
        >
          <img
            src="/assets/ChevronsDown.svg"
            className="h-[30px] w-[30px] -rotate-90"
          />
        </button>
      )}

      <div
        ref={scrollContainerRef}
        className="flex flex-row items-start flex-nowrap justify-between gap-10 max-md:gap-4 overflow-x-auto hide-scroll px-1 text-xl max-md:text-[13px]"
        style={{ scrollBehavior: "smooth" }}
      >
        <div className="py-3 max-md:py-1">
          <Link href="/">
            <Button
              className={
                !(pathSegments.length == 0)
                  ? "rounded-full border-none bg-white shadow-none hover:bg-white text-xl max-md:text-[13px] max-md:py-0 max-md:h-[20px] px-7 max-md:pr-4 max-md:pl-0 text-[#616770] h-[30px] -mt-1 cursor-pointer"
                  : "rounded-full border-[#454545] bg-[#454545] px-7 max-md:px-4 text-xl max-md:text-[13px] max-md:py-0 max-md:h-[20px] font-normal text-white h-[30px] -mt-1 cursor-pointer"
              }
            >
              All
            </Button>
          </Link>
        </div>

        {Object.entries(themes).map(([themeName, themeProperties]) => (
          <div key={themeName}>
            {(!user.isLogin &&
              themeProperties.title !== "My Feed" &&
              themeProperties.title !== "Customer") ||
            (user.isLogin && themeProperties.title !== "Customer") ? (
              <button
                onClick={() => {
                  changeTheme(themeName);
                  handleRougth(themeProperties.title);
                }}
                className="text-center px-3 py-2 flex flex-col items-center max-md:px-1 cursor-pointer"
              >
                <div
                  className={
                    pathSegments[0] === "browse" &&
                    themeProperties.title ===
                      decodeURIComponent(pathSegments[1])
                      ? "text-white whitespace-nowrap h-[30px] max-md:text-[13px] max-md:py-0 max-md:h-[20px] rounded-full px-7 max-md:px-4 text-center flex flex-row items-center mb-1"
                      : "text-[#616770] whitespace-nowrap h-[30px] max-md:text-[13px] max-md:py-0 max-md:h-[20px] rounded-full px-7 max-md:px-4 text-center flex flex-row items-center mb-1"
                  }
                  style={
                    pathSegments[0] === "browse" &&
                    themeProperties.title ===
                      decodeURIComponent(pathSegments[1])
                      ? { backgroundColor: themeProperties.backgroundColor }
                      : undefined
                  }
                >
                  {themeProperties.title}
                </div>
                <span
                  className="w-4 h-4 max-md:max-h-[5px] max-md:max-w-[5px] rounded-full"
                  style={{ backgroundColor: themeProperties.backgroundColor }}
                ></span>
              </button>
            ) : null}
          </div>
        ))}
      </div>
    </div>
  );
}
