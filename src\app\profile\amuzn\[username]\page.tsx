"use client";

import { useSearchParams } from "next/navigation";
import Profile from "@/screens/profile";

interface AmuznProfilePageProps {
  params: {
    username: string;
  };
}

const AmuznProfilePage = ({ params }: AmuznProfilePageProps) => {

  return (
    <Profile 
      userId='test'
      profileType="amuzn"
      profile_name={params?.username}
    />
  );
};

export default AmuznProfilePage;
