import { NextRequest, NextResponse } from 'next/server';
import { getStripeInstance, type StripeAccountRequest } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const { accountId, isUS }: StripeAccountRequest = await request.json();

    if (!accountId) {
      return NextResponse.json({ error: 'Account ID is required' }, { status: 400 });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const account = await stripeService.accounts.retrieve(accountId);

    return NextResponse.json({
      account,
      success: true,
    });

  } catch (error) {
    console.error('Error retrieving account:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}
