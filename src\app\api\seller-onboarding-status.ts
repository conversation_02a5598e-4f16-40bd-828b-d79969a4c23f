import type { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';
import { db } from '@/lib/firebaseAdmin';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { sellerId } = req.query;
  if (!sellerId) return res.status(400).json({ error: 'Missing sellerId' });

  // 1. Get seller's Stripe account ID from Firestore
  const doc = await db.collection('sellers').doc(String(sellerId)).get();
  if (!doc.exists) return res.status(404).json({ error: 'Seller not found' });

  const { stripeAccountId } = doc.data()!;
  if (!stripeAccountId) return res.status(404).json({ error: 'No Stripe account linked' });

  // 2. Fetch account status from Stripe
  const account = await stripe.accounts.retrieve(stripeAccountId);

  // The 'charges_enabled' and 'details_submitted' flags tell you if onboarding is complete
  res.status(200).json({
    stripeAccountId,
    charges_enabled: account.charges_enabled,
    payouts_enabled: account.payouts_enabled,
    details_submitted: account.details_submitted,
    requirements: account.requirements,
  });
}
