"use client";
import { useState } from "react";
import { themes } from "../../../../../../theme";
import { useRouter } from "next/navigation";
import LazyMedia from "@/components/LazyMedia";

const ImageGrid = (props: any) => {
  // console.log(props.chunk[0]?.category);
  const router = useRouter(); // Ensure it's used on the client
  const [category, setCategory] = useState("");

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;

    // console.log(process.env.BASE_STORAGE_URL);

    if (!baseUrl) return undefined;
    // Check if postFile is a valid string
    if (!postFile) {
      return undefined; // Return undefined if postFile is null, undefined, or an empty string
    }

    // If the postFile already includes the baseUrl, return it as-is
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    // Otherwise, construct the URL and return it
    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const getBackgroundColor = (title: string): string => {
    // Convert "Storytelling" to "Literature"
    const normalizedTitle = title === "Storytelling" ? "Literature" : title;

    // Find the theme by title
    const theme = Object.values(themes).find((t) => t.title === normalizedTitle);

    return theme ? theme.backgroundColor : "#ffffff"; // Default color if title not found
  };

  const handleClick = async (category: string, idparam: string, userid: string) => {
    try {
      router.push(`/browse/${category}/${idparam}%20${userid}`);
    } catch (error) {
      console.error("Error fetching user:", error);
    }
  };

  return (
    <>
      <div>
        <div className="w-full mt-0">
          <div className="w-full mb-[1px] gap-[1px]">
            <div className="w-full mt-0 mr-[1px]">
              <div className="grid grid-cols-6 gap-[1px] mb-[1px] mr-[1px]">
                <div className="col-span-4">
                  <div className="h-full w-full">
                    {props.chunk[0]?.mediaType === "image" ? (
                      <div
                        onClick={() =>
                          handleClick(
                            props.chunk[0]?.category == "Storytelling"
                              ? "Literature"
                              : props.chunk[0]?.category,
                            props.chunk[0]?.id,
                            props.chunk[0]?.user_id
                          )
                        }
                        className="h-full w-full cursor-pointer"
                      >
                        <LazyMedia
                          src={generateFileUrl(props.chunk[0]?.postFile) || "/assets/noimg.png"}
                          alt={`Post ${props.chunkIndex * 5 + 1}`}
                          type="image"
                          className="h-full w-full object-cover border-2"
                          style={{
                            borderColor: getBackgroundColor(props.chunk[0].category),
                            aspectRatio: "2/1", // This will make it match the height of the square image
                          }}
                          placeholderClassName="bg-gray-100"
                        />
                      </div>
                    ) : props.chunk[0]?.mediaType === "video" ? (
                      <div className="relative h-full w-full">
                        <div className="h-full w-full cursor-pointer overflow-hidden">
                          <div
                            onClick={() =>
                              handleClick(
                                props.chunk[0]?.category == "Storytelling"
                                  ? "Literature"
                                  : props.chunk[0]?.category,
                                props.chunk[0]?.id,
                                props.chunk[0]?.user_id
                              )
                            }
                            className="h-full w-full"
                          >
                            <LazyMedia
                              src={generateFileUrl(props.chunk[0]?.postFile)}
                              type="video"
                              className="h-full w-full object-cover border-2"
                              style={{
                                borderColor: getBackgroundColor(props.chunk[0].category),
                                aspectRatio: "2/1", // This will make it match the height of the square image
                              }}
                              placeholderClassName="bg-gray-100"
                              showPlayIcon={true}
                              playIconClassName="top-1 right-1"
                              controls={false}
                              autoPlay={false}
                              muted={true}
                            />
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
                <div className="col-span-2 cursor-pointer">
                  <div className="h-full w-full">
                    {props.chunk[1]?.mediaType === "image" ? (
                      <div
                        onClick={() =>
                          handleClick(
                            props.chunk[1]?.category == "Storytelling"
                              ? "Literature"
                              : props.chunk[1]?.category,
                            props.chunk[1]?.id,
                            props.chunk[1]?.user_id
                          )
                        }
                        className="h-full w-full"
                      >
                        <LazyMedia
                          src={generateFileUrl(props.chunk[1]?.postFile) || "/assets/noimg.png"}
                          alt={`Post ${props.chunkIndex * 5 + 1}`}
                          type="image"
                          className="h-full w-full object-cover border-2"
                          style={{
                            borderColor: getBackgroundColor(props.chunk[1].category),
                            aspectRatio: "1/1", // Ensure square aspect ratio
                          }}
                          placeholderClassName="bg-gray-100"
                        />
                      </div>
                    ) : props.chunk[1]?.mediaType === "video" ? (
                      <div className="relative h-full w-full">
                        <div className="h-full w-full">
                          <div
                            onClick={() =>
                              handleClick(
                                props.chunk[1]?.category == "Storytelling"
                                  ? "Literature"
                                  : props.chunk[1]?.category,
                                props.chunk[1]?.id,
                                props.chunk[1]?.user_id
                              )
                            }
                            className="h-full w-full"
                          >
                            <LazyMedia
                              src={generateFileUrl(props.chunk[1]?.postFile)}
                              type="video"
                              className="h-full w-full object-cover border-2"
                              style={{
                                borderColor: getBackgroundColor(props.chunk[1].category),
                                aspectRatio: "1/1", // Ensure square aspect ratio
                              }}
                              placeholderClassName="bg-gray-100"
                              showPlayIcon={true}
                              playIconClassName="top-1 right-1"
                              controls={false}
                              autoPlay={false}
                              muted={true}
                            />
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-6 gap-[1px] mr-[1px] min-h-[250px] ">
                <div className="col-span-2 h-full cursor-pointer">
                  <div className="w-full min-w-full  object-cover h-1/2 min-h-1/2 mb-[1px] aspect-square">
                    <div>
                      {props.chunk[2]?.mediaType === "image" ? (
                        <div
                          className="block aspect-square"
                          onClick={() =>
                            handleClick(
                              props.chunk[2]?.category == "Storytelling"
                                ? "Literature"
                                : props.chunk[2]?.category,
                              props.chunk[2]?.id,
                              props.chunk[2]?.user_id
                            )
                          }
                        >
                          <LazyMedia
                            src={generateFileUrl(props.chunk[2]?.postFile) || "/assets/noimg.png"}
                            alt={`Post ${props.chunkIndex * 5 + 1}`}
                            type="image"
                            className=" h-full w-full min-w-full  object-cover border-2 min-h-[124px]  aspect-square"
                            style={{
                              borderColor: getBackgroundColor(props.chunk[2].category),
                            }}
                            placeholderClassName="bg-gray-100"
                          />
                        </div>
                      ) : props.chunk[2]?.mediaType === "video" ? (
                        <div className=" relative ">
                          <div className="h-full w-full min-w-full object-cover  aspect-square min-h-[124px] ">
                            <div
                              onClick={() =>
                                handleClick(
                                  props.chunk[2]?.category == "Storytelling"
                                    ? "Literature"
                                    : props.chunk[2]?.category,
                                  props.chunk[2]?.id,
                                  props.chunk[2]?.user_id
                                )
                              }
                              className="block"
                            >
                              <LazyMedia
                                src={generateFileUrl(props.chunk[2]?.postFile)}
                                type="video"
                                className="h-full w-full min-w-full  object-cover border-2  aspect-square"
                                style={{
                                  borderColor: getBackgroundColor(props.chunk[2].category),
                                }}
                                placeholderClassName="bg-gray-100"
                                showPlayIcon={true}
                                playIconClassName="top-1 right-1"
                                controls={false}
                                autoPlay={false}
                                muted={true}
                              />
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </div>
                  </div>

                  <div className="w-full min-w-full object-cover h-1/2 min-h-1/2 aspect-square">
                    <div>
                      {props.chunk[3]?.mediaType === "image" ? (
                        <div
                          onClick={() =>
                            handleClick(
                              props.chunk[3]?.category == "Storytelling"
                                ? "Literature"
                                : props.chunk[3]?.category,
                              props.chunk[3]?.id,
                              props.chunk[3]?.user_id
                            )
                          }
                          className="block"
                        >
                          <LazyMedia
                            src={generateFileUrl(props.chunk[3]?.postFile) || "/assets/noimg.png"}
                            alt={`Post ${props.chunkIndex * 5 + 1}`}
                            type="image"
                            className=" h-full w-full min-w-full  object-cover border-2 min-h-[124px]  aspect-square"
                            style={{
                              borderColor: getBackgroundColor(props.chunk[3].category),
                            }}
                            placeholderClassName="bg-gray-100"
                          />
                        </div>
                      ) : props.chunk[3]?.mediaType === "video" ? (
                        <div className=" relative ">
                          <div className="h-full w-full min-w-full  object-cover  aspect-square min-h-[124px] overflow-hidden">
                            <div
                              onClick={() =>
                                handleClick(
                                  props.chunk[3]?.category == "Storytelling"
                                    ? "Literature"
                                    : props.chunk[3]?.category,
                                  props.chunk[3]?.id,
                                  props.chunk[3]?.user_id
                                )
                              }
                              className="block"
                            >
                              <LazyMedia
                                src={generateFileUrl(props.chunk[3]?.postFile)}
                                type="video"
                                className="h-full w-full min-w-full object-cover border-2 min-h-[124px] aspect-square "
                                style={{
                                  borderColor: getBackgroundColor(props.chunk[3].category),
                                }}
                                placeholderClassName="bg-gray-100"
                                showPlayIcon={true}
                                playIconClassName="top-1 right-1"
                                controls={false}
                                autoPlay={false}
                                muted={true}
                              />
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </div>
                <div className="col-span-4 w-full min-w-full  h-full min-h-full object-cover cursor-pointer">
                  <div>
                    {props.chunk[4]?.mediaType === "image" ? (
                      <div
                        onClick={() =>
                          handleClick(
                            props.chunk[4]?.category == "Storytelling"
                              ? "Literature"
                              : props.chunk[4]?.category,
                            props.chunk[4]?.id,
                            props.chunk[4]?.user_id
                          )
                        }
                        className="block"
                      >
                        {/* <p>{props.chunk[3]?.category}</p> */}
                        <LazyMedia
                          src={generateFileUrl(props.chunk[4]?.postFile) || "/assets/noimg.png"}
                          alt={`Post ${props.chunkIndex * 5 + 1}`}
                          type="image"
                          className=" h-full w-full min-w-full  object-cover border-2 min-h-[250px]  aspect-square"
                          style={{
                            borderColor: getBackgroundColor(props.chunk[4].category),
                          }}
                          placeholderClassName="bg-gray-100"
                        />
                      </div>
                    ) : props.chunk[4]?.mediaType === "video" ? (
                      <div className=" relative ">
                        <div className="h-full w-full min-w-full  object-cover max-h-[250px]  aspect-square">
                          <div
                            onClick={() =>
                              handleClick(
                                props.chunk[4]?.category == "Storytelling"
                                  ? "Literature"
                                  : props.chunk[4]?.category,
                                props.chunk[4]?.id,
                                props.chunk[4]?.user_id
                              )
                            }
                            className="block"
                          >
                            <LazyMedia
                              src={generateFileUrl(props.chunk[4]?.postFile)}
                              type="video"
                              className="h-full w-full min-w-full  object-cover border-2 min-h-[250px]  aspect-square"
                              style={{
                                borderColor: getBackgroundColor(props.chunk[4].category),
                              }}
                              placeholderClassName="bg-gray-100"
                              showPlayIcon={true}
                              playIconClassName="top-1 right-1"
                              controls={false}
                              autoPlay={false}
                              muted={true}
                            />
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageGrid;
