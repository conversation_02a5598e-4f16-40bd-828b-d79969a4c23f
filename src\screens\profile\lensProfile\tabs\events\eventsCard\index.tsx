import useAuth from "@/hook";
import useEvent from "@/hook/events";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { useState, useEffect } from "react";
import EditEvents from "../editEvents";
import EventCardSkeleton from "@/components/CardSkeleton/EventCardSkeleton";
import EmptyState from "@/components/EmptyState";

const EventsCard = ({ activeColor, otherUserID, selectedDate, setIstoggleCalenderEvent }: any) => {
  const auth = useAuth();
  const [localLoading, setLocalLoading] = useState(true);
  const { eventData, loading } = useEvent(otherUserID === "my-profile" ? auth.userId : otherUserID);

  const [isOpen, setIsOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState(null);

  // Force initial loading state and ensure it persists
  useEffect(() => {
    // Always start with loading state true
    setLocalLoading(true);

    let timer: NodeJS.Timeout;

    // Only transition to non-loading state when data is ready and loading is false
    if (!loading && eventData !== null) {
      timer = setTimeout(() => {
        setLocalLoading(false);
      }, 1000); // Longer delay to ensure skeleton is visible
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [loading, eventData]);

  const handleSelectService = (id: any) => {
    // console.log(id);

    setSelectedServiceId(id);
    setIsOpen(true);
    setIstoggleCalenderEvent(false);
  };

  const handleSelectServiceDetail = (id: any) => {
    setSelectedServiceId(null);
    setIsOpen(false);
    setIstoggleCalenderEvent(true);
  };

  return (
    <>
      <div className="overflow-y-scroll hide-scroll-custom bg-white h-[calc(100vh-280px)] max-md:h-[calc(100vh-107px)]">
        {localLoading ? (
          <div className="p-4">
            <EventCardSkeleton count={4} columns={1} showGrid={true} />
          </div>
        ) : (
          <div>
            {isOpen ? (
              <EditEvents
                eventId={selectedServiceId}
                onSelectServiceDetails={handleSelectServiceDetail}
                otherUserID={otherUserID}
              />
            ) : (
              <div className="grid grid-cols-1 max-md:grid-cols-1 gap-3">
                {eventData && eventData.length > 0 ? (
                  (() => {
                    const filteredEvents = eventData.filter((service: any) => {
                      const serviceDate = new Date(service.date);
                      return (
                        serviceDate.getMonth() === selectedDate.getMonth() &&
                        serviceDate.getFullYear() === selectedDate.getFullYear()
                      );
                    });

                    return filteredEvents.length > 0 ? (
                      filteredEvents.map((service: any, index: number) => (
                        <div
                          className="cursor-pointer  w-full"
                          key={index}
                          onClick={() => handleSelectService(service.id)}
                        >
                          <GlobalCardEvents post={service} border={activeColor} />
                        </div>
                      ))
                    ) : (
                      <div className="col-span-2 w-full">
                        <EmptyState
                          type="events"
                          title={`No Events in ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`}
                          message={`There are no events scheduled for ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`}
                          isOwnProfile={otherUserID === "my-profile"}
                          actionLabel={otherUserID === "my-profile" ? "Create Event" : undefined}
                          onAction={
                            otherUserID === "my-profile"
                              ? () => handleSelectService("new")
                              : undefined
                          }
                        />
                      </div>
                    );
                  })()
                ) : (
                  <div className="col-span-2 w-full">
                    <EmptyState
                      type="events"
                      title="No Events Yet"
                      message={
                        otherUserID === "my-profile"
                          ? `You haven't created any events yet. Create your first event to start scheduling in ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`
                          : `This user hasn't created any events yet for ${new Date(selectedDate).toLocaleString("default", { month: "long" })} ${selectedDate.getFullYear()}`
                      }
                      isOwnProfile={otherUserID === "my-profile"}
                      actionLabel={otherUserID === "my-profile" ? "Create Event" : undefined}
                      onAction={
                        otherUserID === "my-profile" ? () => handleSelectService("new") : undefined
                      }
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default EventsCard;
