"use client";
import React, { useEffect, useState } from "react";
import useLensUser from "../lib/auth/useLensUser";
import useLogin from "../lib/auth/useLogin";
import { GetWalletId, UpdateAuthBridge_V2 } from "@/services/authBridgeService";
import { Connector, useAccount, useConnect, useDisconnect } from "wagmi";
import { initFirebase } from "../../firebaseConfig";
import { PageSize, useAccountsAvailableQuery } from "@/graphql/test/generated";
import { create } from "zustand";

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogOverlay,
} from "@/components/ui/alert-dialog";
import { Button } from "./ui/button";
import { X, Loader2, AlertCircle, CheckCircle2 } from "lucide-react";

// Create a global store for modal states to ensure they persist even when parent components unmount
interface ModalState {
  isWalletModalOpen: boolean;
  isPostConnectModalOpen: boolean;
  setWalletModalOpen: (open: boolean) => void;
  setPostConnectModalOpen: (open: boolean) => void;
}

const useModalStore = create<ModalState>((set) => ({
  isWalletModalOpen: false,
  isPostConnectModalOpen: false,
  setWalletModalOpen: (open) => set({ isWalletModalOpen: open }),
  setPostConnectModalOpen: (open) => set({ isPostConnectModalOpen: open }),
}));

// Helper function to detect mobile devices
function isMobileDevice() {
  if (typeof window !== "undefined") {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }
  return false;
}

type Props = {
  onClose?: () => void;
};

export default function SignInButton({ onClose }: Props) {
  const { address, isConnected } = useAccount();
  const [selectedAccount, setSelectedAccount] = useState<any>(undefined);
  const { isSignedInQuery, profileQuery } = useLensUser();
  const { mutate: requestLogin } = useLogin();
  const { disconnect } = useDisconnect();
  const { connectors, connect, error } = useConnect();
  const availableConnectors: any = ["injected", "walletConnect"];

  // Use global modal states from the store
  const {
    isWalletModalOpen,
    isPostConnectModalOpen,
    setWalletModalOpen: setIsWalletModalOpen,
    setPostConnectModalOpen: setIsPostConnectModalOpen,
  } = useModalStore();

  // Connection states
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // State to track if we're on a mobile device
  const [isMobile, setIsMobile] = useState(false);

  // State for showing wallet app reminder
  const [showWalletReminder, setShowWalletReminder] = useState(false);

  // Check if we're on a mobile device on component mount
  useEffect(() => {
    setIsMobile(isMobileDevice());
  }, []);

  // Reset error when modal is opened
  useEffect(() => {
    if (isWalletModalOpen) {
      setConnectionError(null);
    }
  }, [isWalletModalOpen]);

  // Handle connection errors
  useEffect(() => {
    if (error) {
      setConnectionError(error.message);
      setIsConnecting(false);
    }
  }, [error]);

  // Handle connection success
  useEffect(() => {
    if (isConnected && isConnecting) {
      setIsConnecting(false);
      setIsWalletModalOpen(false);
    }
  }, [isConnected, isConnecting]);

  // Get available Lens accounts
  const { data: accountData } = useAccountsAvailableQuery(
    {
      accountsAvailableRequest: {
        managedBy: address,
        includeOwned: true,
        pageSize: PageSize.Fifty,
      },
      lastLoggedInAccountRequest: {
        address,
      },
    },
    {
      enabled: !!address,
      refetchOnWindowFocus: false,
    }
  );

  const allProfiles = accountData?.accountsAvailable.items || [];
  const lastLogin = accountData?.lastLoggedInAccount;
  const remainingProfiles = lastLogin
    ? allProfiles
        .filter(({ account }) => account.address !== lastLogin.address)
        .map(({ account }) => account)
    : allProfiles.map(({ account }) => account);
  const accounts = lastLogin ? [lastLogin, ...remainingProfiles] : remainingProfiles;

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
    setIsPostConnectModalOpen(false);
  };

  // Update auth bridge when account is selected
  useEffect(() => {
    (async () => {
      if (selectedAccount) {
        const _wallet_id: string | null = address ?? (await GetWalletId());
        const { auth } = await initFirebase();
        const user = auth.currentUser;

        if (!user?.uid || !_wallet_id) {
          return;
        }

        await UpdateAuthBridge_V2({
          wallet_id: _wallet_id,
          user_id: user.uid,
          email: user.email,
          lens_id: selectedAccount?.username?.value,
          lens_code: selectedAccount?.address,
        });
      }
    })();
  }, [profileQuery]);

  const updateAuthBridge = async () => {
    try {
      if (selectedAccount) {
        const _wallet_id: string | null = address ?? (await GetWalletId());
        const { auth } = await initFirebase();

        const user = auth.currentUser;

        if (!user?.uid || !_wallet_id) {
          return;
        }
        await UpdateAuthBridge_V2({
          wallet_id: _wallet_id,
          user_id: user.uid,
          email: user.email,
          lens_id: selectedAccount?.username?.value,
          lens_code: selectedAccount?.address,
        });
      }
    } catch (error) {
      console.log({ error });
    }
  };

  // Show wallet reminder when account is selected on mobile
  useEffect(() => {
    if (selectedAccount && isMobile) {
      setShowWalletReminder(true);
      // Hide the reminder after 15 seconds to give users more time to notice it
      const timer = setTimeout(() => {
        setShowWalletReminder(false);
      }, 15000);

      return () => clearTimeout(timer);
    }
  }, [selectedAccount, isMobile]);

  // Open post-connect modal when wallet is connected
  useEffect(() => {
    if (isConnected && !isSignedInQuery?.data && !isPostConnectModalOpen) {
      // Open the post-connect modal immediately
      const timer = setTimeout(() => {
        setIsPostConnectModalOpen(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isConnected, isSignedInQuery?.data]);

  // Handle wallet connection
  const handleConnectWallet = (connector: Connector) => {
    try {
      setIsConnecting(true);
      setConnectionError(null);
      connect({ connector });
    } catch (err) {
      console.error("Connection error:", err);
      setIsConnecting(false);
      setConnectionError(err instanceof Error ? err.message : "Failed to connect wallet");
    }
  };

  // Filter available connectors
  const filteredConnectors = connectors
    .filter((connector: any) => availableConnectors.includes(connector.id))
    .sort(
      (a: Connector, b: Connector) =>
        availableConnectors.indexOf(a.id) - availableConnectors.indexOf(b.id)
    );

  // Wallet connection component
  function WalletOption({ connector }: { connector: Connector }) {
    const [ready, setReady] = React.useState(false);

    React.useEffect(() => {
      (async () => {
        try {
          const provider = await connector.getProvider();
          setReady(!!provider);
        } catch (error) {
          console.error(`Error getting provider for ${connector.name}:`, error);
          setReady(false);
        }
      })();
    }, [connector]);

    return (
      <Button
        key={connector.uid}
        disabled={!ready || isConnecting}
        onClick={() => handleConnectWallet(connector)}
        className="w-full mb-2 py-6 flex items-center justify-center"
        variant="outline"
      >
        {isConnecting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
        {connector.name === "Injected" ? "Browser Wallet" : connector.name}
      </Button>
    );
  }

  // Initial state - show sign in with Lens button
  if (!isConnected) {
    return (
      <>
        <p
          onClick={() => {
            // First open our modal, then close the parent modal with a slight delay
            setIsWalletModalOpen(true);

            // Use setTimeout to ensure our modal is fully open before closing the parent
            if (onClose) {
              setTimeout(() => {
                onClose();
              }, 50);
            }
          }}
          className="cursor-pointer"
        >
          Sign in with Lens
        </p>

        {/* Wallet Connection Modal - Using portal to ensure it stays open even when parent closes */}
        <AlertDialog open={isWalletModalOpen} onOpenChange={setIsWalletModalOpen}>
          <AlertDialogOverlay className="bg-black/50" />
          <AlertDialogContent className="py-6 px-5 sm:px-6 max-w-[95vw] sm:max-w-md w-full mx-auto bg-white shadow-lg border border-gray-100 md:rounded-xl">
            <div className="flex justify-between items-center mb-5">
              <AlertDialogTitle className="text-xl font-semibold">Connect Wallet</AlertDialogTitle>
              <div
                className="cursor-pointer hover:bg-gray-100 p-1.5 rounded-full transition-colors"
                onClick={() => setIsWalletModalOpen(false)}
              >
                <X size={20} className="text-gray-600" />
              </div>
            </div>

            <AlertDialogDescription className="mb-4">
              Connect your wallet to sign in with Lens
            </AlertDialogDescription>

            <div className="space-y-2 mb-4">
              {filteredConnectors.map((connector) => (
                <WalletOption key={connector.uid} connector={connector} />
              ))}
            </div>

            {connectionError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4 flex items-start">
                <AlertCircle className="text-red-500 mr-2 mt-0.5 h-5 w-5 flex-shrink-0" />
                <p className="text-red-700 text-sm">{connectionError}</p>
              </div>
            )}

            {isConnecting && (
              <div className="flex justify-center items-center py-3">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
                <span className="ml-2">Connecting...</span>
              </div>
            )}

            <AlertDialogFooter className="mt-4">
              <p className="text-xs text-gray-500 text-center w-full">
                By connecting your wallet, you agree to our Terms of Service and Privacy Policy
              </p>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }

  // Post-connection modal
  if (!isSignedInQuery?.data) {
    return (
      <>
        <p
          onClick={() => {
            // First open our modal, then close the parent modal with a slight delay
            setIsPostConnectModalOpen(true);

            // Use setTimeout to ensure our modal is fully open before closing the parent
            if (onClose) {
              setTimeout(() => {
                onClose();
              }, 50);
            }
          }}
          className="cursor-pointer"
        >
          Sign in with Lens
        </p>

        {/* Wallet Reminder Notification for Mobile */}
        {showWalletReminder && isMobile && (
          <div className="fixed top-0 left-0 right-0 mx-auto w-full bg-blue-600 text-white p-6 shadow-lg z-[9999] animate-pulse">
            <div className="flex items-start gap-3 max-w-md mx-auto">
              <div className="mt-0.5">
                <span className="relative flex h-5 w-5">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-5 w-5 bg-white"></span>
                </span>
              </div>
              <div>
                <p className="font-bold text-lg mb-2">ACTION REQUIRED!</p>
                <p className="text-base opacity-90">
                  Please open your wallet app now to check for pending confirmation requests for
                  Lens sign-in. Your wallet app should have a notification waiting.
                </p>
                <Button
                  className="mt-3 bg-white text-blue-600 font-bold"
                  onClick={() => {
                    // Try to reopen wallet app again when button is clicked
                    try {
                      if (typeof window !== "undefined") {
                        const userAgent = navigator.userAgent.toLowerCase();
                        if (userAgent.includes("android")) {
                          window.location.href = "wc:";
                        } else if (userAgent.includes("iphone") || userAgent.includes("ipad")) {
                          window.location.href = "wc:";
                        }
                      }
                    } catch (error) {
                      console.error("Error reopening wallet:", error);
                    }
                  }}
                >
                  Open Wallet App
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Post-Connection Modal */}
        <AlertDialog open={isPostConnectModalOpen} onOpenChange={setIsPostConnectModalOpen}>
          <AlertDialogOverlay className="bg-black/50" />
          <AlertDialogContent className="py-6 px-5 sm:px-6 max-w-[95vw] sm:max-w-md w-full mx-auto bg-white shadow-lg border border-gray-100 md:rounded-xl">
            <div className="flex justify-between items-center mb-5">
              <AlertDialogTitle className="text-xl font-semibold text-black">
                Sign in with Lens
              </AlertDialogTitle>
              <div
                className="cursor-pointer hover:bg-gray-100 p-1.5 rounded-full transition-colors"
                onClick={() => setIsPostConnectModalOpen(false)}
              >
                <X size={20} className="text-gray-600" />
              </div>
            </div>

            <AlertDialogDescription className="mb-4">
              <div className="flex flex-col gap-5">
                <div className="p-3.5 border border-gray-200 rounded-lg bg-gray-50">
                  <div className="flex items-center">
                    <CheckCircle2 className="text-green-500 mr-2 h-5 w-5" />
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Connected Wallet</p>
                      <p className="font-mono text-sm sm:text-base truncate">
                        {address
                          ? `${address.substring(0, 8)}...${address.substring(address.length - 6)}`
                          : ""}
                      </p>
                    </div>
                  </div>
                </div>

                {isMobile && (
                  <div className="p-3.5 border border-gray-200 rounded-lg bg-blue-50 text-blue-700">
                    <p className="text-sm font-medium mb-1">
                      <span className="flex items-center gap-1.5">
                        <span className="relative flex h-2 w-2">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                        </span>
                        Mobile Instructions
                      </span>
                    </p>
                    <p className="text-xs">
                      After selecting a Lens account below, if your wallet app doesn't open
                      automatically, please manually open your wallet app and check for pending
                      confirmation requests.
                    </p>
                  </div>
                )}

                <div>
                  <p className="font-semibold mb-3 text-black">Available Lens Accounts</p>
                  {accounts && accounts.length > 0 ? (
                    <div className="flex flex-col gap-2.5 max-h-[30vh] overflow-y-auto pr-1">
                      {accounts.map((current) => (
                        <Button
                          key={current.address}
                          variant="outline"
                          className="w-full py-3 text-center justify-center rounded-lg"
                          onClick={() => {
                            // Set the selected account
                            setSelectedAccount(current);
                            // Request login with the selected account
                            requestLogin(current.address);
                            updateAuthBridge();
                            // Close the modal
                            setIsPostConnectModalOpen(false);

                            // For mobile devices, show the wallet reminder immediately
                            if (isMobile) {
                              setShowWalletReminder(true);

                              // Try to automatically reopen the wallet app
                              try {
                                // For MetaMask
                                if (
                                  typeof window !== "undefined" &&
                                  window.ethereum &&
                                  (window.ethereum as any).isMetaMask
                                ) {
                                  (window.ethereum as any).request({
                                    method: "eth_requestAccounts",
                                  });
                                }

                                // For WalletConnect - try to use deep linking
                                const userAgent = navigator.userAgent.toLowerCase();
                                if (userAgent.includes("android")) {
                                  // Try to open wallet app on Android
                                  window.location.href = "wc:";
                                } else if (
                                  userAgent.includes("iphone") ||
                                  userAgent.includes("ipad")
                                ) {
                                  // Try to open wallet app on iOS
                                  window.location.href = "wc:";
                                }
                              } catch (error) {
                                console.error("Error reopening wallet:", error);
                              }
                            }
                          }}
                        >
                          {current.username?.localName || "Unnamed Account"}
                        </Button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500 py-4 px-3 bg-gray-50 rounded-lg border border-gray-200 text-center">
                      No Lens accounts available
                    </div>
                  )}
                </div>

                <Button variant="outline" className="w-full mt-2" onClick={handleDisconnect}>
                  Disconnect Wallet
                </Button>
              </div>
            </AlertDialogDescription>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }

  // Loading states
  if (isSignedInQuery?.isLoading || profileQuery?.isLoading) {
    return (
      <p className="cursor-pointer" onClick={() => onClose && onClose()}>
        <Loader2 className="inline-block mr-2 h-4 w-4 animate-spin" />
        Loading...
      </p>
    );
  }

  // No default profile
  if (!profileQuery?.data?.account) {
    return (
      <p
        className="cursor-pointer"
        onClick={() => {
          // First open our modal, then close the parent modal with a slight delay
          setIsPostConnectModalOpen(true);

          // Use setTimeout to ensure our modal is fully open before closing the parent
          if (onClose) {
            setTimeout(() => {
              onClose();
            }, 50);
          }
        }}
      >
        No Lens Profile
      </p>
    );
  }

  // Successfully connected with Lens profile
  if (profileQuery?.data?.account) {
    selectedAccount && localStorage.setItem("lens-user", JSON.stringify(selectedAccount));
    return (
      <p className="cursor-pointer flex items-center" onClick={() => onClose && onClose()}>
        <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
        Lens Connected
      </p>
    );
  }

  // Fallback for unexpected states
  return (
    <p
      className="cursor-pointer flex items-center"
      onClick={() => {
        window.location.reload();
        if (onClose) onClose();
      }}
    >
      <AlertCircle className="mr-2 h-4 w-4 text-yellow-500" />
      Reconnect
    </p>
  );
}
