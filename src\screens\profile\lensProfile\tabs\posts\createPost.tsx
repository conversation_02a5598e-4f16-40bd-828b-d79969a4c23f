"use client";
import useAuth from "@/hook";
import { useCreatePost } from "@/lib/useCreatePost";
import { useState } from "react";
import { FilePlus, Trash, X } from "react-feather";
const CreatePost = (props: any) => {
  const [about, setAbout] = useState("");
  const [media, setMedia] = useState<File | null>(null);
  const [isMedia, setIsMedia] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const postResp = useCreatePost();

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (media) {
      const resp = await postResp(media, about);
      console.log({ resp });
    }
  };

  const handleSubmit = async () => {
    if (media && !isSubmitting) {
      setIsSubmitting(true);
      try {
        const resp = await postResp(media, about);
        // Close the modal after successful post creation
        props.setIsOpen(false);
      } catch (error) {
        console.error("Error creating post:", error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setMedia(event.target.files[0]);
      setIsMedia(true);
    }
  };

  return (
    <>
      <div className="flex flex-row justify-between items-center mb-4 sticky top-0 bg-white z-[9999]">
        <div
          onClick={() => !isSubmitting && props.setIsOpen(false)}
          className={isSubmitting ? "cursor-not-allowed opacity-50" : "cursor-pointer"}
        >
          <X />
        </div>
        <h2 className="text-xl font-bold">Create Posts</h2>
        <p
          onClick={() => (about && media && !isSubmitting ? handleSubmit() : "")}
          className={
            about && media && !isSubmitting
              ? "font-bold text-primary cursor-pointer"
              : "font-bold text-borderColor cursor-not-allowed"
          }
        >
          {isSubmitting ? "Saving..." : "Save"}
        </p>
      </div>
      <div className=" bg-gray-50">
        <div className="max-w-md mx-auto bg-white  rounded-md p-2 py-4">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}
            className="space-y-6"
          >
            {/* About Project */}
            <div>
              <label htmlFor="about" className="text-primary mb-2 font-[600] block text-start">
                About Project
              </label>
              <textarea
                id="about"
                value={about}
                onChange={(e) => setAbout(e.target.value)}
                placeholder="What was this project about?"
                className="w-full p-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              ></textarea>
              <p className="text-sm text-gray-500"></p>
            </div>

            {/* Media */}
            <div>
              <label className="text-primary mb-2 font-[600] block text-start">Media</label>
              {!isMedia ? (
                <div className="w-full ">
                  <label
                    htmlFor="media-upload"
                    className={`row gap-4 ${
                      isSubmitting ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                    }`}
                  >
                    <span className="bg-primary text-white p-2 rounded-xl h-[60px] w-[100px] row items-center justify-center">
                      <FilePlus />
                    </span>
                    <div className="flex flex-col">
                      <span className="text-subtitle text-base text-start">
                        File size max 4MB per photo
                      </span>
                      <span className="text-subtitle mt-1 text-base text-start">
                        File size max 8MB / 15 seconds per video
                      </span>
                    </div>
                    <input
                      type="file"
                      id="media-upload"
                      className="hidden"
                      accept="image/*,video/*"
                      onChange={handleMediaUpload}
                      disabled={isSubmitting}
                    />
                  </label>
                </div>
              ) : (
                media && (
                  <div className="mt-2 relative">
                    <img
                      src={URL.createObjectURL(media)}
                      alt="Media Preview"
                      className="w-full h-auto rounded-md max-h-[300px] "
                    />
                    <div
                      className={`bg-white p-2 text-primary rounded-full absolute top-2 right-2 ${
                        isSubmitting ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                      }`}
                      onClick={() => {
                        if (!isSubmitting) setIsMedia(false);
                      }}
                    >
                      <Trash />
                    </div>
                  </div>
                )
              )}
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default CreatePost;
