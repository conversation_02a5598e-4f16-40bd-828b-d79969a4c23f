import { NextRequest, NextResponse } from 'next/server';
import { doc, setDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../firebaseConfig';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { UpdateUserAccountId } from '@/services/ordersServices';

export async function POST(req: NextRequest) {
  try {
    const { sellerId, account, userId } = await req.json();

    // Determine the user ID to use (priority: sellerId > userId > auth)
    let finalUserId = sellerId || userId;

    if (!finalUserId) {
      finalUserId = await getUserIdFromRequest(req);
    }

    if (!finalUserId || !account) {
      return NextResponse.json(
        { error: 'Missing userId/sellerId or account ID' },
        { status: 400 }
      );
    }

    console.log('Saving seller account:', { userId: finalUserId, account });

    // Initialize Firebase and get Firestore instance
    const { db } = await initFirebase();

    // Save/update the account in stripeAccounts collection
    const stripeAccountRef = doc(db, 'stripeAccounts', account);
    await setDoc(stripeAccountRef, {
      stripeAccountId: account,
      userId: finalUserId,
      onboardingComplete: true,
      updatedAt: new Date().toISOString()
    }, { merge: true });

    // Update user collection with stripe_id
    try {
      // First, try to update directly using Firestore
      const userRef = doc(db, 'users', finalUserId);
      await setDoc(userRef, {
        stripe_id: account,
        updated_at: new Date().toISOString()
      }, { merge: true });

      console.log('Successfully updated user with stripe_id via direct Firestore update');

      // Also try the service method as a backup
      try {
        const updateResult = await UpdateUserAccountId({
          user_id: finalUserId,
          stripe_id: account
        });

        if (updateResult.success) {
          console.log('Successfully updated user with stripe_id via service');
        }
      } catch (serviceError) {
        console.log('Service update failed, but direct update succeeded:', serviceError);
        // This is expected if the user is not authenticated, so we don't treat it as an error
      }
    } catch (userUpdateError) {
      console.error('Error updating user with stripe_id:', userUpdateError);
      return NextResponse.json(
        { error: 'Failed to update user account' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Seller account saved successfully',
      userId: finalUserId,
      stripeAccountId: account
    });
  } catch (error) {
    console.error('Error saving seller account:', error);
    return NextResponse.json(
      { error: 'Failed to save seller account' },
      { status: 500 }
    );
  }
}
