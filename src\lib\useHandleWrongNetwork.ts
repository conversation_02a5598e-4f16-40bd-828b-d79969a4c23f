import { useConnections, useS<PERSON><PERSON>hai<PERSON> } from "wagmi";
import { CHAIN } from "./constant";

const useHandleWrongNetwork = () => {
  const activeConnection = useConnections();
  const { switchChainAsync } = useSwitchChain();

  const isConnected = () => activeConnection[0] !== undefined;
  const isWrongNetwork = () => activeConnection[0]?.chainId !== CHAIN.id;
  // console.log({ isConnected: isConnected(), isWrongNetwork: isWrongNetwork() });

  const handleWrongNetwork = async () => {
    // console.log("iniininini");

    if (!isConnected()) {
      console.warn("No active connection found.");
      return;
    }

    if (isWrongNetwork()) {
      try {
        // console.log("🔥🔥🔥🔥");

        const resp = await switchChainAsync({ chainId: CHAIN.id });
        // console.log({ resp });
      } catch (error) {
        console.error("Failed to switch chains:", error);
      }
    }
  };

  return handleWrongNetwork;
};

export default useHandleWrongNetwork;
