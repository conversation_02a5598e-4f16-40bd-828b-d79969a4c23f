# import "../../common/graphql/common.graphql"
# import "../../module/graphql/reference-module-fields.graphql"
# import "./metadata-fields.graphql"

fragment PrimaryPublicationFields on PrimaryPublication {
  ... on Comment {
    id,
  }

  ... on Quote {
    id
  }

  ... on Post {
    id
  }
  
}

fragment PublicationOperationFields on PublicationOperations {
  isNotInterested
  hasBookmarked
  hasReported
  canAct
  hasActed {
    value
    isFinalisedOnchain
  }
  actedOn {
    ... on KnownCollectOpenActionResult {
      type
    }
    ... on UnknownOpenActionResult {
      address
      category
      initReturnData
    }
  }
  hasReacted
  canComment
  canMirror
  hasMirrored
  canDecrypt {
    result
    reasons
    extraDetails
  }
}

fragment PostFields on Post {
  id
  publishedOn {
    id
  }
  isHidden
  momoka {
    proof
  }
  txHash
  createdAt
  by {
    id
    handle {
      ...HandleInfo
    }
  }
  stats {
    bookmarks
    comments
    countOpenActions
    mirrors
    quotes
    quotes
    reactions
  }
  operations {
    ...PublicationOperationFields
  }
  metadata {
    ...AnyPublicationMetadataFields
  }
  referenceModule {
    ...ReferenceModuleFields
  }
}

fragment QuoteFields on Quote {
  id
  publishedOn {
    id
  }
  isHidden
  momoka {
    proof
  }
  txHash
  createdAt
  by {
    id
    handle {
      ...HandleInfo
    }
  }
  stats {
    comments
    mirrors
    quotes
  }
  operations {
    ...PublicationOperationFields
  }

  quoteOn {
    ...PrimaryPublicationFields
  }
  metadata {
    ...AnyPublicationMetadataFields
  }
  referenceModule {
    ...ReferenceModuleFields
  }
}

fragment MirrorFields on Mirror {
  id
  publishedOn {
    id
    __typename

  }
  isHidden
  momoka {
    proof
  }
  
  txHash
  createdAt
  mirrorOn {
    ...PrimaryPublicationFields
  }
}

fragment CommentFields on Comment {
  id
  publishedOn {
    id
  }
  isHidden
  momoka {
    proof
  }
  txHash
  createdAt
  by {
    id
    handle {
      ...HandleInfo
    }
  }
  stats {
    comments
    mirrors
    quotes
  }
  operations {
    ...PublicationOperationFields
  }
  metadata {
    ...AnyPublicationMetadataFields
  }
  root {
    ...PrimaryPublicationFields
  }
  commentOn {
    ...PrimaryPublicationFields
  }

  firstComment {
    ...PrimaryPublicationFields
  }

  referenceModule {
    ...ReferenceModuleFields
  }
}