import useLogin from "./auth/useLogin";
import { useMutation } from "@tanstack/react-query";
import { readAccessToken } from "./auth/auth-helpers";
import { useFollowMutation } from "@/graphql/test/generated";
import { useAccount } from "wagmi";
import { GetWalletId } from "@/services/authBridgeService";

export function useFollow() {
  const { mutateAsync: requestTypedData } = useFollowMutation();

  const { address, isConnected } = useAccount();
  const { mutateAsync: loginUser }: any = useLogin();
  const isAuthW3 = GetWalletId();

  async function follow(userId: string) {
    try {
      if (!address && !isConnected && !isAuthW3) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected && !isAuthW3)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      // Validate userId format
      if (!userId || !userId.match(/^0x[0-9a-fA-F]+$/)) {
        // console.log("❌here");
        throw new Error("Invalid profile ID format");
      }

      const typedData = await requestTypedData({
        request: {
          account: userId,
          // follow: [
          //   {
          //     profileId: userId,
          //   },
          // ],
        },
      });

      return typedData.follow;
    } catch (error) {
      // console.error("Follow error:", error);
      throw error;
    }
  }
  return useMutation(follow);
}
