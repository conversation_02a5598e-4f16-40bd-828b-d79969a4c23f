import { themes } from "../../../../theme";
import { useRef } from "react";
import ScrollButton from "@/components/bottomArrow";
import ServicesCard from "./servicesCard";

const ServicesHome = (props: any) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <div className="relative p-0">
        <div className="max-md:hidden">
          <ScrollButton scrollRef={scrollRef} />
        </div>

        <div
          ref={scrollRef}
          className="overflow-y-auto p-0 md:h-[calc(100vh-290px)] max-md:h-screen hide-scroll"
        >
          <div className="mb-3">
            {Object.entries(themes).map(([themeName, themeProperties]) =>
              themeProperties.title === props.subcategory ? (
                <div className="">
                  <ServicesCard
                    key={themeName}
                    border={themeProperties.backgroundColor}
                    themeProperties={themeProperties}
                  />
                </div>
              ) : null
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ServicesHome;
