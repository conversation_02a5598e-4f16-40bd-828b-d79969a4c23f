"use client";
import { useRef } from "react";
import ScrollButton from "@/components/bottomArrow";
import { themes } from "../../../../theme";
import PostsCard from "./postsCard";

const PostsHome = (props: any) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <div className="relative p-0">
        <div className="max-md:hidden">
          <ScrollButton scrollRef={scrollRef} />
        </div>

        <div
          ref={scrollRef}
          className="overflow-y-auto p-0 md:h-[calc(100vh-280px)]  max-md:h-screen hide-scroll"
        >
          {/* For Web  View */}

          <div className="  bg-white h-full">
            <div className="w-full">
              <div className="">
                {Object.entries(themes).map(
                  ([themeName, themeProperties]) =>
                    themeProperties.title === props.subcategory && (
                      <>
                        <PostsCard themeProperties={themeProperties} />
                      </>
                    )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PostsHome;
