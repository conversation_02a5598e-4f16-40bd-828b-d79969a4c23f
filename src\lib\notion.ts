import { Client } from "@notionhq/client";

export const notion = new Client({ auth: process.env.NOTION_TOKEN });

// console.log("NOTION_TOKEN:", process.env.NOTION_TOKEN);

async function getBlockChildren(blockId: string): Promise<any[]> {
  try {
    let allBlocks: any[] = [];
    let cursor: string | undefined = undefined;
    let hasMore = true;

    // Fetch all blocks with pagination
    while (hasMore) {
      const response = await notion.blocks.children.list({
        block_id: blockId,
        start_cursor: cursor,
        page_size: 100, // Maximum allowed by Notion API
      });

      allBlocks.push(...response.results);

      hasMore = response.has_more;
      cursor = response.next_cursor || undefined;
    }

    // Recursively fetch children for blocks that have them
    const blocksWithChildren = await Promise.all(
      allBlocks.map(async (block: any) => {
        if (block.has_children) {
          const children = await getBlockChildren(block.id);
          return { ...block, children };
        }
        return block;
      })
    );

    return blocksWithChildren;
  } catch (error) {
    console.error(`Error fetching children for block ${blockId}:`, error);
    return [];
  }
}

export async function getPageBlocks(pageId: string) {
  // console.log("NOTION_TOKEN:", process.env.NOTION_TOKEN);
  try {
    let allBlocks: any[] = [];
    let cursor: string | undefined = undefined;
    let hasMore = true;

    // Fetch all blocks with pagination
    while (hasMore) {
      const response = await notion.blocks.children.list({
        block_id: pageId,
        start_cursor: cursor,
        page_size: 100, // Maximum allowed by Notion API
      });

      allBlocks.push(...response.results);

      hasMore = response.has_more;
      cursor = response.next_cursor || undefined;
    }

    // console.log(`Fetched ${allBlocks.length} total blocks for page ${pageId}`);

    // Recursively fetch children for blocks that have them
    const blocksWithChildren = await Promise.all(
      allBlocks.map(async (block: any) => {
        if (block.has_children) {
          const children = await getBlockChildren(block.id);
          return { ...block, children };
        }
        return block;
      })
    );

    return blocksWithChildren;
  } catch (error) {
    console.error("Error fetching page blocks:", error);
    return [];
  }
}
