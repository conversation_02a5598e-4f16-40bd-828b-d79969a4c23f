"use client";
import { AlignCenter } from "react-feather";
import { useEffect, useState } from "react";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { isEmailVerified, refreshUser } from "@/services/usersServices";

import { Badge } from "@/components/ui/badge";
import { Divider } from "@heroui/react";
const ProfileInfoConnAccount = (props: any) => {
  const [isVerified, setIsVerified] = useState(false);

  // fetch data from firebase
  const auth = useAuth();
  const profile = useProfile(auth?.userData?.uid);

  const FormatAddress = (address: string): string => {
    if (!address.startsWith("0x") || address.length < 4) return address;
    return `0x...${address.slice(-2)}`;
  };

  const checkVerification = async () => {
    await refreshUser(); // Refresh user info before checking
    const response = isEmailVerified();
    // console.log(response);
    if (response?.verified) {
      setIsVerified(true);
    }
  };

  useEffect(() => {
    checkVerification();
  }, []);

  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">
                Connected Account
              </span>
            </div>
          </div>
          <div className="mt-4 max-md:mt-2">
            <Badge
              variant="secondary"
              className="inline-flex items-center gap-2 mr-2 mb-2 px-4 py-[2px] text-[#404040] bg-[#EEEEEE] text-sm font-normal w-auto"
            >
              <img
                src="/assets/lens.png"
                alt="logo"
                className="h-4 grayscale opacity-20"
              />

              <span>{props.lens_id.split("/")[1]}</span>

              <Divider
                orientation="vertical"
                className="h-4 w-[1px] bg-[#ccc]"
              />

              <img
                src="/assets/lens.png"
                alt="eth"
                className="h-4 grayscale opacity-20"
              />

              <span className="font-mono text-xs">
                {FormatAddress(props?.wallet_id)}
              </span>

              <Divider
                orientation="vertical"
                className="h-4 w-[1px] bg-[#ccc]"
              />
              <AlignCenter strokeWidth="1" className="w-4 h-4" />
            </Badge>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfileInfoConnAccount;
