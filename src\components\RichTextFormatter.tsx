import React from "react";

interface RichTextFormatterProps {
  text: string;
  className?: string;
  linkClassName?: string;
  preserveWhitespace?: boolean;
  enableMarkdown?: boolean;
}

const RichTextFormatter: React.FC<RichTextFormatterProps> = ({
  text,
  className = "text-subtitle break-words",
  linkClassName = "text-blue-600 underline hover:text-blue-800 transition-colors",
  preserveWhitespace = true,
  enableMarkdown = true,
}) => {
  // If no text is provided, return null
  if (!text) return null;

  // Enhanced URL regex that handles more URL formats and line breaks
  const urlRegex =
    /(https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.\-~!$&'()*+,;=:@])*)?(?:\?(?:[\w&=%.\-~!$'()*+,;=:@])*)?(?:\#(?:[\w.\-~!$&'()*+,;=:@])*)?)/g;

  const preprocessText = (text: string): string => {
    // Handle URLs that are broken across lines
    // This handles cases where URLs are split across multiple lines due to text wrapping

    // Pattern 1: URL ending with / followed by path on next line
    let result = text.replace(
      /(https?:\/\/[^\s]*\/)\s*\n\s*([a-zA-Z0-9\/_.\-~!$&'()*+,;=:@#?%]+)/g,
      "$1$2"
    );

    // Pattern 2: URL without trailing / followed by path on next line
    result = result.replace(
      /(https?:\/\/[^\s\/]+)\s*\n\s*([a-zA-Z0-9\/_.\-~!$&'()*+,;=:@#?%]+)/g,
      (match, urlStart, urlEnd) => {
        // Only join if the second part starts with / or looks like a continuation
        if (urlEnd.startsWith("/") || urlEnd.startsWith("?") || urlEnd.startsWith("#")) {
          return urlStart + urlEnd;
        }
        // For paths without leading /, add the / separator
        if (/^[a-zA-Z0-9\-_]+/.test(urlEnd)) {
          return urlStart + "/" + urlEnd;
        }
        return match;
      }
    );

    // Pattern 3: Handle query parameters or fragments split across lines
    result = result.replace(/(https?:\/\/[^\s]*[?&])\s*\n\s*([a-zA-Z0-9=&%.\-_]+)/g, "$1$2");

    return result;
  };

  const processText = (inputText: string): React.ReactNode[] => {
    // First, preprocess to handle broken URLs
    const preprocessedText = preprocessText(inputText);

    // Then split by URLs
    const urlParts = preprocessedText.split(urlRegex);

    return urlParts.map((part, index) => {
      // If this part is a URL, return it as a link
      if (part.match(urlRegex)) {
        return (
          <a
            key={`url-${index}`}
            href={part}
            className={linkClassName}
            target="_blank"
            rel="noopener noreferrer"
          >
            {part}
          </a>
        );
      }

      // If markdown is disabled, return plain text
      if (!enableMarkdown) {
        return <span key={`text-${index}`}>{part}</span>;
      }

      // Process markdown-like formatting
      return processMarkdown(part, index);
    });
  };

  const processMarkdown = (text: string, baseIndex: number): React.ReactNode => {
    // Handle different markdown patterns
    const patterns = [
      // Bold: **text** or __text__
      {
        regex: /(\*\*|__)(.*?)\1/g,
        component: (match: string, key: string) => (
          <strong key={key} className="font-semibold">
            {match}
          </strong>
        ),
      },
      // Italic: *text* or _text_ (but not if it's part of __ or **)
      {
        regex: /(?<!\*)\*(?!\*)([^*]+)\*(?!\*)|(?<!_)_(?!_)([^_]+)_(?!_)/g,
        component: (match: string, key: string) => (
          <em key={key} className="italic">
            {match}
          </em>
        ),
      },
      // Code: `text`
      {
        regex: /`([^`]+)`/g,
        component: (match: string, key: string) => (
          <code
            key={key}
            className="bg-gray-100 text-red-600 px-1.5 py-0.5 rounded text-sm font-mono"
          >
            {match}
          </code>
        ),
      },
      // Strikethrough: ~~text~~
      {
        regex: /~~(.*?)~~/g,
        component: (match: string, key: string) => (
          <s key={key} className="line-through">
            {match}
          </s>
        ),
      },
    ];

    let result: React.ReactNode[] = [text];

    patterns.forEach((pattern, patternIndex) => {
      result = result.flatMap((node, nodeIndex) => {
        if (typeof node !== "string") return [node];

        const matches = [...node.matchAll(pattern.regex)];

        if (matches.length === 0) return [node];

        const elements: React.ReactNode[] = [];
        let lastIndex = 0;

        matches.forEach((match, matchIndex) => {
          const matchStart = node.indexOf(match[0], lastIndex);

          // Add text before the match
          if (matchStart > lastIndex) {
            elements.push(
              <span key={`${baseIndex}-${patternIndex}-${nodeIndex}-text-${matchIndex}`}>
                {node.substring(lastIndex, matchStart)}
              </span>
            );
          }

          // Add the formatted match (extract content without markers)
          let content = match[1] || match[2];
          if (!content) {
            // Fallback: remove the markdown markers manually
            if (match[0].startsWith("**") || match[0].startsWith("__")) {
              content = match[0].slice(2, -2);
            } else if (match[0].startsWith("*") || match[0].startsWith("_")) {
              content = match[0].slice(1, -1);
            } else if (match[0].startsWith("`")) {
              content = match[0].slice(1, -1);
            } else if (match[0].startsWith("~~")) {
              content = match[0].slice(2, -2);
            } else {
              content = match[0];
            }
          }

          elements.push(
            pattern.component(
              content,
              `${baseIndex}-${patternIndex}-${nodeIndex}-match-${matchIndex}`
            )
          );

          lastIndex = matchStart + match[0].length;
        });

        // Add remaining text
        if (lastIndex < node.length) {
          elements.push(
            <span key={`${baseIndex}-${patternIndex}-${nodeIndex}-end`}>
              {node.substring(lastIndex)}
            </span>
          );
        }

        return elements;
      });
    });

    return <span key={`markdown-${baseIndex}`}>{result}</span>;
  };

  const baseClassName = preserveWhitespace ? `${className} whitespace-pre-wrap` : className;

  return <div className={baseClassName}>{processText(text)}</div>;
};

export default RichTextFormatter;
