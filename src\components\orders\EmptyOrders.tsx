import React from 'react';
import { ShoppingBag } from 'react-feather';

interface EmptyOrdersProps {
  message?: string;
}

const EmptyOrders: React.FC<EmptyOrdersProps> = ({ 
  message = "You don't have any orders yet" 
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="bg-gray-50 rounded-full p-6 mb-4">
        <ShoppingBag size={48} className="text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">No Orders Found</h3>
      <p className="text-gray-500 text-center max-w-sm">{message}</p>
      <button 
        className="mt-6 px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition-colors"
        onClick={() => window.location.href = '/'}
      >
        Browse Services
      </button>
    </div>
  );
};

export default EmptyOrders; 