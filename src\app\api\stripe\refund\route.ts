import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const { 
      chargeId, 
      paymentIntentId, 
      amount, 
      reason, 
      refundApplicationFee, 
      reverseTransfer, 
      metadata,
      isUS = false 
    } = await request.json();

    if (!chargeId && !paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Either charge ID or payment intent ID is required'
      }, { status: 400 });
    }

    console.log('💰 ===== CREATING STRIPE REFUND =====');
    console.log(`🔗 Charge ID: ${chargeId}`);
    console.log(`💳 Payment Intent ID: ${paymentIntentId}`);
    console.log(`💵 Amount: ${amount ? `$${(amount / 100).toFixed(2)}` : 'Full refund'}`);
    console.log(`📝 Reason: ${reason || 'requested_by_customer'}`);
    console.log(`🌍 Using US Stripe: ${isUS}`);
    console.log('🕐 Timestamp:', new Date().toISOString());

    let targetChargeId = chargeId;

    // If only payment intent provided, get the charge ID
    if (!chargeId && paymentIntentId) {
      console.log('🔄 Getting charge from payment intent...');
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      if (paymentIntent.latest_charge) {
        targetChargeId = paymentIntent.latest_charge as string;
        console.log(`✅ Found charge: ${targetChargeId}`);
      } else {
        return NextResponse.json({
          success: false,
          error: 'No charge found for this payment intent',
          paymentIntentId
        }, { status: 400 });
      }
    }

    // Get charge details before refund
    console.log('🔄 Retrieving charge details...');
    const charge = await stripe.charges.retrieve(targetChargeId);
    
    console.log('📊 Charge Details:', {
      id: charge.id,
      amount: charge.amount,
      amountFormatted: `$${(charge.amount / 100).toFixed(2)}`,
      currency: charge.currency,
      status: charge.status,
      captured: charge.captured,
      refunded: charge.refunded,
      amountRefunded: charge.amount_refunded,
      amountRefundedFormatted: `$${(charge.amount_refunded / 100).toFixed(2)}`
    });

    if (charge.refunded) {
      return NextResponse.json({
        success: false,
        error: 'This charge has already been fully refunded',
        charge: {
          id: charge.id,
          amountRefunded: charge.amount_refunded,
          amountRefundedFormatted: `$${(charge.amount_refunded / 100).toFixed(2)}`
        }
      }, { status: 400 });
    }

    if (!charge.captured) {
      return NextResponse.json({
        success: false,
        error: 'Cannot refund an uncaptured charge. Cancel the payment intent instead.',
        charge: {
          id: charge.id,
          status: charge.status,
          captured: charge.captured
        }
      }, { status: 400 });
    }

    // Calculate refund amount
    const maxRefundableAmount = charge.amount - charge.amount_refunded;
    const refundAmount = amount ? Math.min(amount, maxRefundableAmount) : maxRefundableAmount;

    if (refundAmount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'No amount available for refund',
        charge: {
          id: charge.id,
          totalAmount: charge.amount,
          alreadyRefunded: charge.amount_refunded,
          availableForRefund: maxRefundableAmount
        }
      }, { status: 400 });
    }

    console.log('💰 Refund Calculation:', {
      requestedAmount: amount ? `$${(amount / 100).toFixed(2)}` : 'Full refund',
      maxRefundableAmount: `$${(maxRefundableAmount / 100).toFixed(2)}`,
      actualRefundAmount: `$${(refundAmount / 100).toFixed(2)}`
    });

    // Create the refund
    console.log('🔄 Creating refund...');
    const refundData: Stripe.RefundCreateParams = {
      charge: targetChargeId,
      amount: refundAmount,
      reason: reason || 'requested_by_customer',
      metadata: {
        refundedAt: new Date().toISOString(),
        refundedBy: 'api',
        originalChargeAmount: charge.amount.toString(),
        ...metadata
      }
    };

    // Add optional parameters
    if (refundApplicationFee !== undefined) {
      refundData.refund_application_fee = refundApplicationFee;
    }
    if (reverseTransfer !== undefined) {
      refundData.reverse_transfer = reverseTransfer;
    }

    const refund = await stripe.refunds.create(refundData);

    console.log('✅ Refund created successfully:', {
      refundId: refund.id,
      amount: refund.amount,
      amountFormatted: `$${(refund.amount / 100).toFixed(2)}`,
      status: refund.status,
      reason: refund.reason
    });

    // Get updated charge information
    console.log('🔄 Getting updated charge information...');
    const updatedCharge = await stripe.charges.retrieve(targetChargeId);

    const responseData = {
      success: true,
      message: 'Refund created successfully',
      refund: {
        id: refund.id,
        amount: refund.amount,
        amountFormatted: `$${(refund.amount / 100).toFixed(2)}`,
        currency: refund.currency,
        status: refund.status,
        reason: refund.reason,
        created: refund.created,
        createdFormatted: new Date(refund.created * 1000).toISOString()
      },
      charge: {
        id: updatedCharge.id,
        originalAmount: updatedCharge.amount,
        originalAmountFormatted: `$${(updatedCharge.amount / 100).toFixed(2)}`,
        totalRefunded: updatedCharge.amount_refunded,
        totalRefundedFormatted: `$${(updatedCharge.amount_refunded / 100).toFixed(2)}`,
        remainingAmount: updatedCharge.amount - updatedCharge.amount_refunded,
        remainingAmountFormatted: `$${((updatedCharge.amount - updatedCharge.amount_refunded) / 100).toFixed(2)}`,
        fullyRefunded: updatedCharge.refunded,
        status: updatedCharge.status
      },
      paymentIntent: paymentIntentId ? {
        id: paymentIntentId,
        status: charge.payment_intent ? 'linked' : 'not_linked'
      } : null,
      isUSStripeUsed: Boolean(isUS === 'true'),
      timestamp: new Date().toISOString()
    };

    console.log('✅ ===== REFUND COMPLETED =====');
    console.log(`💰 Refund ID: ${refund.id}`);
    console.log(`💵 Amount Refunded: $${(refund.amount / 100).toFixed(2)}`);
    console.log(`📊 Status: ${refund.status}`);
    console.log(`🔗 Charge ID: ${targetChargeId}`);
    console.log('🔚 ===== END REFUND =====');

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('❌ Error creating refund:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json({
        success: false,
        error: 'Stripe API error',
        details: error.message,
        type: error.type,
        code: error.code
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to create refund',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET method for convenience
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chargeId = searchParams.get('charge_id');
    const paymentIntentId = searchParams.get('payment_intent_id');
    const amount = searchParams.get('amount');
    const reason = searchParams.get('reason');
    const isUS = searchParams.get('isUS') === 'true';

    if (!chargeId && !paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Either charge ID or payment intent ID is required'
      }, { status: 400 });
    }

    // Convert to POST request format
    const postRequest = new NextRequest(request.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        chargeId, 
        paymentIntentId, 
        amount: amount ? parseInt(amount) : undefined,
        reason,
        isUS 
      })
    });

    return POST(postRequest);

  } catch (error) {
    console.error('❌ Error in GET refund:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to process refund request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
