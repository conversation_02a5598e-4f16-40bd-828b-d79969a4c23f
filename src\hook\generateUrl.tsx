import { useState, useEffect } from "react";

const useGenerateUrl = (postFile: string | undefined) => {
  const [url, setUrl] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (!postFile) {
      setUrl(undefined);
      return;
    }

    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    // If postFile already contains the baseUrl, use it as is
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      setUrl(postFile);
    } else {
      setUrl(`${baseUrl}${encodeURIComponent(postFile)}?alt=media`);
    }
  }, [postFile]); // Runs when postFile changes

  return url;
};

export default useGenerateUrl;
