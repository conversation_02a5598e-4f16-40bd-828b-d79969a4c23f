import Link from "next/link";
import React, { useState } from "react";
import LazyMedia from "@/components/LazyMedia";

const VideoWithThumbnail = (props: any) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) {
      // console.error("postFile is undefined or invalid");
      return undefined;
    }

    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const thumbnailUrl = `${generateFileUrl(props.postFile)}#t=0.1`;

  const handlePlay = () => {
    setIsPlaying(true);
  };

  return (
    <div
      className="h-full w-full"
      style={{
        minHeight: `${props.h}px`,
        maxHeight: `${props.h}px`,
      }}
    >
      {!isPlaying ? (
        <div
          className="h-full w-full cursor-pointer relative border-2"
          style={{
            borderColor: props.borderColor,
            minHeight: `${props.h}px`,
            maxHeight: `${props.h}px`,
          }}
          onClick={handlePlay}
        >
          {/* Thumbnail image */}
          {/* <img
            src={thumbnailUrl}
            alt="Video Thumbnail"
            className="h-full w-full object-cover border-2"
            style={{
              borderColor: props.borderColor,
              minHeight: `${props.h}px`,
              maxHeight: `${props.h}px`,
            }}
          /> */}
          {/* Play button overlay */}
          <div
            className="absolute inset-0 flex items-center justify-center border-2 "
            style={{
              backgroundColor: props.borderColor,
              opacity: 0.5,
              borderColor: props.borderColor,
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="white"
              viewBox="0 0 24 24"
              width="48"
              height="48"
            >
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>
        </div>
      ) : (
        <Link
          href={`/browse/${
            props.category === "Storytelling" ? "Literature" : props.category
          }/${props.id}`}
          className="block"
        >
          <LazyMedia
            src={generateFileUrl(props.postFile)}
            type="video"
            className="h-full w-full object-cover border-2"
            style={{
              borderColor: props.borderColor,
              minHeight: `${props.h}px`,
              maxHeight: `${props.h}px`,
            }}
            placeholderClassName="bg-gray-100"
            controls={false}
            autoPlay={false}
            muted={true}
          />
        </Link>
      )}
    </div>
  );
};

export default VideoWithThumbnail;
