import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeCapturePaymentRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { paymentId, isUS }: StripeCapturePaymentRequest = req.body;

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const paymentMethod = await stripeService.paymentIntents.capture(paymentId);

    res.status(200).json({
      paymentMethod,
      success: true,
    });

  } catch (error) {
    console.error('Error capturing payment:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
