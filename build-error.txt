Command failed: npx next build

Failed to compile.

./src/app/layoout.server.tsx
1:15  Warning: 'Metadata' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/page.tsx
6:10  Warning: 'ConnectKitProvider' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/post-details/page.tsx
3:17  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'AnyPublicationMetadataFieldsFragment' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'CommentFieldsFragment' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:3  Warning: 'LensTransactionStatusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'Maybe' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'MetadataAttribute' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'MetadataAttributeType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:3  Warning: 'NewPublicationStatsDocument' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'PaginatedPublicationsResult' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:3  Warning: 'PaginatedPublicationsTagsResult' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'ProfileWhoReactedResult' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:3  Warning: 'PublicationCommentOn' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'PublicationMetadataLicenseType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'PublicationReactionType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:3  Warning: 'PublicationReportingFraudSubreason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:3  Warning: 'PublicationReportingReason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:3  Warning: 'PublicationReportingSpamSubreason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:3  Warning: 'useAddReactionMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:3  Warning: 'useCreateMomokaCommentTypedDataMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:8  Warning: 'useLogin' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:3  Warning: 'deleteUserDetails' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:3  Warning: 'getUsersByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
52:3  Warning: 'toggleBookMarks' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
54:10  Warning: 'logOut' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
55:10  Warning: 'getId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
57:3  Warning: 'getEventsByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
58:3  Warning: 'getEventsByUserId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:3  Warning: 'getServicesByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:3  Warning: 'getServicesByUserId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
64:10  Warning: 'usePostQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
69:10  Warning: 'searchValue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
69:23  Warning: 'setSearchValue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:29  Warning: 'setCurrentFeedCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
74:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
79:17  Warning: 'transactionData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
79:43  Warning: 'refetchTransactionStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
90:17  Warning: 'feedData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
109:11  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
109:28  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
117:16  Warning: 'commentsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
119:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
139:16  Warning: 'liked_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
141:12  Warning: 'liked_error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
160:9  Warning: 'addReaction' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
162:24  Warning: 'reportPost' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
198:9  Warning: 'sendEmail' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
199:11  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
216:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
235:7  Warning: 'alert' is not defined.  no-undef
241:13  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
258:7  Warning: 'console' is not defined.  no-undef
262:9  Warning: 'deleteUser' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
276:7  Warning: 'console' is not defined.  no-undef
277:7  Warning: 'alert' is not defined.  no-undef
330:19  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
345:19  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/post-details-1/page.tsx
2:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:10  Warning: 'CommentOnMomokaMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:35  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'AccountsBulkQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'AccountsOrderBy' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:3  Warning: 'Follower' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'useAccountsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:3  Warning: 'useFullAccountQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
39:10  Warning: 'CommentManager' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
41:3  Warning: 'deletePost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:3  Warning: 'getAllPostsTest' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:3  Warning: 'getUsersSortedByLastPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:3  Warning: 'createService' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:3  Warning: 'deleteCustomization' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:3  Warning: 'getCustomisations' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:3  Warning: 'getServiceById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
51:3  Warning: 'getServicesByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
52:3  Warning: 'getServicesByUserId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
56:25  Warning: 'Web3Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
67:11  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
67:28  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
90:39  Warning: 'followersLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
116:40  Warning: 'followingsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
155:16  Warning: 'commentsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
201:16  Warning: 'postsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
203:12  Warning: 'postsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
233:17  Warning: 'profileData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
255:17  Warning: 'profileDataByLensId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
276:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
311:24  Warning: 'setInterval' is not defined.  no-undef
313:9  Warning: 'alert' is not defined.  no-undef
314:9  Warning: 'clearInterval' is not defined.  no-undef
322:9  Warning: 'alert' is not defined.  no-undef
323:9  Warning: 'clearInterval' is not defined.  no-undef
330:18  Warning: 'clearInterval' is not defined.  no-undef
336:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
336:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
348:13  Warning: 'formData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
403:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
415:7  Warning: 'console' is not defined.  no-undef
419:12  Warning: 'files' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
423:9  Warning: 'handleFileChange' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
423:41  Warning: 'React' is not defined.  no-undef
423:59  Warning: 'HTMLInputElement' is not defined.  no-undef
430:7  Warning: 'console' is not defined.  no-undef
440:5  Warning: 'console' is not defined.  no-undef
456:9  Warning: 'image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
460:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
473:5  Warning: 'console' is not defined.  no-undef
517:13  Warning: 'console' is not defined.  no-undef
579:19  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
594:17  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
610:11  Warning: 'console' is not defined.  no-undef
623:11  Warning: 'console' is not defined.  no-undef
651:61  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/profile/page.tsx
14:19  Warning: 'setTimeout' is not defined.  no-undef
21:18  Warning: 'clearTimeout' is not defined.  no-undef

./src/app/profile/[userId]/page.tsx
2:10  Warning: 'Suspense' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:6  Warning: 'CategoryProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/search_temp/page.tsx
1:8  Warning: 'LensPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:10  Warning: 'useSearchProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/temp/page.tsx
1:8  Warning: 'LensPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:8  Warning: 'CalendarComp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:8  Warning: 'CalendarCompEvent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/test/page.tsx
2:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:17  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:8  Warning: 'FeedPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:3  Warning: 'FraudSubreason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'ReportingReason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'ReportManager' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:16  Warning: 'searchLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
38:12  Warning: 'searchError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:17  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
63:39  Warning: 'followers_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
72:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/testFirebase/page.tsx
43:9  Warning: 'date' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/bottomArrow.tsx
2:10  Warning: 'ChevronsDown' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:14  Warning: 'React' is not defined.  no-undef
5:30  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/CardSkeleton/EventCardSkeleton.tsx
6:3  Warning: 'CardDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/FeedPost.tsx
7:10  Warning: 'ThemeProvider' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'Play' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:6  Warning: 'Props' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:49  Warning: 'data' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/filter.tsx
2:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:3  Warning: 'SheetFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:7  Warning: 'filterData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:10  Warning: 'screen1' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:19  Warning: 'setScreen1' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:10  Warning: 'screen2' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:19  Warning: 'setScreen2' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
44:10  Warning: 'screen3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
44:19  Warning: 'setScreen3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:10  Warning: 'screen4' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:19  Warning: 'setScreen4' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:10  Warning: 'screen5' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:19  Warning: 'setScreen5' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
99:36  Error: This number literal will lose precision at runtime.  no-loss-of-precision

./src/components/GlobalSignInButton.tsx
40:21  Warning: 'setTimeout' is not defined.  no-undef
44:20  Warning: 'clearTimeout' is not defined.  no-undef
49:31  Warning: 'document' is not defined.  no-undef
59:22  Warning: 'setInterval' is not defined.  no-undef
61:18  Warning: 'clearInterval' is not defined.  no-undef
68:36  Warning: 'MouseEvent' is not defined.  no-undef
71:34  Warning: 'HTMLElement' is not defined.  no-undef
83:19  Warning: 'setTimeout' is not defined.  no-undef
84:7  Warning: 'document' is not defined.  no-undef
88:7  Warning: 'clearTimeout' is not defined.  no-undef
89:7  Warning: 'document' is not defined.  no-undef
122:28  Warning: 'HTMLButtonElement' is not defined.  no-undef
183:5  Warning: 'setTimeout' is not defined.  no-undef
188:7  Warning: 'setTimeout' is not defined.  no-undef
199:9  Warning: 'navigator' is not defined.  no-undef
215:21  Warning: 'setTimeout' is not defined.  no-undef
222:20  Warning: 'clearTimeout' is not defined.  no-undef
238:21  Warning: 'setTimeout' is not defined.  no-undef
242:20  Warning: 'clearTimeout' is not defined.  no-undef
250:21  Warning: 'setTimeout' is not defined.  no-undef
254:20  Warning: 'clearTimeout' is not defined.  no-undef
266:30  Warning: 'setTimeout' is not defined.  no-undef
276:13  Warning: 'window' is not defined.  no-undef
280:22  Warning: 'clearTimeout' is not defined.  no-undef
287:25  Warning: 'setTimeout' is not defined.  no-undef
291:13  Warning: 'window' is not defined.  no-undef
294:24  Warning: 'clearTimeout' is not defined.  no-undef
372:41  Warning: 'navigator' is not defined.  no-undef
374:25  Warning: 'window' is not defined.  no-undef
379:25  Warning: 'window' is not defined.  no-undef
383:21  Warning: 'console' is not defined.  no-undef
551:35  Warning: 'navigator' is not defined.  no-undef
553:35  Warning: 'window' is not defined.  no-undef
558:35  Warning: 'window' is not defined.  no-undef
563:31  Warning: 'setTimeout' is not defined.  no-undef
573:29  Warning: 'console' is not defined.  no-undef
734:33  Warning: 'localStorage' is not defined.  no-undef
748:39  Warning: 'window' is not defined.  no-undef
749:40  Warning: 'window' is not defined.  no-undef
751:40  Warning: 'window' is not defined.  no-undef
759:39  Warning: 'navigator' is not defined.  no-undef
762:39  Warning: 'window' is not defined.  no-undef
768:39  Warning: 'window' is not defined.  no-undef
771:37  Warning: 'console' is not defined.  no-undef
784:33  Warning: 'setTimeout' is not defined.  no-undef

./src/components/imageGrid.tsx
10:6  Warning: 'Props' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:37  Warning: 'data' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
26:17  Error: Unexpected constant truthiness on the left-hand side of a `&&` expression.  no-constant-binary-expression

./src/components/leftArrow.tsx
2:10  Warning: 'ChevronsLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:14  Warning: 'React' is not defined.  no-undef
5:30  Warning: 'HTMLDivElement' is not defined.  no-undef
24:5  Warning: 'window' is not defined.  no-undef
31:7  Warning: 'window' is not defined.  no-undef
45:5  Warning: 'console' is not defined.  no-undef
46:10  Warning: 'console' is not defined.  no-undef

./src/components/lensPost.tsx
3:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:25  Warning: 'Web3Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'ImageGrid' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:57  Warning: 'PostQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:6  Warning: 'Props' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:36  Warning: 'userId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
19:9  Warning: 'id' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
170:10  Warning: 'filteredData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
272:15  Error: Unexpected constant condition.  no-constant-condition

./src/components/loadingOverlay.tsx
20:22  Warning: 'setInterval' is not defined.  no-undef
24:18  Warning: 'clearInterval' is not defined.  no-undef

./src/components/modals/SendVerificationModal.tsx
4:25  Warning: 'X' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
28:21  Warning: 'setTimeout' is not defined.  no-undef
29:20  Warning: 'clearTimeout' is not defined.  no-undef
58:7  Warning: 'console' is not defined.  no-undef

./src/components/navbar.tsx
4:38  Warning: 'X' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
41:9  Warning: 'console' is not defined.  no-undef
44:7  Warning: 'alert' is not defined.  no-undef
45:7  Warning: 'console' is not defined.  no-undef

./src/components/Profile.tsx
9:28  Warning: 'profileId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
18:3  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/rightArrow.tsx
2:10  Warning: 'ChevronsRight' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:14  Warning: 'React' is not defined.  no-undef
5:30  Warning: 'HTMLDivElement' is not defined.  no-undef
26:5  Warning: 'window' is not defined.  no-undef
33:7  Warning: 'window' is not defined.  no-undef

./src/components/sidebar.tsx
213:10  Warning: 'selectedChatId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
349:7  Warning: 'indexedDB' is not defined.  no-undef
356:5  Warning: 'localStorage' is not defined.  no-undef
357:5  Warning: 'window' is not defined.  no-undef
358:5  Warning: 'window' is not defined.  no-undef
364:21  Warning: 'process' is not defined.  no-undef

./src/components/SignInButton.tsx
20:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
28:7  Warning: 'navigator' is not defined.  no-undef
36:38  Error: Unexpected empty object pattern.  no-empty-pattern
86:3  Warning: 'console' is not defined.  no-undef
122:21  Warning: 'setTimeout' is not defined.  no-undef
126:20  Warning: 'clearTimeout' is not defined.  no-undef
134:21  Warning: 'setTimeout' is not defined.  no-undef
138:20  Warning: 'clearTimeout' is not defined.  no-undef
222:43  Warning: 'navigator' is not defined.  no-undef
224:27  Warning: 'window' is not defined.  no-undef
229:27  Warning: 'window' is not defined.  no-undef
233:23  Warning: 'console' is not defined.  no-undef
327:37  Warning: 'window' is not defined.  no-undef
328:38  Warning: 'window' is not defined.  no-undef
330:38  Warning: 'window' is not defined.  no-undef
338:37  Warning: 'navigator' is not defined.  no-undef
341:37  Warning: 'window' is not defined.  no-undef
347:37  Warning: 'window' is not defined.  no-undef
350:35  Warning: 'console' is not defined.  no-undef
400:7  Warning: 'localStorage' is not defined.  no-undef

./src/components/ui/adjacent-drawer.tsx
9:8  Warning: 'CustomCloseButton' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/ui/alert-dialog.tsx
51:25  Warning: 'HTMLDivElement' is not defined.  no-undef
65:25  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/badge.tsx
27:32  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/button.tsx
38:38  Warning: 'HTMLButtonElement' is not defined.  no-undef
43:33  Warning: 'HTMLButtonElement' is not defined.  no-undef

./src/components/ui/calendar.tsx
63:25  Warning: 'props' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
64:26  Warning: 'props' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/ui/card.tsx
6:3  Warning: 'HTMLDivElement' is not defined.  no-undef
7:24  Warning: 'HTMLDivElement' is not defined.  no-undef
21:3  Warning: 'HTMLDivElement' is not defined.  no-undef
22:24  Warning: 'HTMLDivElement' is not defined.  no-undef
33:3  Warning: 'HTMLDivElement' is not defined.  no-undef
34:24  Warning: 'HTMLDivElement' is not defined.  no-undef
45:3  Warning: 'HTMLDivElement' is not defined.  no-undef
46:24  Warning: 'HTMLDivElement' is not defined.  no-undef
57:3  Warning: 'HTMLDivElement' is not defined.  no-undef
58:24  Warning: 'HTMLDivElement' is not defined.  no-undef
65:3  Warning: 'HTMLDivElement' is not defined.  no-undef
66:24  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/carousel.tsx
46:3  Warning: 'HTMLDivElement' is not defined.  no-undef
47:24  Warning: 'HTMLDivElement' is not defined.  no-undef
89:35  Warning: 'HTMLDivElement' is not defined.  no-undef
154:3  Warning: 'HTMLDivElement' is not defined.  no-undef
155:24  Warning: 'HTMLDivElement' is not defined.  no-undef
176:3  Warning: 'HTMLDivElement' is not defined.  no-undef
177:24  Warning: 'HTMLDivElement' is not defined.  no-undef
198:3  Warning: 'HTMLButtonElement' is not defined.  no-undef
227:3  Warning: 'HTMLButtonElement' is not defined.  no-undef

./src/components/ui/dropdown-menu.tsx
175:25  Warning: 'HTMLSpanElement' is not defined.  no-undef

./src/components/ui/input.tsx
5:32  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/components/ui/sheet.tsx
89:25  Warning: 'HTMLDivElement' is not defined.  no-undef
103:25  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/sidebarSheet.tsx
5:64  Warning: 'HTMLDivElement' is not defined.  no-undef
12:5  Warning: 'open' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
13:5  Warning: 'onOpenChange' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
23:61  Warning: 'HTMLDivElement' is not defined.  no-undef
39:66  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/sonner.tsx
6:21  Warning: 'React' is not defined.  no-undef

./src/components/ui/textarea.tsx
6:3  Warning: 'HTMLTextAreaElement' is not defined.  no-undef

./src/firebase/authService.ts
3:3  Warning: 'Auth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:26  Warning: 'console' is not defined.  no-undef
35:5  Warning: 'console' is not defined.  no-undef
42:3  Error: Unnecessary try/catch wrapper.  no-useless-catch
59:3  Error: Unnecessary try/catch wrapper.  no-useless-catch
62:5  Warning: 'localStorage' is not defined.  no-undef
78:5  Warning: 'console' is not defined.  no-undef
92:5  Warning: 'console' is not defined.  no-undef
110:11  Warning: 'accessToken' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
117:5  Warning: 'console' is not defined.  no-undef
121:7  Warning: 'console' is not defined.  no-undef

./src/globalComponents/globalCardEvents.tsx
4:3  Warning: 'CardContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'CardFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:10  Warning: 'calculateDateDifference' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
84:5  Warning: 'console' is not defined.  no-undef

./src/globalComponents/globalProfileCard.tsx
28:21  Warning: 'process' is not defined.  no-undef
43:11  Warning: 'console' is not defined.  no-undef
56:9  Warning: 'console' is not defined.  no-undef
71:11  Warning: 'console' is not defined.  no-undef
84:9  Warning: 'console' is not defined.  no-undef
183:13  Warning: 'console' is not defined.  no-undef
198:21  Warning: 'sessionStorage' is not defined.  no-undef
199:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/globalComponents/globalProfileCardLens.tsx
4:8  Warning: 'useProfile' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:10  Warning: 'FollowerManager' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:10  Warning: 'getUserById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:10  Warning: 'Weight' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:11  Warning: 'isSignedInQuery' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:28  Warning: 'profileQuery' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:9  Warning: 'isAuthW3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
44:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
101:7  Warning: 'requestAnimationFrame' is not defined.  no-undef
194:21  Warning: 'console' is not defined.  no-undef
225:27  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
231:21  Warning: 'console' is not defined.  no-undef

./src/globalComponents/homeComp/postsComp.tsx
8:10  Warning: 'categoryData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:15  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:9  Warning: 'console' is not defined.  no-undef
69:18  Error: Unexpected constant truthiness on the left-hand side of a `&&` expression.  no-constant-binary-expression

./src/globalComponents/imageChunkGrid.tsx
7:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:21  Warning: 'process' is not defined.  no-undef
63:9  Warning: 'console' is not defined.  no-undef

./src/graphql/auth-fetcher.ts
8:13  Warning: 'RequestInit' is not defined.  no-undef
36:23  Warning: 'fetch' is not defined.  no-undef

./src/hook/events/index.tsx
35:14  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars
41:7  Warning: 'setTimeout' is not defined.  no-undef

./src/hook/generateUrl.tsx
12:21  Warning: 'process' is not defined.  no-undef

./src/hook/lensDataFilter.tsx
18:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:18  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:14  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
54:18  Warning: 'loadingPublications' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
55:14  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
100:12  Error: Unsafe usage of optional chaining. If it short-circuits with 'undefined' the evaluation will throw TypeError.  no-unsafe-optional-chaining

./src/hook/multiProfileData.tsx
1:10  Warning: 'User' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:16  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars

./src/hook/profileData.tsx
40:16  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/apollo-client.ts
13:17  Warning: 'localStorage' is not defined.  no-undef

./src/lib/auth/auth-helpers.ts
21:14  Warning: 'localStorage' is not defined.  no-undef
21:30  Warning: 'window' is not defined.  no-undef
31:9  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
59:14  Warning: 'localStorage' is not defined.  no-undef
59:30  Warning: 'window' is not defined.  no-undef
65:9  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:3  Warning: 'Buffer' is not defined.  no-undef

./src/lib/auth/refreshAccessToken.ts
24:15  Warning: 'RequestInit' is not defined.  no-undef
26:23  Warning: 'fetch' is not defined.  no-undef

./src/lib/auth/useLensUser.ts
6:3  Warning: 'useAccountStatsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'console' is not defined.  no-undef
53:5  Warning: 'console' is not defined.  no-undef

./src/lib/auth/useLogin.ts
5:3  Warning: 'PageSize' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'useAccountsAvailableQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'console' is not defined.  no-undef
55:5  Warning: 'console' is not defined.  no-undef
70:5  Warning: 'console' is not defined.  no-undef
103:5  Warning: 'setTimeout' is not defined.  no-undef
104:7  Warning: 'window' is not defined.  no-undef

./src/lib/lens-api.ts
6:17  Warning: 'localStorage' is not defined.  no-undef
8:26  Warning: 'fetch' is not defined.  no-undef

./src/lib/Providers.tsx
6:53  Warning: 'React' is not defined.  no-undef

./src/lib/useBookMark.ts
18:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
25:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useCommen.ts
19:5  Warning: 'ismomoke' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
21:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
28:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useCreatePost.ts
17:29  Warning: 'File' is not defined.  no-undef
18:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
25:36  Warning: 'localStorage' is not defined.  no-undef
40:24  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useDeletePost.ts
14:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
21:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useFollow.ts
17:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
18:7  Warning: 'console' is not defined.  no-undef
25:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useReaction.ts
23:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
30:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useReport.ts
4:3  Warning: 'PublicationReportingReason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:3  Warning: 'useReportPublicationMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
42:36  Warning: 'localStorage' is not defined.  no-undef
46:7  Warning: 'typedData' is never reassigned. Use 'const' instead.  prefer-const

./src/lib/useUnfollow.ts
14:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
21:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useUpdateFeed.ts
28:13  Warning: 'File' is not defined.  no-undef
32:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
42:36  Warning: 'localStorage' is not defined.  no-undef

./src/screens/auth/index.tsx
3:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:20  Warning: 'UserProfile' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:19  Warning: 'React' is not defined.  no-undef
38:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:22  Warning: 'connector' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:33  Warning: 'isConnected' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:49  Warning: React Hook "useAccount" cannot be called in an async function.  react-hooks/rules-of-hooks
67:7  Warning: 'localStorage' is not defined.  no-undef
85:9  Warning: 'UpdateUserData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
101:7  Warning: 'console' is not defined.  no-undef
137:9  Warning: 'localStorage' is not defined.  no-undef
154:11  Warning: 'setTimeout' is not defined.  no-undef
167:15  Warning: 'setTimeout' is not defined.  no-undef
169:17  Warning: 'window' is not defined.  no-undef
172:15  Warning: 'console' is not defined.  no-undef
183:15  Warning: 'window' is not defined.  no-undef
187:11  Warning: 'console' is not defined.  no-undef
189:11  Warning: 'setTimeout' is not defined.  no-undef
201:13  Warning: 'setTimeout' is not defined.  no-undef
203:15  Warning: 'window' is not defined.  no-undef
209:7  Warning: 'console' is not defined.  no-undef
225:20  Warning: 'localStorage' is not defined.  no-undef
228:20  Warning: 'localStorage' is not defined.  no-undef
231:20  Warning: 'localStorage' is not defined.  no-undef
234:20  Warning: 'localStorage' is not defined.  no-undef
281:5  Warning: 'console' is not defined.  no-undef
304:7  Warning: 'localStorage' is not defined.  no-undef
314:7  Warning: 'setTimeout' is not defined.  no-undef
330:11  Warning: 'setTimeout' is not defined.  no-undef
332:13  Warning: 'window' is not defined.  no-undef
335:11  Warning: 'console' is not defined.  no-undef
347:11  Warning: 'setTimeout' is not defined.  no-undef
348:13  Warning: 'window' is not defined.  no-undef
353:7  Warning: 'console' is not defined.  no-undef
367:7  Warning: 'localStorage' is not defined.  no-undef
389:7  Warning: 'setTimeout' is not defined.  no-undef
402:11  Warning: 'setTimeout' is not defined.  no-undef
404:13  Warning: 'window' is not defined.  no-undef
407:11  Warning: 'console' is not defined.  no-undef
419:11  Warning: 'setTimeout' is not defined.  no-undef
420:13  Warning: 'window' is not defined.  no-undef
425:7  Warning: 'console' is not defined.  no-undef
439:7  Warning: 'localStorage' is not defined.  no-undef
461:7  Warning: 'setTimeout' is not defined.  no-undef
474:11  Warning: 'setTimeout' is not defined.  no-undef
476:13  Warning: 'window' is not defined.  no-undef
479:11  Warning: 'console' is not defined.  no-undef
491:11  Warning: 'setTimeout' is not defined.  no-undef
492:13  Warning: 'window' is not defined.  no-undef
497:7  Warning: 'console' is not defined.  no-undef
511:7  Warning: 'localStorage' is not defined.  no-undef
533:7  Warning: 'setTimeout' is not defined.  no-undef
546:11  Warning: 'setTimeout' is not defined.  no-undef
548:13  Warning: 'window' is not defined.  no-undef
551:11  Warning: 'console' is not defined.  no-undef
563:11  Warning: 'setTimeout' is not defined.  no-undef
564:13  Warning: 'window' is not defined.  no-undef
569:7  Warning: 'console' is not defined.  no-undef

./src/screens/basket/basketCard.tsx
7:3  Warning: 'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
101:43  Error: Irregular whitespace not allowed.  no-irregular-whitespace

./src/screens/basket/index.tsx
13:10  Warning: 'isSheetOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/browse/browseLensUser/browseLensData.tsx
78:7  Warning: 'console' is not defined.  no-undef

./src/screens/browse/browseLensUser/browseLensUserCard.tsx
2:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:3  Warning: 'ModalHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:28  Warning: 'getLocation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:27  Warning: 'HTMLDivElement' is not defined.  no-undef
99:10  Warning: 'reload' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
99:18  Warning: 'setSetreload' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
102:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
110:9  Warning: 'handleStarClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
119:9  Warning: 'handleSaveClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
128:9  Warning: 'handleRefresh' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
142:9  Warning: 'handleInfoClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
148:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
152:18  Warning: 'setPostId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
159:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
200:24  Warning: 'setInterval' is not defined.  no-undef
202:9  Warning: 'alert' is not defined.  no-undef
203:9  Warning: 'clearInterval' is not defined.  no-undef
211:9  Warning: 'alert' is not defined.  no-undef
218:9  Warning: 'clearInterval' is not defined.  no-undef
225:18  Warning: 'clearInterval' is not defined.  no-undef
230:7  Warning: 'sessionStorage' is not defined.  no-undef
231:7  Warning: 'sessionStorage' is not defined.  no-undef
236:7  Warning: 'console' is not defined.  no-undef
245:7  Warning: 'alert' is not defined.  no-undef
269:9  Warning: 'console' is not defined.  no-undef
274:15  Warning: 'resplike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
289:15  Warning: 'respdislike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
304:7  Warning: 'alert' is not defined.  no-undef
315:9  Warning: 'console' is not defined.  no-undef
319:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
322:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
328:7  Warning: 'alert' is not defined.  no-undef
352:5  Warning: 'navigator' is not defined.  no-undef
358:9  Warning: 'console' is not defined.  no-undef
368:25  Warning: 'options' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
368:39  Warning: 'pswp' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
369:10  Warning: 'document' is not defined.  no-undef
370:10  Warning: 'window' is not defined.  no-undef
383:21  Warning: 'Image' is not defined.  no-undef
390:27  Warning: 'HTMLVideoElement' is not defined.  no-undef
394:29  Warning: 'HTMLVideoElement' is not defined.  no-undef
665:20  Error: Unexpected constant condition.  no-constant-condition
781:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
860:33  Error: Unexpected constant condition.  no-constant-condition
897:32  Error: Unexpected constant condition.  no-constant-condition
1182:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1280:33  Warning: 'alert' is not defined.  no-undef
1283:31  Warning: 'alert' is not defined.  no-undef
1311:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1352:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1358:23  Warning: 'sessionStorage' is not defined.  no-undef
1359:23  Warning: 'sessionStorage' is not defined.  no-undef
1383:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/browse/browseLensUser/index.tsx
6:3  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'LensTransactionStatusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:3  Warning: 'useLensTransactionStatusQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:10  Warning: 'ChevronsLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:24  Warning: 'ChevronsRight' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:3  Warning: 'AlertDialogFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:3  Warning: 'userPost' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
62:9  Warning: 'txnIdRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
63:10  Warning: 'currentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
63:25  Warning: 'setCurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
70:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
70:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
72:9  Warning: 'profileImage' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
136:9  Warning: 'buttonStyles' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
146:9  Warning: 'window' is not defined.  no-undef
147:9  Warning: 'window' is not defined.  no-undef
148:9  Warning: 'window' is not defined.  no-undef
152:19  Warning: 'document' is not defined.  no-undef
155:29  Warning: 'HTMLElement' is not defined.  no-undef
250:39  Warning: 'console' is not defined.  no-undef
282:45  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
292:39  Warning: 'console' is not defined.  no-undef
382:37  Warning: 'console' is not defined.  no-undef
414:43  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
424:37  Warning: 'console' is not defined.  no-undef
490:34  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
523:24  Warning: 'window' is not defined.  no-undef
574:21  Warning: 'sessionStorage' is not defined.  no-undef
575:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/browse/browseUser/browseUserCard.tsx
13:3  Warning: 'ChevronLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'AlertDialogFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:27  Warning: 'HTMLDivElement' is not defined.  no-undef
80:10  Warning: 'starCount' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
80:21  Warning: 'setStarCount' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
83:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
112:9  Warning: 'handleInfoClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
120:21  Warning: 'process' is not defined.  no-undef
139:16  Warning: 'sessionStorage' is not defined.  no-undef
141:19  Warning: 'sessionStorage' is not defined.  no-undef
144:7  Warning: 'sessionStorage' is not defined.  no-undef
145:7  Warning: 'sessionStorage' is not defined.  no-undef
152:7  Warning: 'sessionStorage' is not defined.  no-undef
153:7  Warning: 'sessionStorage' is not defined.  no-undef
160:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
172:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
174:7  Warning: 'alert' is not defined.  no-undef
211:9  Warning: 'alert' is not defined.  no-undef
232:7  Warning: 'console' is not defined.  no-undef
234:13  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
238:7  Warning: 'console' is not defined.  no-undef
255:7  Warning: 'console' is not defined.  no-undef
257:13  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
261:7  Warning: 'console' is not defined.  no-undef
279:7  Warning: 'console' is not defined.  no-undef
283:7  Warning: 'console' is not defined.  no-undef
300:7  Warning: 'console' is not defined.  no-undef
305:7  Warning: 'console' is not defined.  no-undef
363:25  Warning: 'options' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
363:39  Warning: 'pswp' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
364:10  Warning: 'document' is not defined.  no-undef
365:10  Warning: 'window' is not defined.  no-undef
371:23  Warning: 'Image' is not defined.  no-undef
391:5  Warning: 'navigator' is not defined.  no-undef
397:9  Warning: 'console' is not defined.  no-undef
401:27  Warning: 'HTMLVideoElement' is not defined.  no-undef
405:29  Warning: 'HTMLVideoElement' is not defined.  no-undef
422:9  Warning: 'alert' is not defined.  no-undef
425:7  Warning: 'alert' is not defined.  no-undef
426:7  Warning: 'console' is not defined.  no-undef
703:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1127:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1238:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1257:29  Warning: 'window' is not defined.  no-undef
1304:13  Warning: 'console' is not defined.  no-undef
1319:21  Warning: 'sessionStorage' is not defined.  no-undef
1320:21  Warning: 'sessionStorage' is not defined.  no-undef
1364:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1390:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/browse/browseUser/editPost.tsx
6:10  Warning: 'updateUser' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
38:38  Warning: 'File' is not defined.  no-undef
43:10  Warning: 'showConfirmation' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:28  Warning: 'setShowConfirmation' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:13  Warning: 'alert' is not defined.  no-undef
74:11  Warning: 'console' is not defined.  no-undef
75:11  Warning: 'alert' is not defined.  no-undef
85:35  Warning: 'React' is not defined.  no-undef
89:7  Warning: 'alert' is not defined.  no-undef
105:9  Warning: 'alert' is not defined.  no-undef
131:9  Warning: 'alert' is not defined.  no-undef
136:7  Warning: 'console' is not defined.  no-undef
137:7  Warning: 'alert' is not defined.  no-undef
141:37  Warning: 'React' is not defined.  no-undef
141:55  Warning: 'HTMLInputElement' is not defined.  no-undef
149:26  Warning: 'React' is not defined.  no-undef
149:46  Warning: 'HTMLInputElement' is not defined.  no-undef
167:25  Warning: 'React' is not defined.  no-undef
167:45  Warning: 'HTMLInputElement' is not defined.  no-undef
215:17  Warning: 'window' is not defined.  no-undef
324:30  Warning: 'URL' is not defined.  no-undef

./src/screens/browse/browseUser/index.tsx
22:3  Warning: 'AlertDialogFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:21  Warning: 'process' is not defined.  no-undef
56:16  Warning: React Hook "useAuth" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
59:27  Warning: React Hook "useProfile" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
60:41  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
61:10  Warning: 'triggerEffect' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:45  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
64:24  Warning: React Hook "useCallback" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
75:11  Warning: 'console' is not defined.  no-undef
87:9  Warning: 'console' is not defined.  no-undef
96:26  Warning: React Hook "useCallback" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
107:11  Warning: 'console' is not defined.  no-undef
119:9  Warning: 'console' is not defined.  no-undef
141:3  Warning: React Hook "useEffect" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
145:43  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
149:10  Warning: 'slidesToShow' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
149:43  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
151:21  Warning: React Hook "useRef" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
153:30  Warning: 'swiper' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
153:50  Warning: 'length' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
160:9  Warning: 'console' is not defined.  no-undef
164:47  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
166:3  Warning: React Hook "useEffect" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
178:3  Warning: React Hook "useEffect" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
180:11  Warning: 'window' is not defined.  no-undef
182:18  Warning: 'window' is not defined.  no-undef
190:5  Warning: 'window' is not defined.  no-undef
191:18  Warning: 'window' is not defined.  no-undef
195:9  Warning: 'window' is not defined.  no-undef
196:9  Warning: 'window' is not defined.  no-undef
197:9  Warning: 'window' is not defined.  no-undef
409:26  Warning: 'window' is not defined.  no-undef

./src/screens/browse/index.tsx
1:10  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:21  Warning: 'ProfilesOrderBy' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'FollowersQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'FollowingQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:10  Warning: 'currentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
52:27  Warning: 'HTMLDivElement' is not defined.  no-undef
54:37  Warning: 'NodeJS' is not defined.  no-undef
67:5  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
98:7  Warning: 'console' is not defined.  no-undef
151:7  Warning: 'console' is not defined.  no-undef
214:7  Warning: 'console' is not defined.  no-undef
281:9  Warning: 'clearTimeout' is not defined.  no-undef
296:9  Warning: 'clearTimeout' is not defined.  no-undef
299:36  Warning: 'setTimeout' is not defined.  no-undef
363:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
363:31  Warning: 'HTMLDivElement' is not defined.  no-undef
365:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
372:7  Warning: 'console' is not defined.  no-undef

./src/screens/chat/chatBox.tsx
11:3  Warning: 'chatId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/eventsCard.tsx
7:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:10  Warning: 'userID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:18  Warning: 'setUserID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
104:7  Warning: 'console' is not defined.  no-undef
119:16  Warning: 'NodeJS' is not defined.  no-undef
123:15  Warning: 'setTimeout' is not defined.  no-undef
129:18  Warning: 'clearTimeout' is not defined.  no-undef
142:7  Warning: 'console' is not defined.  no-undef
156:7  Warning: 'console' is not defined.  no-undef
184:33  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/index.tsx
3:10  Warning: 'GlobalCardEvents' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
60:28  Warning: 'HTMLDivElement' is not defined.  no-undef
62:12  Warning: 'hexToRgba' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
111:31  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>
22:28  Warning: 'localStorage' is not defined.  no-undef
27:13  Warning: 'localStorage' is not defined.  no-undef
31:9  Warning: 'console' is not defined.  no-undef
51:20  Warning: 'React' is not defined.  no-undef

./src/screens/home/<USER>/imageCard.tsx
7:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
33:21  Warning: 'process' is not defined.  no-undef
81:9  Warning: 'console' is not defined.  no-undef

./src/screens/home/<USER>/index.tsx
72:28  Warning: 'HTMLDivElement' is not defined.  no-undef
75:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
75:27  Warning: 'HTMLDivElement' is not defined.  no-undef
89:33  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
94:32  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
121:10  Warning: 'imgHeight' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
122:33  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
123:13  Warning: 'pageYOffset' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
123:26  Warning: 'scrollY' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
123:38  Warning: 'window' is not defined.  no-undef
131:45  Warning: 'window' is not defined.  no-undef
136:5  Warning: 'window' is not defined.  no-undef
137:5  Warning: 'window' is not defined.  no-undef
140:7  Warning: 'window' is not defined.  no-undef
142:7  Warning: 'window' is not defined.  no-undef
156:23  Warning: 'setProfileData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:26  Warning: 'setLoadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
158:10  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
158:24  Warning: 'setProfileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
161:10  Warning: 'publicationsData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
163:10  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
408:35  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
462:35  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/postMobileView.tsx
22:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:21  Warning: 'process' is not defined.  no-undef
79:20  Warning: 'postCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
130:7  Warning: 'console' is not defined.  no-undef
149:7  Warning: 'console' is not defined.  no-undef
163:7  Warning: 'console' is not defined.  no-undef
176:7  Warning: 'console' is not defined.  no-undef

./src/screens/home/<USER>/test.tsx
6:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'PublicationMetadataMainFocusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'PublicationType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:3  Warning: 'usePublicationsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'getLensProfileDetails' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:20  Warning: 'postCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
127:7  Warning: 'console' is not defined.  no-undef
146:7  Warning: 'console' is not defined.  no-undef
160:7  Warning: 'console' is not defined.  no-undef
177:9  Warning: 'id' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
196:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
197:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
212:16  Warning: 'isLoadingPublications' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
214:12  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
298:9  Warning: 'loadNextPage' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/videoWithThumbnail.tsx
10:21  Warning: 'process' is not defined.  no-undef
25:9  Warning: 'thumbnailUrl' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/index.tsx
8:8  Warning: 'GlobalProfileCard' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
58:28  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/home/<USER>/profileCard.tsx
1:10  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:23  Warning: 'getUserById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:9  Warning: 'isAuthLogin' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:10  Warning: 'followingList' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:7  Warning: 'console' is not defined.  no-undef
156:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
205:34  Warning: 'setCurrentFollowingCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:31  Warning: 'HTMLDivElement' is not defined.  no-undef
211:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
218:7  Warning: 'console' is not defined.  no-undef
225:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
279:15  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/text.tsx
3:10  Warning: 'GlobalCardEvents' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'GlobalCard' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
56:28  Warning: 'HTMLDivElement' is not defined.  no-undef
139:31  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/textProfile.tsx
1:10  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:21  Warning: 'ProfilesOrderBy' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'LoadingOverlay' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:8  Warning: 'ScrollButton' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'FollowersQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'FollowingQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:10  Warning: 'currentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:27  Warning: 'HTMLDivElement' is not defined.  no-undef
59:5  Warning: 'paramPID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
64:5  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
65:5  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
66:5  Warning: 'refetch' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:7  Warning: 'console' is not defined.  no-undef
203:9  Warning: 'handleScroll' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
245:34  Warning: 'setCurrentFollowingCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
249:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
249:31  Warning: 'HTMLDivElement' is not defined.  no-undef
251:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
258:7  Warning: 'console' is not defined.  no-undef
266:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/index.tsx
57:28  Warning: 'HTMLDivElement' is not defined.  no-undef
100:31  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/servicesCard.tsx
41:11  Warning: 'console' is not defined.  no-undef
46:9  Warning: 'console' is not defined.  no-undef

./src/screens/orders/index.tsx
2:3  Warning: 'Sheet' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:3  Warning: 'SheetContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:3  Warning: 'SheetDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'SheetHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'SheetTitle' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:23  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:10  Warning: 'isSheetOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
75:22  Warning: 'React' is not defined.  no-undef

./src/screens/orders/orderCardDetail.tsx
2:23  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'closeEvent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
56:22  Warning: 'React' is not defined.  no-undef

./src/screens/orders/orderDetail/activeLog.tsx
22:26  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/orders/orderDetail/detail.tsx
57:46  Warning: 'indexs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/orders/orderDetail/orderCardDetail.tsx
7:23  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
67:24  Warning: 'React' is not defined.  no-undef

./src/screens/profile/index.tsx
4:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:27  Warning: 'HTMLDivElement' is not defined.  no-undef
27:7  Warning: 'localStorage' is not defined.  no-undef
42:11  Warning: 'console' is not defined.  no-undef
48:11  Warning: 'console' is not defined.  no-undef

./src/screens/profile/lensProfile/index.tsx
6:10  Warning: 'useAccountQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'getId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'useSearchParams' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:10  Warning: 'isRefatch' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:11  Warning: 'address' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:11  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
37:9  Warning: 'userId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:20  Warning: 'localStorage' is not defined.  no-undef
60:16  Warning: 'isLoadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:12  Warning: 'profileQueryError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
118:22  Warning: 'React' is not defined.  no-undef
134:17  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/aboutMe/index.tsx
4:3  Warning: 'AlertDialog' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'AlertDialogContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'AlertDialogDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:3  Warning: 'AlertDialogHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:3  Warning: 'AlertDialogTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:42  Warning: 'Tooltip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:10  Warning: 'isToggle' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:20  Warning: 'setIsToggle' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:27  Warning: 'e' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
52:23  Warning: 'window' is not defined.  no-undef
110:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/hashtags/index.tsx
2:10  Warning: 'Edit2' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:10  Warning: 'Textarea' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:7  Warning: 'data' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:10  Warning: 'profileName' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:23  Warning: 'setProfileName' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:10  Warning: 'location' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:20  Warning: 'setLocation' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:26  Warning: 'React' is not defined.  no-undef
32:46  Warning: 'HTMLInputElement' is not defined.  no-undef
76:49  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/index.tsx
40:10  Warning: 'Progress' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:10  Warning: 'ref' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:15  Warning: 'uploadBytes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:28  Warning: 'getDownloadURL' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:8  Warning: 'useLensUser' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:10  Warning: 'Web3Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
51:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
51:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
53:10  Warning: 'initFirebase' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
59:10  Warning: 'Chip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
110:10  Warning: 'isbothID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
112:20  Warning: 'isAuthW3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
115:10  Warning: 'SelectedCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
154:11  Warning: 'setTimeout' is not defined.  no-undef
167:11  Warning: 'console' is not defined.  no-undef
171:9  Warning: 'console' is not defined.  no-undef
185:11  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
199:10  Warning: 'progress' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
202:19  Warning: 'setTimeout' is not defined.  no-undef
203:18  Warning: 'clearTimeout' is not defined.  no-undef
212:23  Warning: 'setProfileName' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
213:9  Warning: 'addHashtag' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
213:46  Warning: 'HTMLInputElement' is not defined.  no-undef
221:9  Warning: 'removeHashtag' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
225:9  Warning: 'handleSubmit' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
235:14  Warning: Empty block statement.  no-empty
242:43  Warning: 'File' is not defined.  no-undef
247:55  Warning: 'HTMLInputElement' is not defined.  no-undef
251:7  Warning: 'console' is not defined.  no-undef
260:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
260:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
262:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
268:16  Warning: 'isLoadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
269:12  Warning: 'profileQueryError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
286:10  Warning: 'lensId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
306:7  Warning: 'console' is not defined.  no-undef
322:5  Warning: 'navigator' is not defined.  no-undef
328:9  Warning: 'console' is not defined.  no-undef
333:9  Warning: 'toggleIcon' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
367:5  Warning: 'console' is not defined.  no-undef
425:26  Warning: 'setInterval' is not defined.  no-undef
427:11  Warning: 'clearInterval' is not defined.  no-undef
439:11  Warning: 'setTimeout' is not defined.  no-undef
445:11  Warning: 'clearInterval' is not defined.  no-undef
452:11  Warning: 'clearInterval' is not defined.  no-undef
459:20  Warning: 'clearInterval' is not defined.  no-undef
470:26  Warning: 'setInterval' is not defined.  no-undef
472:11  Warning: 'clearInterval' is not defined.  no-undef
484:11  Warning: 'setTimeout' is not defined.  no-undef
490:11  Warning: 'clearInterval' is not defined.  no-undef
497:11  Warning: 'clearInterval' is not defined.  no-undef
504:20  Warning: 'clearInterval' is not defined.  no-undef
515:26  Warning: 'setInterval' is not defined.  no-undef
517:11  Warning: 'clearInterval' is not defined.  no-undef
529:11  Warning: 'setTimeout' is not defined.  no-undef
535:11  Warning: 'clearInterval' is not defined.  no-undef
542:11  Warning: 'clearInterval' is not defined.  no-undef
549:20  Warning: 'clearInterval' is not defined.  no-undef
576:24  Warning: 'window' is not defined.  no-undef
582:5  Warning: 'window' is not defined.  no-undef
586:7  Warning: 'window' is not defined.  no-undef
624:32  Warning: 'window' is not defined.  no-undef
885:37  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1037:29  Warning: 'e' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1039:25  Warning: 'window' is not defined.  no-undef
1143:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1246:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1364:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1405:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1437:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1450:25  Error: Unexpected constant condition.  no-constant-condition
1495:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1532:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1757:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1823:52  Warning: 'Blob' is not defined.  no-undef
1826:36  Warning: 'URL' is not defined.  no-undef
1895:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1909:48  Warning: 'Blob' is not defined.  no-undef
1912:32  Warning: 'URL' is not defined.  no-undef
1973:13  Warning: 'console' is not defined.  no-undef
1984:21  Warning: 'sessionStorage' is not defined.  no-undef
1985:21  Warning: 'sessionStorage' is not defined.  no-undef
2001:13  Warning: 'console' is not defined.  no-undef
2036:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/personalMotto/index.tsx
2:10  Warning: 'Edit2' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:9  Warning: 'profile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/socialMedia/index.tsx
3:3  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:3  Warning: 'MoreHorizontal' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'Edit2' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:10  Warning: 'Textarea' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
142:23  Error: Unexpected constant condition.  no-constant-condition

./src/screens/profile/lensProfile/tabs/events/calender/index.tsx
8:11  Warning: 'CustomCalendarProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'activeDate' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
21:22  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:29  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
178:31  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/events/calenderEvents/index.tsx
2:17  Warning: 'useState' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/events/createEvent.tsx
2:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:10  Warning: 'FilePlus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:20  Warning: 'Plus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:26  Warning: 'Trash' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'FileUploader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:27  Warning: 'HTMLDivElement' is not defined.  no-undef
16:9  Warning: 'Category' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:34  Warning: 'React' is not defined.  no-undef
53:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
53:21  Warning: Empty block statement.  no-empty
83:32  Warning: 'React' is not defined.  no-undef
83:50  Warning: 'HTMLInputElement' is not defined.  no-undef
88:32  Warning: 'React' is not defined.  no-undef
88:50  Warning: 'HTMLInputElement' is not defined.  no-undef
93:9  Warning: 'handleLog' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:7  Warning: 'console' is not defined.  no-undef
97:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/events/editEvents/index.tsx
2:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:27  Warning: 'HTMLDivElement' is not defined.  no-undef
28:10  Warning: 'eventData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
83:19  Warning: 'console' is not defined.  no-undef
104:19  Warning: 'console' is not defined.  no-undef
107:17  Warning: 'console' is not defined.  no-undef
111:13  Warning: 'console' is not defined.  no-undef
114:11  Warning: 'console' is not defined.  no-undef
133:34  Warning: 'React' is not defined.  no-undef
150:11  Warning: 'console' is not defined.  no-undef
153:11  Warning: 'console' is not defined.  no-undef
164:13  Warning: 'console' is not defined.  no-undef
168:11  Warning: 'console' is not defined.  no-undef
172:7  Warning: 'console' is not defined.  no-undef
199:32  Warning: 'React' is not defined.  no-undef
199:50  Warning: 'HTMLInputElement' is not defined.  no-undef
204:32  Warning: 'React' is not defined.  no-undef
204:50  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/events/eventsCard/index.tsx
28:16  Warning: 'NodeJS' is not defined.  no-undef
32:15  Warning: 'setTimeout' is not defined.  no-undef
38:18  Warning: 'clearTimeout' is not defined.  no-undef
50:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/events/index.tsx
2:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/index.tsx
12:10  Warning: 'Plus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:3  Warning: 'setSelectedProfileTabs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
53:27  Warning: 'HTMLDivElement' is not defined.  no-undef
56:10  Warning: 'isbothID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:7  Warning: 'console' is not defined.  no-undef
117:22  Warning: 'React' is not defined.  no-undef
188:33  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
259:16  Error: Unexpected constant condition.  no-constant-condition
326:23  Error: Unexpected constant condition.  no-constant-condition

./src/screens/profile/lensProfile/tabs/otherServices/index.tsx
14:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/editCustomizations.tsx
8:10  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:22  Warning: 'Info' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:18  Warning: 'setIsTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/index.tsx
34:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
105:18  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/ViewService.tsx
32:21  Warning: 'process' is not defined.  no-undef
49:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/otherServices/servicesCard/index.tsx
3:8  Warning: 'useProfile' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:8  Warning: 'useAuth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/posts/createPost.tsx
2:8  Warning: 'useAuth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:38  Warning: 'File' is not defined.  no-undef
13:9  Warning: 'handleFileChange' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:5  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
14:12  Warning: 'React' is not defined.  no-undef
14:30  Warning: 'HTMLInputElement' is not defined.  no-undef
18:7  Warning: 'console' is not defined.  no-undef
26:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:9  Warning: 'console' is not defined.  no-undef
37:37  Warning: 'React' is not defined.  no-undef
37:55  Warning: 'HTMLInputElement' is not defined.  no-undef
138:28  Warning: 'URL' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/posts/index.tsx
1:31  Warning: 'useRef' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'ChevronLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:9  Warning: 'queryClient' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:10  Warning: 'profileData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
77:10  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:10  Warning: 'showMoreOptions' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:27  Warning: 'setShowMoreOptions' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
106:10  Warning: 'isResonPost' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
111:12  Warning: 'profileQueryError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
171:12  Error: Unsafe usage of optional chaining. If it short-circuits with 'undefined' the evaluation will throw TypeError.  no-unsafe-optional-chaining
177:7  Warning: 'setTimeout' is not defined.  no-undef
213:21  Warning: 'setTimeout' is not defined.  no-undef
217:20  Warning: 'clearTimeout' is not defined.  no-undef
238:5  Warning: 'console' is not defined.  no-undef
244:24  Warning: 'setInterval' is not defined.  no-undef
246:9  Warning: 'alert' is not defined.  no-undef
247:9  Warning: 'clearInterval' is not defined.  no-undef
262:9  Warning: 'clearInterval' is not defined.  no-undef
269:18  Warning: 'clearInterval' is not defined.  no-undef
281:28  Warning: 'React' is not defined.  no-undef
281:42  Warning: 'HTMLDivElement' is not defined.  no-undef
293:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
325:7  Warning: 'sessionStorage' is not defined.  no-undef
326:7  Warning: 'sessionStorage' is not defined.  no-undef
332:7  Warning: 'console' is not defined.  no-undef
343:7  Warning: 'alert' is not defined.  no-undef
366:9  Warning: 'console' is not defined.  no-undef
371:15  Warning: 'resplike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
384:15  Warning: 'respdislike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
400:7  Warning: 'alert' is not defined.  no-undef
415:9  Warning: 'console' is not defined.  no-undef
420:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
423:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
431:7  Warning: 'alert' is not defined.  no-undef
571:5  Warning: 'navigator' is not defined.  no-undef
574:9  Warning: 'alert' is not defined.  no-undef
577:9  Warning: 'console' is not defined.  no-undef
581:26  Warning: React Hook "useDeletePost" is called conditionally. React Hooks must be called in the exact same order in every component render.  react-hooks/rules-of-hooks
694:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1057:31  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1061:25  Warning: 'console' is not defined.  no-undef
1093:21  Warning: 'sessionStorage' is not defined.  no-undef
1094:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/profile/followers/index.tsx
35:11  Warning: 'console' is not defined.  no-undef
45:7  Warning: 'console' is not defined.  no-undef
96:42  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/profile/following/index.tsx
2:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:8  Warning: 'useAuth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'FollowersQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
41:11  Warning: 'console' is not defined.  no-undef
51:7  Warning: 'console' is not defined.  no-undef
54:31  Warning: 'HTMLDivElement' is not defined.  no-undef
97:7  Warning: 'setTimeout' is not defined.  no-undef
111:42  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/profile/index.tsx
20:3  Warning: 'isOtherProfileStatus' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
34:5  Warning: 'setTimeout' is not defined.  no-undef
38:7  Warning: 'setTimeout' is not defined.  no-undef
55:19  Warning: 'setTimeout' is not defined.  no-undef
60:7  Warning: 'clearTimeout' is not defined.  no-undef
79:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/services/createService.tsx
9:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:10  Warning: 'hashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:20  Warning: 'setHashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:10  Warning: 'geoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:19  Warning: 'setGeoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:10  Warning: 'media' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:17  Warning: 'setMedia' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:38  Warning: 'File' is not defined.  no-undef
23:34  Warning: 'React' is not defined.  no-undef
52:11  Warning: 'console' is not defined.  no-undef
57:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
67:11  Error: Unexpected constant condition.  no-constant-condition

./src/screens/profile/lensProfile/tabs/services/index.tsx
14:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/serviceDetails/editCustomizations.tsx
8:23  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:35  Warning: 'Info' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:18  Warning: 'setIsTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/serviceDetails/editService.tsx
8:23  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
177:52  Warning: 'indexs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/services/serviceDetails/index.tsx
9:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
81:50  Warning: 'indexs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/servicesCard/index.tsx
33:9  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/index.tsx
10:16  Warning: 'X' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:9  Warning: 'console' is not defined.  no-undef
48:7  Warning: 'console' is not defined.  no-undef
131:21  Warning: 'alert' is not defined.  no-undef
136:22  Warning: 'React' is not defined.  no-undef
154:17  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/aboutMe/index.tsx
3:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:11  Warning: 'setTimeout' is not defined.  no-undef
50:11  Warning: 'setTimeout' is not defined.  no-undef
54:11  Warning: 'console' is not defined.  no-undef
58:9  Warning: 'console' is not defined.  no-undef
72:29  Warning: 'e' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
74:25  Warning: 'window' is not defined.  no-undef
136:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/connectedAccount/index.tsx
11:10  Warning: 'isVerified' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:9  Warning: 'profile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/hashtags/index.tsx
3:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:7  Warning: 'data' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:26  Warning: 'React' is not defined.  no-undef
30:46  Warning: 'HTMLInputElement' is not defined.  no-undef
58:11  Warning: 'setTimeout' is not defined.  no-undef
71:11  Warning: 'setTimeout' is not defined.  no-undef
75:11  Warning: 'console' is not defined.  no-undef
79:9  Warning: 'console' is not defined.  no-undef
136:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/index.tsx
8:3  Warning: 'Search' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'AlertDialog' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'AlertDialogContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'AlertDialogDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:3  Warning: 'AlertDialogHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:3  Warning: 'AlertDialogTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:3  Warning: 'Chip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:3  Warning: 'ModalFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:3  Warning: 'ModalHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:3  Warning: 'Tooltip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:3  Warning: 'useDisclosure' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
64:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
73:21  Warning: 'process' is not defined.  no-undef
122:10  Warning: 'email' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
122:17  Warning: 'setEmail' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
137:10  Warning: 'isDeleteProcessing' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
137:30  Warning: 'setIsDeleteProcessing' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
138:10  Warning: 'deleteSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
138:25  Warning: 'setDeleteSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
141:10  Warning: 'SelectedCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
168:14  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
182:24  Warning: 'window' is not defined.  no-undef
188:5  Warning: 'window' is not defined.  no-undef
192:7  Warning: 'window' is not defined.  no-undef
216:7  Warning: 'alert' is not defined.  no-undef
229:5  Warning: 'google' is not defined.  no-undef
232:38  Warning: 'google' is not defined.  no-undef
239:46  Warning: 'HTMLInputElement' is not defined.  no-undef
249:59  Warning: 'HTMLInputElement' is not defined.  no-undef
296:11  Warning: 'setTimeout' is not defined.  no-undef
308:11  Warning: 'console' is not defined.  no-undef
312:9  Warning: 'console' is not defined.  no-undef
339:11  Warning: 'setTimeout' is not defined.  no-undef
352:11  Warning: 'console' is not defined.  no-undef
356:9  Warning: 'console' is not defined.  no-undef
364:43  Warning: 'File' is not defined.  no-undef
388:11  Warning: 'setTimeout' is not defined.  no-undef
400:11  Warning: 'console' is not defined.  no-undef
405:7  Warning: 'console' is not defined.  no-undef
410:55  Warning: 'HTMLInputElement' is not defined.  no-undef
422:10  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
435:7  Warning: 'console' is not defined.  no-undef
447:7  Warning: 'console' is not defined.  no-undef
454:38  Warning: 'userId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
468:7  Warning: 'console' is not defined.  no-undef
501:16  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
535:10  Warning: 'value' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
553:11  Warning: 'setTimeout' is not defined.  no-undef
568:13  Warning: 'window' is not defined.  no-undef
571:11  Warning: 'console' is not defined.  no-undef
575:9  Warning: 'console' is not defined.  no-undef
590:7  Warning: 'alert' is not defined.  no-undef
605:19  Warning: 'setTimeout' is not defined.  no-undef
606:18  Warning: 'clearTimeout' is not defined.  no-undef
641:22  Warning: 'setInterval' is not defined.  no-undef
645:18  Warning: 'clearInterval' is not defined.  no-undef
662:7  Warning: 'console' is not defined.  no-undef
674:5  Warning: 'navigator' is not defined.  no-undef
678:9  Warning: 'setTimeout' is not defined.  no-undef
683:9  Warning: 'console' is not defined.  no-undef
688:9  Warning: 'toggleIcon' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
695:22  Warning: 'process' is not defined.  no-undef
697:9  Warning: 'console' is not defined.  no-undef
701:22  Warning: 'document' is not defined.  no-undef
707:43  Warning: 'google' is not defined.  no-undef
709:7  Warning: 'document' is not defined.  no-undef
714:59  Warning: 'HTMLInputElement' is not defined.  no-undef
732:26  Warning: 'google' is not defined.  no-undef
745:31  Warning: 'google' is not defined.  no-undef
756:61  Warning: 'process' is not defined.  no-undef
767:35  Warning: 'window' is not defined.  no-undef
1156:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1549:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1592:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1624:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1632:35  Error: Unexpected constant condition.  no-constant-condition
1673:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1822:33  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1952:33  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
2087:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
2165:32  Warning: 'URL' is not defined.  no-undef
2217:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
2234:32  Warning: 'URL' is not defined.  no-undef
2336:23  Warning: 'sessionStorage' is not defined.  no-undef
2337:23  Warning: 'sessionStorage' is not defined.  no-undef
2442:31  Warning: 'navigator' is not defined.  no-undef
2444:31  Warning: 'setTimeout' is not defined.  no-undef
2446:31  Warning: 'window' is not defined.  no-undef

./src/screens/profile/myProfile/profileInfo/personalMotto/index.tsx
3:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:9  Warning: 'profile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
40:11  Warning: 'setTimeout' is not defined.  no-undef
53:11  Warning: 'setTimeout' is not defined.  no-undef
57:11  Warning: 'console' is not defined.  no-undef
61:9  Warning: 'console' is not defined.  no-undef
106:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/socialMedia/index.tsx
4:3  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'MoreHorizontal' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'Divider' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'ModalHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:33  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
74:11  Warning: 'setTimeout' is not defined.  no-undef
91:11  Warning: 'setTimeout' is not defined.  no-undef
95:11  Warning: 'console' is not defined.  no-undef
99:9  Warning: 'console' is not defined.  no-undef
105:56  Warning: 'React' is not defined.  no-undef
113:7  Warning: 'window' is not defined.  no-undef
256:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
444:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/verifyEmail/index.tsx
18:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:10  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
54:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/calender/index.tsx
8:11  Warning: 'CustomCalendarProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'activeDate' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
21:22  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:29  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
178:31  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/events/calenderEvents/index.tsx
2:17  Warning: 'useState' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/events/createEvent.tsx
2:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:17  Warning: 'FilePlus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:35  Warning: 'Plus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:41  Warning: 'Trash' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'FileUploader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:27  Warning: 'HTMLDivElement' is not defined.  no-undef
17:9  Warning: 'Category' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
55:21  Warning: 'setTimeout' is not defined.  no-undef
60:20  Warning: 'clearTimeout' is not defined.  no-undef
167:9  Warning: 'console' is not defined.  no-undef
172:7  Warning: 'console' is not defined.  no-undef
205:32  Warning: 'React' is not defined.  no-undef
205:50  Warning: 'HTMLInputElement' is not defined.  no-undef
223:32  Warning: 'React' is not defined.  no-undef
223:50  Warning: 'HTMLInputElement' is not defined.  no-undef
452:45  Warning: 'document' is not defined.  no-undef
454:32  Warning: 'HTMLInputElement' is not defined.  no-undef
520:45  Warning: 'document' is not defined.  no-undef
522:32  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/editEvents/index.tsx
12:24  Warning: 'EventType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:27  Warning: 'HTMLDivElement' is not defined.  no-undef
34:10  Warning: 'eventData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
111:17  Warning: 'console' is not defined.  no-undef
129:17  Warning: 'console' is not defined.  no-undef
132:15  Warning: 'console' is not defined.  no-undef
136:11  Warning: 'console' is not defined.  no-undef
169:13  Warning: 'console' is not defined.  no-undef
172:11  Warning: 'console' is not defined.  no-undef
198:21  Warning: 'setTimeout' is not defined.  no-undef
203:20  Warning: 'clearTimeout' is not defined.  no-undef
207:34  Warning: 'React' is not defined.  no-undef
241:11  Warning: 'console' is not defined.  no-undef
245:11  Warning: 'console' is not defined.  no-undef
258:13  Warning: 'console' is not defined.  no-undef
263:11  Warning: 'console' is not defined.  no-undef
269:7  Warning: 'console' is not defined.  no-undef
357:9  Warning: 'handleInputChange' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
358:8  Warning: 'React' is not defined.  no-undef
358:26  Warning: 'HTMLInputElement' is not defined.  no-undef
358:45  Warning: 'HTMLTextAreaElement' is not defined.  no-undef
382:32  Warning: 'React' is not defined.  no-undef
382:50  Warning: 'HTMLInputElement' is not defined.  no-undef
400:32  Warning: 'React' is not defined.  no-undef
400:50  Warning: 'HTMLInputElement' is not defined.  no-undef
505:24  Warning: 'React' is not defined.  no-undef
615:45  Warning: 'document' is not defined.  no-undef
617:32  Warning: 'HTMLInputElement' is not defined.  no-undef
683:45  Warning: 'document' is not defined.  no-undef
685:32  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/eventsCard/index.tsx
31:16  Warning: 'NodeJS' is not defined.  no-undef
35:15  Warning: 'setTimeout' is not defined.  no-undef
41:18  Warning: 'clearTimeout' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/index.tsx
2:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/index.tsx
33:3  Warning: 'setToggleMain' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
41:27  Warning: 'HTMLDivElement' is not defined.  no-undef
54:10  Warning: 'activeTabs' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
79:22  Warning: 'React' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/otherServices/index.tsx
14:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/otherServices/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/otherServices/serviceDetails/index.tsx
8:3  Warning: 'AlertDialog' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:3  Warning: 'AlertDialogContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'AlertDialogDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'AlertDialogHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'AlertDialogTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
106:18  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
347:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
392:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/otherServices/serviceDetails/ViewService.tsx
32:21  Warning: 'process' is not defined.  no-undef
49:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/otherServices/servicesCard/index.tsx
5:10  Warning: 'getServiceById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'activeColor' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
18:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/posts/createPost.tsx
5:10  Warning: 'useRef' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:10  Warning: 'createUser' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'getStorage' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:38  Warning: 'File' is not defined.  no-undef
30:21  Warning: 'setTimeout' is not defined.  no-undef
36:20  Warning: 'clearTimeout' is not defined.  no-undef
47:7  Warning: 'alert' is not defined.  no-undef
87:9  Warning: 'console' is not defined.  no-undef
92:7  Warning: 'console' is not defined.  no-undef
96:37  Warning: 'React' is not defined.  no-undef
96:55  Warning: 'HTMLInputElement' is not defined.  no-undef
103:26  Warning: 'React' is not defined.  no-undef
103:46  Warning: 'HTMLInputElement' is not defined.  no-undef
115:25  Warning: 'React' is not defined.  no-undef
115:45  Warning: 'HTMLInputElement' is not defined.  no-undef
302:30  Warning: 'URL' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/posts/imageGrid.tsx
2:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:10  Warning: 'category' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:21  Warning: 'process' is not defined.  no-undef
21:5  Warning: 'console' is not defined.  no-undef
21:17  Warning: 'process' is not defined.  no-undef
58:9  Warning: 'console' is not defined.  no-undef
82:9  Warning: 'console' is not defined.  no-undef
97:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/posts/index.tsx
67:11  Warning: 'setTimeout' is not defined.  no-undef
72:11  Warning: 'setTimeout' is not defined.  no-undef
80:9  Warning: 'console' is not defined.  no-undef
82:9  Warning: 'setTimeout' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/profile/followers/index.tsx
32:21  Warning: 'process' is not defined.  no-undef
51:7  Warning: 'setTimeout' is not defined.  no-undef
55:7  Warning: 'console' is not defined.  no-undef
96:7  Warning: 'console' is not defined.  no-undef
111:7  Warning: 'console' is not defined.  no-undef
131:19  Warning: 'setTimeout' is not defined.  no-undef
159:7  Warning: 'clearTimeout' is not defined.  no-undef
208:7  Warning: 'console' is not defined.  no-undef
370:13  Warning: 'console' is not defined.  no-undef
385:21  Warning: 'sessionStorage' is not defined.  no-undef
386:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/profile/following/index.tsx
32:21  Warning: 'process' is not defined.  no-undef
48:7  Warning: 'setTimeout' is not defined.  no-undef
52:7  Warning: 'console' is not defined.  no-undef
93:7  Warning: 'console' is not defined.  no-undef
107:7  Warning: 'console' is not defined.  no-undef
127:19  Warning: 'setTimeout' is not defined.  no-undef
155:7  Warning: 'clearTimeout' is not defined.  no-undef
368:13  Warning: 'console' is not defined.  no-undef
383:21  Warning: 'sessionStorage' is not defined.  no-undef
384:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/profile/index.tsx
1:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'setSelectedProfileTabs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
36:7  Warning: 'console' is not defined.  no-undef
47:7  Warning: 'console' is not defined.  no-undef
69:7  Warning: 'console' is not defined.  no-undef
90:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/createService.tsx
13:10  Warning: 'FormEvent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:10  Warning: 'listPrice' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
33:10  Warning: 'hashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
33:20  Warning: 'setHashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:10  Warning: 'geoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:19  Warning: 'setGeoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:10  Warning: 'media' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:17  Warning: 'setMedia' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:38  Warning: 'File' is not defined.  no-undef
49:21  Warning: 'setTimeout' is not defined.  no-undef
54:20  Warning: 'clearTimeout' is not defined.  no-undef
89:9  Warning: 'console' is not defined.  no-undef
94:7  Warning: 'console' is not defined.  no-undef
185:7  Warning: 'console' is not defined.  no-undef
187:7  Warning: 'alert' is not defined.  no-undef
219:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/index.tsx
16:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/services/serviceDetails/editCustomizations.tsx
69:3  Warning: 'console' is not defined.  no-undef
81:21  Warning: 'setTimeout' is not defined.  no-undef
85:20  Warning: 'clearTimeout' is not defined.  no-undef
94:9  Warning: 'console' is not defined.  no-undef
143:13  Warning: 'console' is not defined.  no-undef
165:13  Warning: 'console' is not defined.  no-undef
176:9  Warning: 'console' is not defined.  no-undef
237:7  Warning: 'alert' is not defined.  no-undef
255:9  Warning: 'alert' is not defined.  no-undef
259:7  Warning: 'console' is not defined.  no-undef
261:7  Warning: 'alert' is not defined.  no-undef
316:5  Warning: 'console' is not defined.  no-undef
332:9  Warning: 'alert' is not defined.  no-undef
335:7  Warning: 'console' is not defined.  no-undef
337:7  Warning: 'alert' is not defined.  no-undef
384:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/serviceDetails/editService.tsx
38:10  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
66:30  Warning: 'setTimeout' is not defined.  no-undef
107:9  Warning: 'console' is not defined.  no-undef
110:9  Warning: 'clearTimeout' is not defined.  no-undef
121:8  Warning: 'React' is not defined.  no-undef
121:26  Warning: 'HTMLInputElement' is not defined.  no-undef
121:45  Warning: 'HTMLTextAreaElement' is not defined.  no-undef
134:21  Warning: 'setTimeout' is not defined.  no-undef
138:20  Warning: 'clearTimeout' is not defined.  no-undef
158:9  Warning: 'console' is not defined.  no-undef
163:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/serviceDetails/index.tsx
28:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
34:5  Warning: 'setTimeout' is not defined.  no-undef
57:7  Warning: 'console' is not defined.  no-undef
72:9  Warning: 'formatDurationToDays' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
106:9  Warning: 'alert' is not defined.  no-undef
109:7  Warning: 'alert' is not defined.  no-undef
110:7  Warning: 'console' is not defined.  no-undef
114:9  Warning: 'formatDescription' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/services/servicesCard/index.tsx
1:31  Warning: 'JSX' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:36  Warning: 'SVGProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:10  Warning: 'services' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
143:7  Warning: 'console' is not defined.  no-undef
175:9  Warning: 'console' is not defined.  no-undef
208:11  Warning: 'setTimeout' is not defined.  no-undef
213:11  Warning: 'console' is not defined.  no-undef
217:9  Warning: 'console' is not defined.  no-undef
221:9  Warning: 'setTimeout' is not defined.  no-undef

./src/screens/selectedCategory/events/eventsCardSC.tsx
33:7  Warning: 'console' is not defined.  no-undef
46:16  Warning: 'NodeJS' is not defined.  no-undef
50:15  Warning: 'setTimeout' is not defined.  no-undef
56:18  Warning: 'clearTimeout' is not defined.  no-undef

./src/screens/selectedCategory/events/index.tsx
7:28  Warning: 'HTMLDivElement' is not defined.  no-undef
9:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:27  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/selectedCategory/index.tsx
3:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'ChevronDown' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:23  Warning: 'ChevronsDown' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:10  Warning: 'Item' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
60:27  Warning: 'HTMLDivElement' is not defined.  no-undef
63:9  Warning: 'handleScroll' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
70:5  Warning: 'console' is not defined.  no-undef
73:5  Warning: 'console' is not defined.  no-undef
75:38  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
82:9  Warning: 'console' is not defined.  no-undef
84:9  Warning: 'console' is not defined.  no-undef
114:20  Warning: 'React' is not defined.  no-undef

./src/screens/selectedCategory/posts/imageCard.tsx
7:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:21  Warning: 'process' is not defined.  no-undef
63:9  Warning: 'console' is not defined.  no-undef

./src/screens/selectedCategory/posts/imageCardMobile.tsx
13:21  Warning: 'process' is not defined.  no-undef
38:7  Warning: 'console' is not defined.  no-undef

./src/screens/selectedCategory/posts/index.tsx
8:28  Warning: 'HTMLDivElement' is not defined.  no-undef
27:21  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/posts/postsCard.tsx
6:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'PublicationMetadataMainFocusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'PublicationType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:3  Warning: 'usePublicationsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'getLensProfileDetails' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:10  Warning: 'visiblePosts' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:24  Warning: 'setVisiblePosts' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:10  Warning: 'loadingMore' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:23  Warning: 'setLoadingMore' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:10  Warning: 'allPostsLoaded' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:26  Warning: 'setAllPostsLoaded' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
81:20  Warning: 'postCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
132:7  Warning: 'console' is not defined.  no-undef
151:7  Warning: 'console' is not defined.  no-undef
165:7  Warning: 'console' is not defined.  no-undef
181:9  Warning: 'id' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
200:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
201:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
216:16  Warning: 'isLoadingPublications' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
218:12  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
302:9  Warning: 'loadNextPage' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/profiles/index.tsx
8:28  Warning: 'HTMLDivElement' is not defined.  no-undef
10:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:27  Warning: 'HTMLDivElement' is not defined.  no-undef
27:17  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/profiles/profilesCardSC.tsx
1:10  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:23  Warning: 'getUserById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:9  Warning: 'isAuthLogin' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:10  Warning: 'followingList' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:7  Warning: 'console' is not defined.  no-undef
156:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
205:34  Warning: 'setCurrentFollowingCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:31  Warning: 'HTMLDivElement' is not defined.  no-undef
211:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
218:7  Warning: 'console' is not defined.  no-undef
225:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
277:15  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/services/index.tsx
7:28  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/selectedCategory/services/servicesCard.tsx
47:9  Warning: 'console' is not defined.  no-undef

./src/services/authBridgeService.ts
10:3  Warning: 'deleteDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
77:5  Warning: 'console' is not defined.  no-undef
96:5  Warning: 'console' is not defined.  no-undef
114:5  Warning: 'console' is not defined.  no-undef
137:7  Warning: 'console' is not defined.  no-undef
147:7  Error: Unreachable code.  no-unreachable
147:7  Warning: 'console' is not defined.  no-undef
150:5  Warning: 'console' is not defined.  no-undef
172:7  Warning: 'console' is not defined.  no-undef
184:5  Warning: 'console' is not defined.  no-undef
202:5  Warning: 'console' is not defined.  no-undef
234:5  Warning: 'console' is not defined.  no-undef
280:9  Warning: 'console' is not defined.  no-undef
285:5  Warning: 'console' is not defined.  no-undef
288:5  Warning: 'console' is not defined.  no-undef
306:5  Warning: 'console' is not defined.  no-undef
310:7  Warning: 'console' is not defined.  no-undef
324:7  Warning: 'console' is not defined.  no-undef
332:7  Warning: 'console' is not defined.  no-undef
341:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
353:5  Warning: 'console' is not defined.  no-undef
403:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
421:7  Warning: 'console' is not defined.  no-undef
428:7  Warning: 'console' is not defined.  no-undef
437:7  Warning: 'console' is not defined.  no-undef
443:5  Warning: 'console' is not defined.  no-undef
452:16  Warning: 'localStorage' is not defined.  no-undef
452:32  Warning: 'window' is not defined.  no-undef
462:5  Warning: 'console' is not defined.  no-undef
480:5  Warning: 'console' is not defined.  no-undef
488:16  Warning: 'localStorage' is not defined.  no-undef
488:32  Warning: 'window' is not defined.  no-undef
490:5  Warning: 'console' is not defined.  no-undef
494:5  Warning: 'console' is not defined.  no-undef
503:5  Warning: 'console' is not defined.  no-undef
536:5  Warning: 'console' is not defined.  no-undef

./src/services/chatService.ts
44:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
62:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
84:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
102:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
117:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/commentServices.ts
74:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
82:5  Warning: 'userId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
112:7  Warning: 'console' is not defined.  no-undef
137:7  Warning: 'console' is not defined.  no-undef
214:7  Warning: 'console' is not defined.  no-undef

./src/services/currencyService.ts
38:28  Warning: 'localStorage' is not defined.  no-undef
48:16  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
60:5  Warning: 'localStorage' is not defined.  no-undef
63:5  Warning: 'console' is not defined.  no-undef
77:5  Warning: 'console' is not defined.  no-undef
91:12  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
111:7  Warning: 'localStorage' is not defined.  no-undef
116:5  Warning: 'console' is not defined.  no-undef
150:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/eventsServices.ts
5:3  Warning: 'addDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'getFirestore' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
86:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
112:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
220:5  Warning: 'console' is not defined.  no-undef
251:5  Warning: 'console' is not defined.  no-undef
325:5  Warning: 'console' is not defined.  no-undef
399:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
437:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/followServices.ts
47:9  Warning: 'console' is not defined.  no-undef
72:7  Warning: 'console' is not defined.  no-undef
80:9  Warning: 'console' is not defined.  no-undef
106:7  Warning: 'console' is not defined.  no-undef
122:7  Warning: 'console' is not defined.  no-undef
131:9  Warning: 'console' is not defined.  no-undef
148:7  Warning: 'console' is not defined.  no-undef
158:9  Warning: 'console' is not defined.  no-undef
197:7  Warning: 'console' is not defined.  no-undef

./src/services/lensService.ts
4:3  Warning: 'doc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'getDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:7  Warning: 'data' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:7  Warning: 'insertLensProfiles' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
78:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
108:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
122:7  Warning: 'console' is not defined.  no-undef
151:5  Warning: 'console' is not defined.  no-undef

./src/services/ordersServices.ts
65:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
89:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
115:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
142:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
161:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/postService.ts
7:3  Warning: 'addDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:16  Warning: 'uuidv4' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
66:5  Warning: 'console' is not defined.  no-undef
84:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
153:5  Warning: 'console' is not defined.  no-undef
215:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
238:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
264:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
304:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
346:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
368:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
377:3  Warning: 'console' is not defined.  no-undef
401:3  Warning: 'console' is not defined.  no-undef
450:3  Warning: 'console' is not defined.  no-undef

./src/services/reportServices.ts
100:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
108:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
127:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
135:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/serviceService.ts
14:3  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
110:5  Warning: 'console' is not defined.  no-undef
129:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
154:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
204:5  Warning: 'console' is not defined.  no-undef
249:5  Warning: 'console' is not defined.  no-undef
343:5  Warning: 'console' is not defined.  no-undef
350:9  Warning: 'console' is not defined.  no-undef
358:9  Warning: 'console' is not defined.  no-undef
364:7  Warning: 'console' is not defined.  no-undef
376:9  Warning: 'console' is not defined.  no-undef
388:9  Warning: 'console' is not defined.  no-undef
402:13  Warning: 'console' is not defined.  no-undef
411:13  Warning: 'console' is not defined.  no-undef
418:11  Warning: 'console' is not defined.  no-undef
432:11  Warning: 'console' is not defined.  no-undef
453:7  Warning: 'console' is not defined.  no-undef
463:7  Warning: 'console' is not defined.  no-undef
489:7  Warning: 'console' is not defined.  no-undef
500:5  Warning: 'console' is not defined.  no-undef
521:5  Warning: 'console' is not defined.  no-undef
556:5  Warning: 'console' is not defined.  no-undef
572:5  Warning: 'console' is not defined.  no-undef
693:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
733:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
846:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
907:5  Warning: 'console' is not defined.  no-undef

./src/services/usersServices.ts
9:3  Warning: 'getFirestore' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:11  Warning: 'usersCollection' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
28:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:5  Warning: 'console' is not defined.  no-undef
53:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
71:5  Warning: 'console' is not defined.  no-undef
89:9  Warning: 'localStorage' is not defined.  no-undef
91:9  Warning: 'localStorage' is not defined.  no-undef
104:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
205:5  Warning: 'console' is not defined.  no-undef
217:11  Warning: 'usersCollection' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
235:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
261:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
290:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
300:7  Warning: 'console' is not defined.  no-undef
309:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
343:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
362:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
383:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
402:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
420:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
432:5  Warning: 'console' is not defined.  no-undef
439:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
444:7  Warning: 'POST_COLLECTION' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
445:7  Warning: 'SERVICES_COLLECTION' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
446:7  Warning: 'EVENT_COLLECTION' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
484:5  Warning: 'console' is not defined.  no-undef
513:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
517:5  Warning: 'console' is not defined.  no-undef
521:5  Warning: 'console' is not defined.  no-undef
562:5  Warning: 'console' is not defined.  no-undef
577:5  Warning: 'console' is not defined.  no-undef
611:5  Warning: 'console' is not defined.  no-undef
645:5  Warning: 'console' is not defined.  no-undef
680:5  Warning: 'console' is not defined.  no-undef
707:5  Warning: 'console' is not defined.  no-undef
728:5  Warning: 'console' is not defined.  no-undef
767:5  Warning: 'console' is not defined.  no-undef
780:7  Warning: 'console' is not defined.  no-undef
790:5  Warning: 'console' is not defined.  no-undef
792:5  Warning: 'console' is not defined.  no-undef
813:9  Warning: 'console' is not defined.  no-undef
823:5  Warning: 'console' is not defined.  no-undef
857:5  Warning: 'console' is not defined.  no-undef
952:5  Warning: 'console' is not defined.  no-undef
969:5  Warning: 'console' is not defined.  no-undef
997:5  Warning: 'console' is not defined.  no-undef
1021:5  Warning: 'console' is not defined.  no-undef
1026:7  Warning: 'console' is not defined.  no-undef
1033:5  Warning: 'console' is not defined.  no-undef
1035:5  Warning: 'console' is not defined.  no-undef

./src/types/global.d.ts
10:29  Warning: 'Storage' is not defined.  no-undef
11:31  Warning: 'Storage' is not defined.  no-undef
12:25  Warning: 'Document' is not defined.  no-undef
14:26  Warning: 'Navigator' is not defined.  no-undef
24:18  Warning: 'React' is not defined.  no-undef
24:42  Warning: 'React' is not defined.  no-undef
24:62  Warning: 'SVGElement' is not defined.  no-undef

./src/utils/htmlSanitizer.ts
77:22  Warning: 'DOMParser' is not defined.  no-undef

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/building-your-application/configuring/eslint#disabling-rules


   ▲ Next.js 15.0.3
   - Environments: .env

   Creating an optimized production build ...
 ✓ Compiled successfully
   Linting and checking validity of types ...



Failed to compile.

./src/app/layoout.server.tsx
1:15  Warning: 'Metadata' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/page.tsx
6:10  Warning: 'ConnectKitProvider' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/post-details/page.tsx
3:17  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'AnyPublicationMetadataFieldsFragment' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'CommentFieldsFragment' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:3  Warning: 'LensTransactionStatusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'Maybe' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'MetadataAttribute' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'MetadataAttributeType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:3  Warning: 'NewPublicationStatsDocument' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'PaginatedPublicationsResult' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:3  Warning: 'PaginatedPublicationsTagsResult' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'ProfileWhoReactedResult' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:3  Warning: 'PublicationCommentOn' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'PublicationMetadataLicenseType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'PublicationReactionType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:3  Warning: 'PublicationReportingFraudSubreason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:3  Warning: 'PublicationReportingReason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:3  Warning: 'PublicationReportingSpamSubreason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:3  Warning: 'useAddReactionMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:3  Warning: 'useCreateMomokaCommentTypedDataMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:8  Warning: 'useLogin' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:3  Warning: 'deleteUserDetails' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:3  Warning: 'getUsersByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
52:3  Warning: 'toggleBookMarks' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
54:10  Warning: 'logOut' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
55:10  Warning: 'getId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
57:3  Warning: 'getEventsByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
58:3  Warning: 'getEventsByUserId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:3  Warning: 'getServicesByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:3  Warning: 'getServicesByUserId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
64:10  Warning: 'usePostQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
69:10  Warning: 'searchValue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
69:23  Warning: 'setSearchValue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:29  Warning: 'setCurrentFeedCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
74:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
79:17  Warning: 'transactionData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
79:43  Warning: 'refetchTransactionStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
90:17  Warning: 'feedData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
109:11  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
109:28  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
117:16  Warning: 'commentsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
119:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
139:16  Warning: 'liked_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
141:12  Warning: 'liked_error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
160:9  Warning: 'addReaction' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
162:24  Warning: 'reportPost' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
198:9  Warning: 'sendEmail' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
199:11  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
216:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
235:7  Warning: 'alert' is not defined.  no-undef
241:13  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
258:7  Warning: 'console' is not defined.  no-undef
262:9  Warning: 'deleteUser' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
276:7  Warning: 'console' is not defined.  no-undef
277:7  Warning: 'alert' is not defined.  no-undef
330:19  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
345:19  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/post-details-1/page.tsx
2:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:10  Warning: 'CommentOnMomokaMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:35  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'AccountsBulkQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'AccountsOrderBy' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:3  Warning: 'Follower' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'useAccountsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:3  Warning: 'useFullAccountQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
39:10  Warning: 'CommentManager' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
41:3  Warning: 'deletePost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:3  Warning: 'getAllPostsTest' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:3  Warning: 'getUsersSortedByLastPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:3  Warning: 'createService' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:3  Warning: 'deleteCustomization' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:3  Warning: 'getCustomisations' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:3  Warning: 'getServiceById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
51:3  Warning: 'getServicesByCategory' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
52:3  Warning: 'getServicesByUserId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
56:25  Warning: 'Web3Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
67:11  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
67:28  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
90:39  Warning: 'followersLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
116:40  Warning: 'followingsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
155:16  Warning: 'commentsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
201:16  Warning: 'postsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
203:12  Warning: 'postsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
233:17  Warning: 'profileData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
255:17  Warning: 'profileDataByLensId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
276:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
311:24  Warning: 'setInterval' is not defined.  no-undef
313:9  Warning: 'alert' is not defined.  no-undef
314:9  Warning: 'clearInterval' is not defined.  no-undef
322:9  Warning: 'alert' is not defined.  no-undef
323:9  Warning: 'clearInterval' is not defined.  no-undef
330:18  Warning: 'clearInterval' is not defined.  no-undef
336:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
336:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
348:13  Warning: 'formData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
403:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
415:7  Warning: 'console' is not defined.  no-undef
419:12  Warning: 'files' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
423:9  Warning: 'handleFileChange' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
423:41  Warning: 'React' is not defined.  no-undef
423:59  Warning: 'HTMLInputElement' is not defined.  no-undef
430:7  Warning: 'console' is not defined.  no-undef
440:5  Warning: 'console' is not defined.  no-undef
456:9  Warning: 'image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
460:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
473:5  Warning: 'console' is not defined.  no-undef
517:13  Warning: 'console' is not defined.  no-undef
579:19  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
594:17  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
610:11  Warning: 'console' is not defined.  no-undef
623:11  Warning: 'console' is not defined.  no-undef
651:61  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/profile/page.tsx
14:19  Warning: 'setTimeout' is not defined.  no-undef
21:18  Warning: 'clearTimeout' is not defined.  no-undef

./src/app/profile/[userId]/page.tsx
2:10  Warning: 'Suspense' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:6  Warning: 'CategoryProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/search_temp/page.tsx
1:8  Warning: 'LensPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:10  Warning: 'useSearchProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/temp/page.tsx
1:8  Warning: 'LensPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:8  Warning: 'CalendarComp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:8  Warning: 'CalendarCompEvent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/test/page.tsx
2:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:17  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:8  Warning: 'FeedPost' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:3  Warning: 'FraudSubreason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'ReportingReason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'ReportManager' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:16  Warning: 'searchLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
38:12  Warning: 'searchError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:17  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
63:39  Warning: 'followers_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
72:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/app/testFirebase/page.tsx
43:9  Warning: 'date' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/bottomArrow.tsx
2:10  Warning: 'ChevronsDown' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:14  Warning: 'React' is not defined.  no-undef
5:30  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/CardSkeleton/EventCardSkeleton.tsx
6:3  Warning: 'CardDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/FeedPost.tsx
7:10  Warning: 'ThemeProvider' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'Play' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:6  Warning: 'Props' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:49  Warning: 'data' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/filter.tsx
2:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:3  Warning: 'SheetFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:7  Warning: 'filterData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:10  Warning: 'screen1' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:19  Warning: 'setScreen1' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:10  Warning: 'screen2' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:19  Warning: 'setScreen2' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
44:10  Warning: 'screen3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
44:19  Warning: 'setScreen3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:10  Warning: 'screen4' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:19  Warning: 'setScreen4' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:10  Warning: 'screen5' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:19  Warning: 'setScreen5' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
99:36  Error: This number literal will lose precision at runtime.  no-loss-of-precision

./src/components/GlobalSignInButton.tsx
40:21  Warning: 'setTimeout' is not defined.  no-undef
44:20  Warning: 'clearTimeout' is not defined.  no-undef
49:31  Warning: 'document' is not defined.  no-undef
59:22  Warning: 'setInterval' is not defined.  no-undef
61:18  Warning: 'clearInterval' is not defined.  no-undef
68:36  Warning: 'MouseEvent' is not defined.  no-undef
71:34  Warning: 'HTMLElement' is not defined.  no-undef
83:19  Warning: 'setTimeout' is not defined.  no-undef
84:7  Warning: 'document' is not defined.  no-undef
88:7  Warning: 'clearTimeout' is not defined.  no-undef
89:7  Warning: 'document' is not defined.  no-undef
122:28  Warning: 'HTMLButtonElement' is not defined.  no-undef
183:5  Warning: 'setTimeout' is not defined.  no-undef
188:7  Warning: 'setTimeout' is not defined.  no-undef
199:9  Warning: 'navigator' is not defined.  no-undef
215:21  Warning: 'setTimeout' is not defined.  no-undef
222:20  Warning: 'clearTimeout' is not defined.  no-undef
238:21  Warning: 'setTimeout' is not defined.  no-undef
242:20  Warning: 'clearTimeout' is not defined.  no-undef
250:21  Warning: 'setTimeout' is not defined.  no-undef
254:20  Warning: 'clearTimeout' is not defined.  no-undef
266:30  Warning: 'setTimeout' is not defined.  no-undef
276:13  Warning: 'window' is not defined.  no-undef
280:22  Warning: 'clearTimeout' is not defined.  no-undef
287:25  Warning: 'setTimeout' is not defined.  no-undef
291:13  Warning: 'window' is not defined.  no-undef
294:24  Warning: 'clearTimeout' is not defined.  no-undef
372:41  Warning: 'navigator' is not defined.  no-undef
374:25  Warning: 'window' is not defined.  no-undef
379:25  Warning: 'window' is not defined.  no-undef
383:21  Warning: 'console' is not defined.  no-undef
551:35  Warning: 'navigator' is not defined.  no-undef
553:35  Warning: 'window' is not defined.  no-undef
558:35  Warning: 'window' is not defined.  no-undef
563:31  Warning: 'setTimeout' is not defined.  no-undef
573:29  Warning: 'console' is not defined.  no-undef
734:33  Warning: 'localStorage' is not defined.  no-undef
748:39  Warning: 'window' is not defined.  no-undef
749:40  Warning: 'window' is not defined.  no-undef
751:40  Warning: 'window' is not defined.  no-undef
759:39  Warning: 'navigator' is not defined.  no-undef
762:39  Warning: 'window' is not defined.  no-undef
768:39  Warning: 'window' is not defined.  no-undef
771:37  Warning: 'console' is not defined.  no-undef
784:33  Warning: 'setTimeout' is not defined.  no-undef

./src/components/imageGrid.tsx
10:6  Warning: 'Props' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:37  Warning: 'data' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
26:17  Error: Unexpected constant truthiness on the left-hand side of a `&&` expression.  no-constant-binary-expression

./src/components/leftArrow.tsx
2:10  Warning: 'ChevronsLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:14  Warning: 'React' is not defined.  no-undef
5:30  Warning: 'HTMLDivElement' is not defined.  no-undef
24:5  Warning: 'window' is not defined.  no-undef
31:7  Warning: 'window' is not defined.  no-undef
45:5  Warning: 'console' is not defined.  no-undef
46:10  Warning: 'console' is not defined.  no-undef

./src/components/lensPost.tsx
3:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:25  Warning: 'Web3Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'ImageGrid' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:57  Warning: 'PostQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:6  Warning: 'Props' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:36  Warning: 'userId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
19:9  Warning: 'id' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
170:10  Warning: 'filteredData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
272:15  Error: Unexpected constant condition.  no-constant-condition

./src/components/loadingOverlay.tsx
20:22  Warning: 'setInterval' is not defined.  no-undef
24:18  Warning: 'clearInterval' is not defined.  no-undef

./src/components/modals/SendVerificationModal.tsx
4:25  Warning: 'X' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
28:21  Warning: 'setTimeout' is not defined.  no-undef
29:20  Warning: 'clearTimeout' is not defined.  no-undef
58:7  Warning: 'console' is not defined.  no-undef

./src/components/navbar.tsx
4:38  Warning: 'X' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
41:9  Warning: 'console' is not defined.  no-undef
44:7  Warning: 'alert' is not defined.  no-undef
45:7  Warning: 'console' is not defined.  no-undef

./src/components/Profile.tsx
9:28  Warning: 'profileId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
18:3  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/rightArrow.tsx
2:10  Warning: 'ChevronsRight' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:14  Warning: 'React' is not defined.  no-undef
5:30  Warning: 'HTMLDivElement' is not defined.  no-undef
26:5  Warning: 'window' is not defined.  no-undef
33:7  Warning: 'window' is not defined.  no-undef

./src/components/sidebar.tsx
213:10  Warning: 'selectedChatId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
349:7  Warning: 'indexedDB' is not defined.  no-undef
356:5  Warning: 'localStorage' is not defined.  no-undef
357:5  Warning: 'window' is not defined.  no-undef
358:5  Warning: 'window' is not defined.  no-undef
364:21  Warning: 'process' is not defined.  no-undef

./src/components/SignInButton.tsx
20:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
28:7  Warning: 'navigator' is not defined.  no-undef
36:38  Error: Unexpected empty object pattern.  no-empty-pattern
86:3  Warning: 'console' is not defined.  no-undef
122:21  Warning: 'setTimeout' is not defined.  no-undef
126:20  Warning: 'clearTimeout' is not defined.  no-undef
134:21  Warning: 'setTimeout' is not defined.  no-undef
138:20  Warning: 'clearTimeout' is not defined.  no-undef
222:43  Warning: 'navigator' is not defined.  no-undef
224:27  Warning: 'window' is not defined.  no-undef
229:27  Warning: 'window' is not defined.  no-undef
233:23  Warning: 'console' is not defined.  no-undef
327:37  Warning: 'window' is not defined.  no-undef
328:38  Warning: 'window' is not defined.  no-undef
330:38  Warning: 'window' is not defined.  no-undef
338:37  Warning: 'navigator' is not defined.  no-undef
341:37  Warning: 'window' is not defined.  no-undef
347:37  Warning: 'window' is not defined.  no-undef
350:35  Warning: 'console' is not defined.  no-undef
400:7  Warning: 'localStorage' is not defined.  no-undef

./src/components/ui/adjacent-drawer.tsx
9:8  Warning: 'CustomCloseButton' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/ui/alert-dialog.tsx
51:25  Warning: 'HTMLDivElement' is not defined.  no-undef
65:25  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/badge.tsx
27:32  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/button.tsx
38:38  Warning: 'HTMLButtonElement' is not defined.  no-undef
43:33  Warning: 'HTMLButtonElement' is not defined.  no-undef

./src/components/ui/calendar.tsx
63:25  Warning: 'props' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
64:26  Warning: 'props' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/components/ui/card.tsx
6:3  Warning: 'HTMLDivElement' is not defined.  no-undef
7:24  Warning: 'HTMLDivElement' is not defined.  no-undef
21:3  Warning: 'HTMLDivElement' is not defined.  no-undef
22:24  Warning: 'HTMLDivElement' is not defined.  no-undef
33:3  Warning: 'HTMLDivElement' is not defined.  no-undef
34:24  Warning: 'HTMLDivElement' is not defined.  no-undef
45:3  Warning: 'HTMLDivElement' is not defined.  no-undef
46:24  Warning: 'HTMLDivElement' is not defined.  no-undef
57:3  Warning: 'HTMLDivElement' is not defined.  no-undef
58:24  Warning: 'HTMLDivElement' is not defined.  no-undef
65:3  Warning: 'HTMLDivElement' is not defined.  no-undef
66:24  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/carousel.tsx
46:3  Warning: 'HTMLDivElement' is not defined.  no-undef
47:24  Warning: 'HTMLDivElement' is not defined.  no-undef
89:35  Warning: 'HTMLDivElement' is not defined.  no-undef
154:3  Warning: 'HTMLDivElement' is not defined.  no-undef
155:24  Warning: 'HTMLDivElement' is not defined.  no-undef
176:3  Warning: 'HTMLDivElement' is not defined.  no-undef
177:24  Warning: 'HTMLDivElement' is not defined.  no-undef
198:3  Warning: 'HTMLButtonElement' is not defined.  no-undef
227:3  Warning: 'HTMLButtonElement' is not defined.  no-undef

./src/components/ui/dropdown-menu.tsx
175:25  Warning: 'HTMLSpanElement' is not defined.  no-undef

./src/components/ui/input.tsx
5:32  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/components/ui/sheet.tsx
89:25  Warning: 'HTMLDivElement' is not defined.  no-undef
103:25  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/sidebarSheet.tsx
5:64  Warning: 'HTMLDivElement' is not defined.  no-undef
12:5  Warning: 'open' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
13:5  Warning: 'onOpenChange' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
23:61  Warning: 'HTMLDivElement' is not defined.  no-undef
39:66  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/components/ui/sonner.tsx
6:21  Warning: 'React' is not defined.  no-undef

./src/components/ui/textarea.tsx
6:3  Warning: 'HTMLTextAreaElement' is not defined.  no-undef

./src/firebase/authService.ts
3:3  Warning: 'Auth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:26  Warning: 'console' is not defined.  no-undef
35:5  Warning: 'console' is not defined.  no-undef
42:3  Error: Unnecessary try/catch wrapper.  no-useless-catch
59:3  Error: Unnecessary try/catch wrapper.  no-useless-catch
62:5  Warning: 'localStorage' is not defined.  no-undef
78:5  Warning: 'console' is not defined.  no-undef
92:5  Warning: 'console' is not defined.  no-undef
110:11  Warning: 'accessToken' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
117:5  Warning: 'console' is not defined.  no-undef
121:7  Warning: 'console' is not defined.  no-undef

./src/globalComponents/globalCardEvents.tsx
4:3  Warning: 'CardContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'CardFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:10  Warning: 'calculateDateDifference' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
84:5  Warning: 'console' is not defined.  no-undef

./src/globalComponents/globalProfileCard.tsx
28:21  Warning: 'process' is not defined.  no-undef
43:11  Warning: 'console' is not defined.  no-undef
56:9  Warning: 'console' is not defined.  no-undef
71:11  Warning: 'console' is not defined.  no-undef
84:9  Warning: 'console' is not defined.  no-undef
183:13  Warning: 'console' is not defined.  no-undef
198:21  Warning: 'sessionStorage' is not defined.  no-undef
199:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/globalComponents/globalProfileCardLens.tsx
4:8  Warning: 'useProfile' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:10  Warning: 'FollowerManager' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:10  Warning: 'getUserById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:10  Warning: 'Weight' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:11  Warning: 'isSignedInQuery' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:28  Warning: 'profileQuery' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:9  Warning: 'isAuthW3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
44:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
101:7  Warning: 'requestAnimationFrame' is not defined.  no-undef
194:21  Warning: 'console' is not defined.  no-undef
225:27  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
231:21  Warning: 'console' is not defined.  no-undef

./src/globalComponents/homeComp/postsComp.tsx
8:10  Warning: 'categoryData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:15  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:9  Warning: 'console' is not defined.  no-undef
69:18  Error: Unexpected constant truthiness on the left-hand side of a `&&` expression.  no-constant-binary-expression

./src/globalComponents/imageChunkGrid.tsx
7:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:21  Warning: 'process' is not defined.  no-undef
63:9  Warning: 'console' is not defined.  no-undef

./src/graphql/auth-fetcher.ts
8:13  Warning: 'RequestInit' is not defined.  no-undef
36:23  Warning: 'fetch' is not defined.  no-undef

./src/hook/events/index.tsx
35:14  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars
41:7  Warning: 'setTimeout' is not defined.  no-undef

./src/hook/generateUrl.tsx
12:21  Warning: 'process' is not defined.  no-undef

./src/hook/lensDataFilter.tsx
18:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:18  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:14  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
54:18  Warning: 'loadingPublications' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
55:14  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
100:12  Error: Unsafe usage of optional chaining. If it short-circuits with 'undefined' the evaluation will throw TypeError.  no-unsafe-optional-chaining

./src/hook/multiProfileData.tsx
1:10  Warning: 'User' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:16  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars

./src/hook/profileData.tsx
40:16  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/apollo-client.ts
13:17  Warning: 'localStorage' is not defined.  no-undef

./src/lib/auth/auth-helpers.ts
21:14  Warning: 'localStorage' is not defined.  no-undef
21:30  Warning: 'window' is not defined.  no-undef
31:9  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
59:14  Warning: 'localStorage' is not defined.  no-undef
59:30  Warning: 'window' is not defined.  no-undef
65:9  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:3  Warning: 'Buffer' is not defined.  no-undef

./src/lib/auth/refreshAccessToken.ts
24:15  Warning: 'RequestInit' is not defined.  no-undef
26:23  Warning: 'fetch' is not defined.  no-undef

./src/lib/auth/useLensUser.ts
6:3  Warning: 'useAccountStatsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'console' is not defined.  no-undef
53:5  Warning: 'console' is not defined.  no-undef

./src/lib/auth/useLogin.ts
5:3  Warning: 'PageSize' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'useAccountsAvailableQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'console' is not defined.  no-undef
55:5  Warning: 'console' is not defined.  no-undef
70:5  Warning: 'console' is not defined.  no-undef
103:5  Warning: 'setTimeout' is not defined.  no-undef
104:7  Warning: 'window' is not defined.  no-undef

./src/lib/lens-api.ts
6:17  Warning: 'localStorage' is not defined.  no-undef
8:26  Warning: 'fetch' is not defined.  no-undef

./src/lib/Providers.tsx
6:53  Warning: 'React' is not defined.  no-undef

./src/lib/useBookMark.ts
18:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
25:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useCommen.ts
19:5  Warning: 'ismomoke' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
21:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
28:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useCreatePost.ts
17:29  Warning: 'File' is not defined.  no-undef
18:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
25:36  Warning: 'localStorage' is not defined.  no-undef
40:24  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useDeletePost.ts
14:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
21:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useFollow.ts
17:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
18:7  Warning: 'console' is not defined.  no-undef
25:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useReaction.ts
23:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
30:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useReport.ts
4:3  Warning: 'PublicationReportingReason' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:3  Warning: 'useReportPublicationMutation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
42:36  Warning: 'localStorage' is not defined.  no-undef
46:7  Warning: 'typedData' is never reassigned. Use 'const' instead.  prefer-const

./src/lib/useUnfollow.ts
14:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
21:36  Warning: 'localStorage' is not defined.  no-undef

./src/lib/useUpdateFeed.ts
28:13  Warning: 'File' is not defined.  no-undef
32:5  Error: Unnecessary try/catch wrapper.  no-useless-catch
42:36  Warning: 'localStorage' is not defined.  no-undef

./src/screens/auth/index.tsx
3:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:20  Warning: 'UserProfile' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:19  Warning: 'React' is not defined.  no-undef
38:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:22  Warning: 'connector' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:33  Warning: 'isConnected' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:49  Warning: React Hook "useAccount" cannot be called in an async function.  react-hooks/rules-of-hooks
67:7  Warning: 'localStorage' is not defined.  no-undef
85:9  Warning: 'UpdateUserData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
101:7  Warning: 'console' is not defined.  no-undef
137:9  Warning: 'localStorage' is not defined.  no-undef
154:11  Warning: 'setTimeout' is not defined.  no-undef
167:15  Warning: 'setTimeout' is not defined.  no-undef
169:17  Warning: 'window' is not defined.  no-undef
172:15  Warning: 'console' is not defined.  no-undef
183:15  Warning: 'window' is not defined.  no-undef
187:11  Warning: 'console' is not defined.  no-undef
189:11  Warning: 'setTimeout' is not defined.  no-undef
201:13  Warning: 'setTimeout' is not defined.  no-undef
203:15  Warning: 'window' is not defined.  no-undef
209:7  Warning: 'console' is not defined.  no-undef
225:20  Warning: 'localStorage' is not defined.  no-undef
228:20  Warning: 'localStorage' is not defined.  no-undef
231:20  Warning: 'localStorage' is not defined.  no-undef
234:20  Warning: 'localStorage' is not defined.  no-undef
281:5  Warning: 'console' is not defined.  no-undef
304:7  Warning: 'localStorage' is not defined.  no-undef
314:7  Warning: 'setTimeout' is not defined.  no-undef
330:11  Warning: 'setTimeout' is not defined.  no-undef
332:13  Warning: 'window' is not defined.  no-undef
335:11  Warning: 'console' is not defined.  no-undef
347:11  Warning: 'setTimeout' is not defined.  no-undef
348:13  Warning: 'window' is not defined.  no-undef
353:7  Warning: 'console' is not defined.  no-undef
367:7  Warning: 'localStorage' is not defined.  no-undef
389:7  Warning: 'setTimeout' is not defined.  no-undef
402:11  Warning: 'setTimeout' is not defined.  no-undef
404:13  Warning: 'window' is not defined.  no-undef
407:11  Warning: 'console' is not defined.  no-undef
419:11  Warning: 'setTimeout' is not defined.  no-undef
420:13  Warning: 'window' is not defined.  no-undef
425:7  Warning: 'console' is not defined.  no-undef
439:7  Warning: 'localStorage' is not defined.  no-undef
461:7  Warning: 'setTimeout' is not defined.  no-undef
474:11  Warning: 'setTimeout' is not defined.  no-undef
476:13  Warning: 'window' is not defined.  no-undef
479:11  Warning: 'console' is not defined.  no-undef
491:11  Warning: 'setTimeout' is not defined.  no-undef
492:13  Warning: 'window' is not defined.  no-undef
497:7  Warning: 'console' is not defined.  no-undef
511:7  Warning: 'localStorage' is not defined.  no-undef
533:7  Warning: 'setTimeout' is not defined.  no-undef
546:11  Warning: 'setTimeout' is not defined.  no-undef
548:13  Warning: 'window' is not defined.  no-undef
551:11  Warning: 'console' is not defined.  no-undef
563:11  Warning: 'setTimeout' is not defined.  no-undef
564:13  Warning: 'window' is not defined.  no-undef
569:7  Warning: 'console' is not defined.  no-undef

./src/screens/basket/basketCard.tsx
7:3  Warning: 'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
101:43  Error: Irregular whitespace not allowed.  no-irregular-whitespace

./src/screens/basket/index.tsx
13:10  Warning: 'isSheetOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/browse/browseLensUser/browseLensData.tsx
78:7  Warning: 'console' is not defined.  no-undef

./src/screens/browse/browseLensUser/browseLensUserCard.tsx
2:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:3  Warning: 'ModalHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:28  Warning: 'getLocation' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:27  Warning: 'HTMLDivElement' is not defined.  no-undef
99:10  Warning: 'reload' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
99:18  Warning: 'setSetreload' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
102:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
110:9  Warning: 'handleStarClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
119:9  Warning: 'handleSaveClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
128:9  Warning: 'handleRefresh' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
142:9  Warning: 'handleInfoClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
148:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
152:18  Warning: 'setPostId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
159:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
200:24  Warning: 'setInterval' is not defined.  no-undef
202:9  Warning: 'alert' is not defined.  no-undef
203:9  Warning: 'clearInterval' is not defined.  no-undef
211:9  Warning: 'alert' is not defined.  no-undef
218:9  Warning: 'clearInterval' is not defined.  no-undef
225:18  Warning: 'clearInterval' is not defined.  no-undef
230:7  Warning: 'sessionStorage' is not defined.  no-undef
231:7  Warning: 'sessionStorage' is not defined.  no-undef
236:7  Warning: 'console' is not defined.  no-undef
245:7  Warning: 'alert' is not defined.  no-undef
269:9  Warning: 'console' is not defined.  no-undef
274:15  Warning: 'resplike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
289:15  Warning: 'respdislike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
304:7  Warning: 'alert' is not defined.  no-undef
315:9  Warning: 'console' is not defined.  no-undef
319:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
322:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
328:7  Warning: 'alert' is not defined.  no-undef
352:5  Warning: 'navigator' is not defined.  no-undef
358:9  Warning: 'console' is not defined.  no-undef
368:25  Warning: 'options' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
368:39  Warning: 'pswp' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
369:10  Warning: 'document' is not defined.  no-undef
370:10  Warning: 'window' is not defined.  no-undef
383:21  Warning: 'Image' is not defined.  no-undef
390:27  Warning: 'HTMLVideoElement' is not defined.  no-undef
394:29  Warning: 'HTMLVideoElement' is not defined.  no-undef
665:20  Error: Unexpected constant condition.  no-constant-condition
781:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
860:33  Error: Unexpected constant condition.  no-constant-condition
897:32  Error: Unexpected constant condition.  no-constant-condition
1182:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1280:33  Warning: 'alert' is not defined.  no-undef
1283:31  Warning: 'alert' is not defined.  no-undef
1311:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1352:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1358:23  Warning: 'sessionStorage' is not defined.  no-undef
1359:23  Warning: 'sessionStorage' is not defined.  no-undef
1383:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/browse/browseLensUser/index.tsx
6:3  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'LensTransactionStatusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:3  Warning: 'useLensTransactionStatusQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:10  Warning: 'ChevronsLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:24  Warning: 'ChevronsRight' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:3  Warning: 'AlertDialogFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:3  Warning: 'userPost' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
62:9  Warning: 'txnIdRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
63:10  Warning: 'currentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
63:25  Warning: 'setCurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
70:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
70:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
72:9  Warning: 'profileImage' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
136:9  Warning: 'buttonStyles' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
146:9  Warning: 'window' is not defined.  no-undef
147:9  Warning: 'window' is not defined.  no-undef
148:9  Warning: 'window' is not defined.  no-undef
152:19  Warning: 'document' is not defined.  no-undef
155:29  Warning: 'HTMLElement' is not defined.  no-undef
250:39  Warning: 'console' is not defined.  no-undef
282:45  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
292:39  Warning: 'console' is not defined.  no-undef
382:37  Warning: 'console' is not defined.  no-undef
414:43  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
424:37  Warning: 'console' is not defined.  no-undef
490:34  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
523:24  Warning: 'window' is not defined.  no-undef
574:21  Warning: 'sessionStorage' is not defined.  no-undef
575:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/browse/browseUser/browseUserCard.tsx
13:3  Warning: 'ChevronLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'AlertDialogFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:27  Warning: 'HTMLDivElement' is not defined.  no-undef
80:10  Warning: 'starCount' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
80:21  Warning: 'setStarCount' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
83:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
112:9  Warning: 'handleInfoClick' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
120:21  Warning: 'process' is not defined.  no-undef
139:16  Warning: 'sessionStorage' is not defined.  no-undef
141:19  Warning: 'sessionStorage' is not defined.  no-undef
144:7  Warning: 'sessionStorage' is not defined.  no-undef
145:7  Warning: 'sessionStorage' is not defined.  no-undef
152:7  Warning: 'sessionStorage' is not defined.  no-undef
153:7  Warning: 'sessionStorage' is not defined.  no-undef
160:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
172:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
174:7  Warning: 'alert' is not defined.  no-undef
211:9  Warning: 'alert' is not defined.  no-undef
232:7  Warning: 'console' is not defined.  no-undef
234:13  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
238:7  Warning: 'console' is not defined.  no-undef
255:7  Warning: 'console' is not defined.  no-undef
257:13  Warning: 'response' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
261:7  Warning: 'console' is not defined.  no-undef
279:7  Warning: 'console' is not defined.  no-undef
283:7  Warning: 'console' is not defined.  no-undef
300:7  Warning: 'console' is not defined.  no-undef
305:7  Warning: 'console' is not defined.  no-undef
363:25  Warning: 'options' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
363:39  Warning: 'pswp' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
364:10  Warning: 'document' is not defined.  no-undef
365:10  Warning: 'window' is not defined.  no-undef
371:23  Warning: 'Image' is not defined.  no-undef
391:5  Warning: 'navigator' is not defined.  no-undef
397:9  Warning: 'console' is not defined.  no-undef
401:27  Warning: 'HTMLVideoElement' is not defined.  no-undef
405:29  Warning: 'HTMLVideoElement' is not defined.  no-undef
422:9  Warning: 'alert' is not defined.  no-undef
425:7  Warning: 'alert' is not defined.  no-undef
426:7  Warning: 'console' is not defined.  no-undef
703:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1127:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1238:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1257:29  Warning: 'window' is not defined.  no-undef
1304:13  Warning: 'console' is not defined.  no-undef
1319:21  Warning: 'sessionStorage' is not defined.  no-undef
1320:21  Warning: 'sessionStorage' is not defined.  no-undef
1364:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1390:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/browse/browseUser/editPost.tsx
6:10  Warning: 'updateUser' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
38:38  Warning: 'File' is not defined.  no-undef
43:10  Warning: 'showConfirmation' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
43:28  Warning: 'setShowConfirmation' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:13  Warning: 'alert' is not defined.  no-undef
74:11  Warning: 'console' is not defined.  no-undef
75:11  Warning: 'alert' is not defined.  no-undef
85:35  Warning: 'React' is not defined.  no-undef
89:7  Warning: 'alert' is not defined.  no-undef
105:9  Warning: 'alert' is not defined.  no-undef
131:9  Warning: 'alert' is not defined.  no-undef
136:7  Warning: 'console' is not defined.  no-undef
137:7  Warning: 'alert' is not defined.  no-undef
141:37  Warning: 'React' is not defined.  no-undef
141:55  Warning: 'HTMLInputElement' is not defined.  no-undef
149:26  Warning: 'React' is not defined.  no-undef
149:46  Warning: 'HTMLInputElement' is not defined.  no-undef
167:25  Warning: 'React' is not defined.  no-undef
167:45  Warning: 'HTMLInputElement' is not defined.  no-undef
215:17  Warning: 'window' is not defined.  no-undef
324:30  Warning: 'URL' is not defined.  no-undef

./src/screens/browse/browseUser/index.tsx
22:3  Warning: 'AlertDialogFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:21  Warning: 'process' is not defined.  no-undef
56:16  Warning: React Hook "useAuth" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
59:27  Warning: React Hook "useProfile" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
60:41  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
61:10  Warning: 'triggerEffect' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:45  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
64:24  Warning: React Hook "useCallback" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
75:11  Warning: 'console' is not defined.  no-undef
87:9  Warning: 'console' is not defined.  no-undef
96:26  Warning: React Hook "useCallback" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
107:11  Warning: 'console' is not defined.  no-undef
119:9  Warning: 'console' is not defined.  no-undef
141:3  Warning: React Hook "useEffect" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
145:43  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
149:10  Warning: 'slidesToShow' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
149:43  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
151:21  Warning: React Hook "useRef" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
153:30  Warning: 'swiper' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
153:50  Warning: 'length' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
160:9  Warning: 'console' is not defined.  no-undef
164:47  Warning: React Hook "useState" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
166:3  Warning: React Hook "useEffect" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
178:3  Warning: React Hook "useEffect" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?  react-hooks/rules-of-hooks
180:11  Warning: 'window' is not defined.  no-undef
182:18  Warning: 'window' is not defined.  no-undef
190:5  Warning: 'window' is not defined.  no-undef
191:18  Warning: 'window' is not defined.  no-undef
195:9  Warning: 'window' is not defined.  no-undef
196:9  Warning: 'window' is not defined.  no-undef
197:9  Warning: 'window' is not defined.  no-undef
409:26  Warning: 'window' is not defined.  no-undef

./src/screens/browse/index.tsx
1:10  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:21  Warning: 'ProfilesOrderBy' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'FollowersQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'FollowingQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:10  Warning: 'currentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
52:27  Warning: 'HTMLDivElement' is not defined.  no-undef
54:37  Warning: 'NodeJS' is not defined.  no-undef
67:5  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
98:7  Warning: 'console' is not defined.  no-undef
151:7  Warning: 'console' is not defined.  no-undef
214:7  Warning: 'console' is not defined.  no-undef
281:9  Warning: 'clearTimeout' is not defined.  no-undef
296:9  Warning: 'clearTimeout' is not defined.  no-undef
299:36  Warning: 'setTimeout' is not defined.  no-undef
363:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
363:31  Warning: 'HTMLDivElement' is not defined.  no-undef
365:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
372:7  Warning: 'console' is not defined.  no-undef

./src/screens/chat/chatBox.tsx
11:3  Warning: 'chatId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/eventsCard.tsx
7:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:10  Warning: 'userID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:18  Warning: 'setUserID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
104:7  Warning: 'console' is not defined.  no-undef
119:16  Warning: 'NodeJS' is not defined.  no-undef
123:15  Warning: 'setTimeout' is not defined.  no-undef
129:18  Warning: 'clearTimeout' is not defined.  no-undef
142:7  Warning: 'console' is not defined.  no-undef
156:7  Warning: 'console' is not defined.  no-undef
184:33  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/index.tsx
3:10  Warning: 'GlobalCardEvents' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
60:28  Warning: 'HTMLDivElement' is not defined.  no-undef
62:12  Warning: 'hexToRgba' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
111:31  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>
22:28  Warning: 'localStorage' is not defined.  no-undef
27:13  Warning: 'localStorage' is not defined.  no-undef
31:9  Warning: 'console' is not defined.  no-undef
51:20  Warning: 'React' is not defined.  no-undef

./src/screens/home/<USER>/imageCard.tsx
7:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
33:21  Warning: 'process' is not defined.  no-undef
81:9  Warning: 'console' is not defined.  no-undef

./src/screens/home/<USER>/index.tsx
72:28  Warning: 'HTMLDivElement' is not defined.  no-undef
75:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
75:27  Warning: 'HTMLDivElement' is not defined.  no-undef
89:33  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
94:32  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
121:10  Warning: 'imgHeight' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
122:33  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
123:13  Warning: 'pageYOffset' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
123:26  Warning: 'scrollY' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
123:38  Warning: 'window' is not defined.  no-undef
131:45  Warning: 'window' is not defined.  no-undef
136:5  Warning: 'window' is not defined.  no-undef
137:5  Warning: 'window' is not defined.  no-undef
140:7  Warning: 'window' is not defined.  no-undef
142:7  Warning: 'window' is not defined.  no-undef
156:23  Warning: 'setProfileData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:26  Warning: 'setLoadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
158:10  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
158:24  Warning: 'setProfileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
161:10  Warning: 'publicationsData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
163:10  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
408:35  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
462:35  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/postMobileView.tsx
22:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:21  Warning: 'process' is not defined.  no-undef
79:20  Warning: 'postCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
130:7  Warning: 'console' is not defined.  no-undef
149:7  Warning: 'console' is not defined.  no-undef
163:7  Warning: 'console' is not defined.  no-undef
176:7  Warning: 'console' is not defined.  no-undef

./src/screens/home/<USER>/test.tsx
6:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'PublicationMetadataMainFocusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'PublicationType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:3  Warning: 'usePublicationsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'getLensProfileDetails' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:20  Warning: 'postCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
127:7  Warning: 'console' is not defined.  no-undef
146:7  Warning: 'console' is not defined.  no-undef
160:7  Warning: 'console' is not defined.  no-undef
177:9  Warning: 'id' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
196:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
197:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
212:16  Warning: 'isLoadingPublications' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
214:12  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
298:9  Warning: 'loadNextPage' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/videoWithThumbnail.tsx
10:21  Warning: 'process' is not defined.  no-undef
25:9  Warning: 'thumbnailUrl' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/index.tsx
8:8  Warning: 'GlobalProfileCard' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
58:28  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/home/<USER>/profileCard.tsx
1:10  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:23  Warning: 'getUserById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:9  Warning: 'isAuthLogin' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:10  Warning: 'followingList' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:7  Warning: 'console' is not defined.  no-undef
156:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
205:34  Warning: 'setCurrentFollowingCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:31  Warning: 'HTMLDivElement' is not defined.  no-undef
211:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
218:7  Warning: 'console' is not defined.  no-undef
225:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
279:15  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/text.tsx
3:10  Warning: 'GlobalCardEvents' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'GlobalCard' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
56:28  Warning: 'HTMLDivElement' is not defined.  no-undef
139:31  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/textProfile.tsx
1:10  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:21  Warning: 'ProfilesOrderBy' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'LoadingOverlay' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:8  Warning: 'ScrollButton' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'FollowersQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'FollowingQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:10  Warning: 'currentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
48:25  Warning: 'setCurrentCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:27  Warning: 'HTMLDivElement' is not defined.  no-undef
59:5  Warning: 'paramPID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
64:5  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
65:5  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
66:5  Warning: 'refetch' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:7  Warning: 'console' is not defined.  no-undef
203:9  Warning: 'handleScroll' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
245:34  Warning: 'setCurrentFollowingCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
249:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
249:31  Warning: 'HTMLDivElement' is not defined.  no-undef
251:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
258:7  Warning: 'console' is not defined.  no-undef
266:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/index.tsx
57:28  Warning: 'HTMLDivElement' is not defined.  no-undef
100:31  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/home/<USER>/servicesCard.tsx
41:11  Warning: 'console' is not defined.  no-undef
46:9  Warning: 'console' is not defined.  no-undef

./src/screens/orders/index.tsx
2:3  Warning: 'Sheet' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:3  Warning: 'SheetContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:3  Warning: 'SheetDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'SheetHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'SheetTitle' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:23  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:10  Warning: 'isSheetOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
75:22  Warning: 'React' is not defined.  no-undef

./src/screens/orders/orderCardDetail.tsx
2:23  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'closeEvent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
56:22  Warning: 'React' is not defined.  no-undef

./src/screens/orders/orderDetail/activeLog.tsx
22:26  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/orders/orderDetail/detail.tsx
57:46  Warning: 'indexs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/orders/orderDetail/orderCardDetail.tsx
7:23  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
67:24  Warning: 'React' is not defined.  no-undef

./src/screens/profile/index.tsx
4:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:27  Warning: 'HTMLDivElement' is not defined.  no-undef
27:7  Warning: 'localStorage' is not defined.  no-undef
42:11  Warning: 'console' is not defined.  no-undef
48:11  Warning: 'console' is not defined.  no-undef

./src/screens/profile/lensProfile/index.tsx
6:10  Warning: 'useAccountQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'getId' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'useSearchParams' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:10  Warning: 'isRefatch' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:11  Warning: 'address' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:11  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
37:9  Warning: 'userId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:20  Warning: 'localStorage' is not defined.  no-undef
60:16  Warning: 'isLoadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
61:12  Warning: 'profileQueryError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
118:22  Warning: 'React' is not defined.  no-undef
134:17  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/aboutMe/index.tsx
4:3  Warning: 'AlertDialog' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'AlertDialogContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'AlertDialogDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:3  Warning: 'AlertDialogHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:3  Warning: 'AlertDialogTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:42  Warning: 'Tooltip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:10  Warning: 'isToggle' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:20  Warning: 'setIsToggle' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:27  Warning: 'e' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
52:23  Warning: 'window' is not defined.  no-undef
110:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/hashtags/index.tsx
2:10  Warning: 'Edit2' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:10  Warning: 'Textarea' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:7  Warning: 'data' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:10  Warning: 'profileName' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:23  Warning: 'setProfileName' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:10  Warning: 'location' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:20  Warning: 'setLocation' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
32:26  Warning: 'React' is not defined.  no-undef
32:46  Warning: 'HTMLInputElement' is not defined.  no-undef
76:49  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/index.tsx
40:10  Warning: 'Progress' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:10  Warning: 'ref' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:15  Warning: 'uploadBytes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:28  Warning: 'getDownloadURL' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:8  Warning: 'useLensUser' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
50:10  Warning: 'Web3Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
51:10  Warning: 'LENS_CONTRACT_ABI' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
51:29  Warning: 'LENS_CONTRACT_ADDRESS' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
53:10  Warning: 'initFirebase' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
59:10  Warning: 'Chip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
110:10  Warning: 'isbothID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
112:20  Warning: 'isAuthW3' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
115:10  Warning: 'SelectedCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
154:11  Warning: 'setTimeout' is not defined.  no-undef
167:11  Warning: 'console' is not defined.  no-undef
171:9  Warning: 'console' is not defined.  no-undef
185:11  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
199:10  Warning: 'progress' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
202:19  Warning: 'setTimeout' is not defined.  no-undef
203:18  Warning: 'clearTimeout' is not defined.  no-undef
212:23  Warning: 'setProfileName' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
213:9  Warning: 'addHashtag' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
213:46  Warning: 'HTMLInputElement' is not defined.  no-undef
221:9  Warning: 'removeHashtag' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
225:9  Warning: 'handleSubmit' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
235:14  Warning: Empty block statement.  no-empty
242:43  Warning: 'File' is not defined.  no-undef
247:55  Warning: 'HTMLInputElement' is not defined.  no-undef
251:7  Warning: 'console' is not defined.  no-undef
260:36  Warning: 'isSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
260:53  Warning: 'followData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
262:10  Warning: 'CurrentStatus' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
268:16  Warning: 'isLoadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
269:12  Warning: 'profileQueryError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
286:10  Warning: 'lensId' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
306:7  Warning: 'console' is not defined.  no-undef
322:5  Warning: 'navigator' is not defined.  no-undef
328:9  Warning: 'console' is not defined.  no-undef
333:9  Warning: 'toggleIcon' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
367:5  Warning: 'console' is not defined.  no-undef
425:26  Warning: 'setInterval' is not defined.  no-undef
427:11  Warning: 'clearInterval' is not defined.  no-undef
439:11  Warning: 'setTimeout' is not defined.  no-undef
445:11  Warning: 'clearInterval' is not defined.  no-undef
452:11  Warning: 'clearInterval' is not defined.  no-undef
459:20  Warning: 'clearInterval' is not defined.  no-undef
470:26  Warning: 'setInterval' is not defined.  no-undef
472:11  Warning: 'clearInterval' is not defined.  no-undef
484:11  Warning: 'setTimeout' is not defined.  no-undef
490:11  Warning: 'clearInterval' is not defined.  no-undef
497:11  Warning: 'clearInterval' is not defined.  no-undef
504:20  Warning: 'clearInterval' is not defined.  no-undef
515:26  Warning: 'setInterval' is not defined.  no-undef
517:11  Warning: 'clearInterval' is not defined.  no-undef
529:11  Warning: 'setTimeout' is not defined.  no-undef
535:11  Warning: 'clearInterval' is not defined.  no-undef
542:11  Warning: 'clearInterval' is not defined.  no-undef
549:20  Warning: 'clearInterval' is not defined.  no-undef
576:24  Warning: 'window' is not defined.  no-undef
582:5  Warning: 'window' is not defined.  no-undef
586:7  Warning: 'window' is not defined.  no-undef
624:32  Warning: 'window' is not defined.  no-undef
885:37  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1037:29  Warning: 'e' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1039:25  Warning: 'window' is not defined.  no-undef
1143:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1246:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1364:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1405:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1437:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1450:25  Error: Unexpected constant condition.  no-constant-condition
1495:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1532:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1757:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1823:52  Warning: 'Blob' is not defined.  no-undef
1826:36  Warning: 'URL' is not defined.  no-undef
1895:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1909:48  Warning: 'Blob' is not defined.  no-undef
1912:32  Warning: 'URL' is not defined.  no-undef
1973:13  Warning: 'console' is not defined.  no-undef
1984:21  Warning: 'sessionStorage' is not defined.  no-undef
1985:21  Warning: 'sessionStorage' is not defined.  no-undef
2001:13  Warning: 'console' is not defined.  no-undef
2036:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/personalMotto/index.tsx
2:10  Warning: 'Edit2' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:9  Warning: 'profile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/profileInfo/socialMedia/index.tsx
3:3  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:3  Warning: 'MoreHorizontal' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'Edit2' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:10  Warning: 'Textarea' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
142:23  Error: Unexpected constant condition.  no-constant-condition

./src/screens/profile/lensProfile/tabs/events/calender/index.tsx
8:11  Warning: 'CustomCalendarProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'activeDate' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
21:22  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:29  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
178:31  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/events/calenderEvents/index.tsx
2:17  Warning: 'useState' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/events/createEvent.tsx
2:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:10  Warning: 'FilePlus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:20  Warning: 'Plus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:26  Warning: 'Trash' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'FileUploader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:27  Warning: 'HTMLDivElement' is not defined.  no-undef
16:9  Warning: 'Category' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:34  Warning: 'React' is not defined.  no-undef
53:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
53:21  Warning: Empty block statement.  no-empty
83:32  Warning: 'React' is not defined.  no-undef
83:50  Warning: 'HTMLInputElement' is not defined.  no-undef
88:32  Warning: 'React' is not defined.  no-undef
88:50  Warning: 'HTMLInputElement' is not defined.  no-undef
93:9  Warning: 'handleLog' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
95:7  Warning: 'console' is not defined.  no-undef
97:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/events/editEvents/index.tsx
2:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:27  Warning: 'HTMLDivElement' is not defined.  no-undef
28:10  Warning: 'eventData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
83:19  Warning: 'console' is not defined.  no-undef
104:19  Warning: 'console' is not defined.  no-undef
107:17  Warning: 'console' is not defined.  no-undef
111:13  Warning: 'console' is not defined.  no-undef
114:11  Warning: 'console' is not defined.  no-undef
133:34  Warning: 'React' is not defined.  no-undef
150:11  Warning: 'console' is not defined.  no-undef
153:11  Warning: 'console' is not defined.  no-undef
164:13  Warning: 'console' is not defined.  no-undef
168:11  Warning: 'console' is not defined.  no-undef
172:7  Warning: 'console' is not defined.  no-undef
199:32  Warning: 'React' is not defined.  no-undef
199:50  Warning: 'HTMLInputElement' is not defined.  no-undef
204:32  Warning: 'React' is not defined.  no-undef
204:50  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/events/eventsCard/index.tsx
28:16  Warning: 'NodeJS' is not defined.  no-undef
32:15  Warning: 'setTimeout' is not defined.  no-undef
38:18  Warning: 'clearTimeout' is not defined.  no-undef
50:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/events/index.tsx
2:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/index.tsx
12:10  Warning: 'Plus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
49:3  Warning: 'setSelectedProfileTabs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
53:27  Warning: 'HTMLDivElement' is not defined.  no-undef
56:10  Warning: 'isbothID' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:7  Warning: 'console' is not defined.  no-undef
117:22  Warning: 'React' is not defined.  no-undef
188:33  Warning: 'index' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
259:16  Error: Unexpected constant condition.  no-constant-condition
326:23  Error: Unexpected constant condition.  no-constant-condition

./src/screens/profile/lensProfile/tabs/otherServices/index.tsx
14:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/editCustomizations.tsx
8:10  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:22  Warning: 'Info' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:18  Warning: 'setIsTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/index.tsx
34:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
105:18  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/otherServices/serviceDetails/ViewService.tsx
32:21  Warning: 'process' is not defined.  no-undef
49:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/otherServices/servicesCard/index.tsx
3:8  Warning: 'useProfile' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:8  Warning: 'useAuth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/posts/createPost.tsx
2:8  Warning: 'useAuth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:38  Warning: 'File' is not defined.  no-undef
13:9  Warning: 'handleFileChange' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:5  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
14:12  Warning: 'React' is not defined.  no-undef
14:30  Warning: 'HTMLInputElement' is not defined.  no-undef
18:7  Warning: 'console' is not defined.  no-undef
26:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:9  Warning: 'console' is not defined.  no-undef
37:37  Warning: 'React' is not defined.  no-undef
37:55  Warning: 'HTMLInputElement' is not defined.  no-undef
138:28  Warning: 'URL' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/posts/index.tsx
1:31  Warning: 'useRef' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:3  Warning: 'ChevronLeft' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
71:9  Warning: 'queryClient' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
76:10  Warning: 'profileData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
77:10  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:10  Warning: 'showMoreOptions' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:27  Warning: 'setShowMoreOptions' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
106:10  Warning: 'isResonPost' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
111:12  Warning: 'profileQueryError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
171:12  Error: Unsafe usage of optional chaining. If it short-circuits with 'undefined' the evaluation will throw TypeError.  no-unsafe-optional-chaining
177:7  Warning: 'setTimeout' is not defined.  no-undef
213:21  Warning: 'setTimeout' is not defined.  no-undef
217:20  Warning: 'clearTimeout' is not defined.  no-undef
238:5  Warning: 'console' is not defined.  no-undef
244:24  Warning: 'setInterval' is not defined.  no-undef
246:9  Warning: 'alert' is not defined.  no-undef
247:9  Warning: 'clearInterval' is not defined.  no-undef
262:9  Warning: 'clearInterval' is not defined.  no-undef
269:18  Warning: 'clearInterval' is not defined.  no-undef
281:28  Warning: 'React' is not defined.  no-undef
281:42  Warning: 'HTMLDivElement' is not defined.  no-undef
293:12  Warning: 'commentsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
325:7  Warning: 'sessionStorage' is not defined.  no-undef
326:7  Warning: 'sessionStorage' is not defined.  no-undef
332:7  Warning: 'console' is not defined.  no-undef
343:7  Warning: 'alert' is not defined.  no-undef
366:9  Warning: 'console' is not defined.  no-undef
371:15  Warning: 'resplike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
384:15  Warning: 'respdislike' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
400:7  Warning: 'alert' is not defined.  no-undef
415:9  Warning: 'console' is not defined.  no-undef
420:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
423:15  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
431:7  Warning: 'alert' is not defined.  no-undef
571:5  Warning: 'navigator' is not defined.  no-undef
574:9  Warning: 'alert' is not defined.  no-undef
577:9  Warning: 'console' is not defined.  no-undef
581:26  Warning: React Hook "useDeletePost" is called conditionally. React Hooks must be called in the exact same order in every component render.  react-hooks/rules-of-hooks
694:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1057:31  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1061:25  Warning: 'console' is not defined.  no-undef
1093:21  Warning: 'sessionStorage' is not defined.  no-undef
1094:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/profile/followers/index.tsx
35:11  Warning: 'console' is not defined.  no-undef
45:7  Warning: 'console' is not defined.  no-undef
96:42  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/profile/following/index.tsx
2:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:10  Warning: 'themes' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:8  Warning: 'useAuth' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'FollowersQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
41:11  Warning: 'console' is not defined.  no-undef
51:7  Warning: 'console' is not defined.  no-undef
54:31  Warning: 'HTMLDivElement' is not defined.  no-undef
97:7  Warning: 'setTimeout' is not defined.  no-undef
111:42  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/profile/index.tsx
20:3  Warning: 'isOtherProfileStatus' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
34:5  Warning: 'setTimeout' is not defined.  no-undef
38:7  Warning: 'setTimeout' is not defined.  no-undef
55:19  Warning: 'setTimeout' is not defined.  no-undef
60:7  Warning: 'clearTimeout' is not defined.  no-undef
79:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/services/createService.tsx
9:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:10  Warning: 'hashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:20  Warning: 'setHashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:10  Warning: 'geoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:19  Warning: 'setGeoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:10  Warning: 'media' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:17  Warning: 'setMedia' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:38  Warning: 'File' is not defined.  no-undef
23:34  Warning: 'React' is not defined.  no-undef
52:11  Warning: 'console' is not defined.  no-undef
57:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
67:11  Error: Unexpected constant condition.  no-constant-condition

./src/screens/profile/lensProfile/tabs/services/index.tsx
14:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/serviceDetails/editCustomizations.tsx
8:23  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:35  Warning: 'Info' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:10  Warning: 'isTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:18  Warning: 'setIsTrue' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/serviceDetails/editService.tsx
8:23  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
177:52  Warning: 'indexs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/lensProfile/tabs/services/serviceDetails/index.tsx
9:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
81:50  Warning: 'indexs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/lensProfile/tabs/services/servicesCard/index.tsx
33:9  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/index.tsx
10:16  Warning: 'X' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:9  Warning: 'console' is not defined.  no-undef
48:7  Warning: 'console' is not defined.  no-undef
131:21  Warning: 'alert' is not defined.  no-undef
136:22  Warning: 'React' is not defined.  no-undef
154:17  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/aboutMe/index.tsx
3:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:11  Warning: 'setTimeout' is not defined.  no-undef
50:11  Warning: 'setTimeout' is not defined.  no-undef
54:11  Warning: 'console' is not defined.  no-undef
58:9  Warning: 'console' is not defined.  no-undef
72:29  Warning: 'e' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
74:25  Warning: 'window' is not defined.  no-undef
136:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/connectedAccount/index.tsx
11:10  Warning: 'isVerified' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:9  Warning: 'profile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/hashtags/index.tsx
3:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:7  Warning: 'data' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:26  Warning: 'React' is not defined.  no-undef
30:46  Warning: 'HTMLInputElement' is not defined.  no-undef
58:11  Warning: 'setTimeout' is not defined.  no-undef
71:11  Warning: 'setTimeout' is not defined.  no-undef
75:11  Warning: 'console' is not defined.  no-undef
79:9  Warning: 'console' is not defined.  no-undef
136:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/index.tsx
8:3  Warning: 'Search' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:3  Warning: 'AlertDialog' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:3  Warning: 'AlertDialogContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'AlertDialogDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
22:3  Warning: 'AlertDialogHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:3  Warning: 'AlertDialogTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
30:3  Warning: 'Chip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:3  Warning: 'ModalFooter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:3  Warning: 'ModalHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
36:3  Warning: 'Tooltip' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:3  Warning: 'useDisclosure' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
64:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
73:21  Warning: 'process' is not defined.  no-undef
122:10  Warning: 'email' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
122:17  Warning: 'setEmail' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
137:10  Warning: 'isDeleteProcessing' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
137:30  Warning: 'setIsDeleteProcessing' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
138:10  Warning: 'deleteSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
138:25  Warning: 'setDeleteSuccess' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
141:10  Warning: 'SelectedCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
168:14  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
182:24  Warning: 'window' is not defined.  no-undef
188:5  Warning: 'window' is not defined.  no-undef
192:7  Warning: 'window' is not defined.  no-undef
216:7  Warning: 'alert' is not defined.  no-undef
229:5  Warning: 'google' is not defined.  no-undef
232:38  Warning: 'google' is not defined.  no-undef
239:46  Warning: 'HTMLInputElement' is not defined.  no-undef
249:59  Warning: 'HTMLInputElement' is not defined.  no-undef
296:11  Warning: 'setTimeout' is not defined.  no-undef
308:11  Warning: 'console' is not defined.  no-undef
312:9  Warning: 'console' is not defined.  no-undef
339:11  Warning: 'setTimeout' is not defined.  no-undef
352:11  Warning: 'console' is not defined.  no-undef
356:9  Warning: 'console' is not defined.  no-undef
364:43  Warning: 'File' is not defined.  no-undef
388:11  Warning: 'setTimeout' is not defined.  no-undef
400:11  Warning: 'console' is not defined.  no-undef
405:7  Warning: 'console' is not defined.  no-undef
410:55  Warning: 'HTMLInputElement' is not defined.  no-undef
422:10  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
435:7  Warning: 'console' is not defined.  no-undef
447:7  Warning: 'console' is not defined.  no-undef
454:38  Warning: 'userId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
468:7  Warning: 'console' is not defined.  no-undef
501:16  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
535:10  Warning: 'value' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
553:11  Warning: 'setTimeout' is not defined.  no-undef
568:13  Warning: 'window' is not defined.  no-undef
571:11  Warning: 'console' is not defined.  no-undef
575:9  Warning: 'console' is not defined.  no-undef
590:7  Warning: 'alert' is not defined.  no-undef
605:19  Warning: 'setTimeout' is not defined.  no-undef
606:18  Warning: 'clearTimeout' is not defined.  no-undef
641:22  Warning: 'setInterval' is not defined.  no-undef
645:18  Warning: 'clearInterval' is not defined.  no-undef
662:7  Warning: 'console' is not defined.  no-undef
674:5  Warning: 'navigator' is not defined.  no-undef
678:9  Warning: 'setTimeout' is not defined.  no-undef
683:9  Warning: 'console' is not defined.  no-undef
688:9  Warning: 'toggleIcon' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
695:22  Warning: 'process' is not defined.  no-undef
697:9  Warning: 'console' is not defined.  no-undef
701:22  Warning: 'document' is not defined.  no-undef
707:43  Warning: 'google' is not defined.  no-undef
709:7  Warning: 'document' is not defined.  no-undef
714:59  Warning: 'HTMLInputElement' is not defined.  no-undef
732:26  Warning: 'google' is not defined.  no-undef
745:31  Warning: 'google' is not defined.  no-undef
756:61  Warning: 'process' is not defined.  no-undef
767:35  Warning: 'window' is not defined.  no-undef
1156:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1549:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1592:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1624:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1632:35  Error: Unexpected constant condition.  no-constant-condition
1673:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1822:33  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
1952:33  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
2087:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
2165:32  Warning: 'URL' is not defined.  no-undef
2217:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
2234:32  Warning: 'URL' is not defined.  no-undef
2336:23  Warning: 'sessionStorage' is not defined.  no-undef
2337:23  Warning: 'sessionStorage' is not defined.  no-undef
2442:31  Warning: 'navigator' is not defined.  no-undef
2444:31  Warning: 'setTimeout' is not defined.  no-undef
2446:31  Warning: 'window' is not defined.  no-undef

./src/screens/profile/myProfile/profileInfo/personalMotto/index.tsx
3:10  Warning: 'Button' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
4:20  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:10  Warning: 'Input' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:9  Warning: 'profile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
40:11  Warning: 'setTimeout' is not defined.  no-undef
53:11  Warning: 'setTimeout' is not defined.  no-undef
57:11  Warning: 'console' is not defined.  no-undef
61:9  Warning: 'console' is not defined.  no-undef
106:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/socialMedia/index.tsx
4:3  Warning: 'DollarSign' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'MoreHorizontal' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'Divider' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'ModalHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:33  Warning: 'useEffect' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
74:11  Warning: 'setTimeout' is not defined.  no-undef
91:11  Warning: 'setTimeout' is not defined.  no-undef
95:11  Warning: 'console' is not defined.  no-undef
99:9  Warning: 'console' is not defined.  no-undef
105:56  Warning: 'React' is not defined.  no-undef
113:7  Warning: 'window' is not defined.  no-undef
256:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
444:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/profileInfo/verifyEmail/index.tsx
18:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
23:10  Warning: 'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
54:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/calender/index.tsx
8:11  Warning: 'CustomCalendarProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'activeDate' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
21:22  Warning: 'error' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:29  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
178:31  Warning: 'event' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/events/calenderEvents/index.tsx
2:17  Warning: 'useState' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/events/createEvent.tsx
2:10  Warning: 'Badge' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:17  Warning: 'FilePlus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:35  Warning: 'Plus' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
6:41  Warning: 'Trash' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:8  Warning: 'FileUploader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:27  Warning: 'HTMLDivElement' is not defined.  no-undef
17:9  Warning: 'Category' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
55:21  Warning: 'setTimeout' is not defined.  no-undef
60:20  Warning: 'clearTimeout' is not defined.  no-undef
167:9  Warning: 'console' is not defined.  no-undef
172:7  Warning: 'console' is not defined.  no-undef
205:32  Warning: 'React' is not defined.  no-undef
205:50  Warning: 'HTMLInputElement' is not defined.  no-undef
223:32  Warning: 'React' is not defined.  no-undef
223:50  Warning: 'HTMLInputElement' is not defined.  no-undef
452:45  Warning: 'document' is not defined.  no-undef
454:32  Warning: 'HTMLInputElement' is not defined.  no-undef
520:45  Warning: 'document' is not defined.  no-undef
522:32  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/editEvents/index.tsx
12:24  Warning: 'EventType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:10  Warning: 'isOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
24:18  Warning: 'setIsOpen' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
25:27  Warning: 'HTMLDivElement' is not defined.  no-undef
34:10  Warning: 'eventData' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
111:17  Warning: 'console' is not defined.  no-undef
129:17  Warning: 'console' is not defined.  no-undef
132:15  Warning: 'console' is not defined.  no-undef
136:11  Warning: 'console' is not defined.  no-undef
169:13  Warning: 'console' is not defined.  no-undef
172:11  Warning: 'console' is not defined.  no-undef
198:21  Warning: 'setTimeout' is not defined.  no-undef
203:20  Warning: 'clearTimeout' is not defined.  no-undef
207:34  Warning: 'React' is not defined.  no-undef
241:11  Warning: 'console' is not defined.  no-undef
245:11  Warning: 'console' is not defined.  no-undef
258:13  Warning: 'console' is not defined.  no-undef
263:11  Warning: 'console' is not defined.  no-undef
269:7  Warning: 'console' is not defined.  no-undef
357:9  Warning: 'handleInputChange' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
358:8  Warning: 'React' is not defined.  no-undef
358:26  Warning: 'HTMLInputElement' is not defined.  no-undef
358:45  Warning: 'HTMLTextAreaElement' is not defined.  no-undef
382:32  Warning: 'React' is not defined.  no-undef
382:50  Warning: 'HTMLInputElement' is not defined.  no-undef
400:32  Warning: 'React' is not defined.  no-undef
400:50  Warning: 'HTMLInputElement' is not defined.  no-undef
505:24  Warning: 'React' is not defined.  no-undef
615:45  Warning: 'document' is not defined.  no-undef
617:32  Warning: 'HTMLInputElement' is not defined.  no-undef
683:45  Warning: 'document' is not defined.  no-undef
685:32  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/eventsCard/index.tsx
31:16  Warning: 'NodeJS' is not defined.  no-undef
35:15  Warning: 'setTimeout' is not defined.  no-undef
41:18  Warning: 'clearTimeout' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/events/index.tsx
2:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/index.tsx
33:3  Warning: 'setToggleMain' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
41:27  Warning: 'HTMLDivElement' is not defined.  no-undef
54:10  Warning: 'activeTabs' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
79:22  Warning: 'React' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/otherServices/index.tsx
14:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/otherServices/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/otherServices/serviceDetails/index.tsx
8:3  Warning: 'AlertDialog' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:3  Warning: 'AlertDialogContent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'AlertDialogDescription' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'AlertDialogHeader' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'AlertDialogTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
106:18  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
347:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
392:15  Warning: 'onClose' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/otherServices/serviceDetails/ViewService.tsx
32:21  Warning: 'process' is not defined.  no-undef
49:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/otherServices/servicesCard/index.tsx
5:10  Warning: 'getServiceById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'activeColor' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
18:9  Warning: 'auth' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/posts/createPost.tsx
5:10  Warning: 'useRef' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
7:10  Warning: 'createUser' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'getStorage' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:38  Warning: 'File' is not defined.  no-undef
30:21  Warning: 'setTimeout' is not defined.  no-undef
36:20  Warning: 'clearTimeout' is not defined.  no-undef
47:7  Warning: 'alert' is not defined.  no-undef
87:9  Warning: 'console' is not defined.  no-undef
92:7  Warning: 'console' is not defined.  no-undef
96:37  Warning: 'React' is not defined.  no-undef
96:55  Warning: 'HTMLInputElement' is not defined.  no-undef
103:26  Warning: 'React' is not defined.  no-undef
103:46  Warning: 'HTMLInputElement' is not defined.  no-undef
115:25  Warning: 'React' is not defined.  no-undef
115:45  Warning: 'HTMLInputElement' is not defined.  no-undef
302:30  Warning: 'URL' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/posts/imageGrid.tsx
2:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:8  Warning: 'Link' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:10  Warning: 'category' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
19:21  Warning: 'process' is not defined.  no-undef
21:5  Warning: 'console' is not defined.  no-undef
21:17  Warning: 'process' is not defined.  no-undef
58:9  Warning: 'console' is not defined.  no-undef
82:9  Warning: 'console' is not defined.  no-undef
97:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/posts/index.tsx
67:11  Warning: 'setTimeout' is not defined.  no-undef
72:11  Warning: 'setTimeout' is not defined.  no-undef
80:9  Warning: 'console' is not defined.  no-undef
82:9  Warning: 'setTimeout' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/profile/followers/index.tsx
32:21  Warning: 'process' is not defined.  no-undef
51:7  Warning: 'setTimeout' is not defined.  no-undef
55:7  Warning: 'console' is not defined.  no-undef
96:7  Warning: 'console' is not defined.  no-undef
111:7  Warning: 'console' is not defined.  no-undef
131:19  Warning: 'setTimeout' is not defined.  no-undef
159:7  Warning: 'clearTimeout' is not defined.  no-undef
208:7  Warning: 'console' is not defined.  no-undef
370:13  Warning: 'console' is not defined.  no-undef
385:21  Warning: 'sessionStorage' is not defined.  no-undef
386:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/profile/following/index.tsx
32:21  Warning: 'process' is not defined.  no-undef
48:7  Warning: 'setTimeout' is not defined.  no-undef
52:7  Warning: 'console' is not defined.  no-undef
93:7  Warning: 'console' is not defined.  no-undef
107:7  Warning: 'console' is not defined.  no-undef
127:19  Warning: 'setTimeout' is not defined.  no-undef
155:7  Warning: 'clearTimeout' is not defined.  no-undef
368:13  Warning: 'console' is not defined.  no-undef
383:21  Warning: 'sessionStorage' is not defined.  no-undef
384:21  Warning: 'sessionStorage' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/profile/index.tsx
1:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
21:3  Warning: 'setSelectedProfileTabs' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
36:7  Warning: 'console' is not defined.  no-undef
47:7  Warning: 'console' is not defined.  no-undef
69:7  Warning: 'console' is not defined.  no-undef
90:18  Warning: 'React' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/createService.tsx
13:10  Warning: 'FormEvent' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:22  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:10  Warning: 'listPrice' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
33:10  Warning: 'hashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
33:20  Warning: 'setHashtags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:10  Warning: 'geoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
34:19  Warning: 'setGeoTags' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:10  Warning: 'media' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:17  Warning: 'setMedia' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
35:38  Warning: 'File' is not defined.  no-undef
49:21  Warning: 'setTimeout' is not defined.  no-undef
54:20  Warning: 'clearTimeout' is not defined.  no-undef
89:9  Warning: 'console' is not defined.  no-undef
94:7  Warning: 'console' is not defined.  no-undef
185:7  Warning: 'console' is not defined.  no-undef
187:7  Warning: 'alert' is not defined.  no-undef
219:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/index.tsx
16:38  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/services/serviceDetails/editCustomizations.tsx
69:3  Warning: 'console' is not defined.  no-undef
81:21  Warning: 'setTimeout' is not defined.  no-undef
85:20  Warning: 'clearTimeout' is not defined.  no-undef
94:9  Warning: 'console' is not defined.  no-undef
143:13  Warning: 'console' is not defined.  no-undef
165:13  Warning: 'console' is not defined.  no-undef
176:9  Warning: 'console' is not defined.  no-undef
237:7  Warning: 'alert' is not defined.  no-undef
255:9  Warning: 'alert' is not defined.  no-undef
259:7  Warning: 'console' is not defined.  no-undef
261:7  Warning: 'alert' is not defined.  no-undef
316:5  Warning: 'console' is not defined.  no-undef
332:9  Warning: 'alert' is not defined.  no-undef
335:7  Warning: 'console' is not defined.  no-undef
337:7  Warning: 'alert' is not defined.  no-undef
384:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/serviceDetails/editService.tsx
38:10  Warning: 'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
66:30  Warning: 'setTimeout' is not defined.  no-undef
107:9  Warning: 'console' is not defined.  no-undef
110:9  Warning: 'clearTimeout' is not defined.  no-undef
121:8  Warning: 'React' is not defined.  no-undef
121:26  Warning: 'HTMLInputElement' is not defined.  no-undef
121:45  Warning: 'HTMLTextAreaElement' is not defined.  no-undef
134:21  Warning: 'setTimeout' is not defined.  no-undef
138:20  Warning: 'clearTimeout' is not defined.  no-undef
158:9  Warning: 'console' is not defined.  no-undef
163:7  Warning: 'console' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/serviceDetails/fileUploader.tsx
2:8  Warning: 'Image' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:36  Warning: 'React' is not defined.  no-undef
13:54  Warning: 'HTMLInputElement' is not defined.  no-undef

./src/screens/profile/myProfile/tabs/services/serviceDetails/index.tsx
28:34  Warning: 'id' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
34:5  Warning: 'setTimeout' is not defined.  no-undef
57:7  Warning: 'console' is not defined.  no-undef
72:9  Warning: 'formatDurationToDays' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
106:9  Warning: 'alert' is not defined.  no-undef
109:7  Warning: 'alert' is not defined.  no-undef
110:7  Warning: 'console' is not defined.  no-undef
114:9  Warning: 'formatDescription' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/profile/myProfile/tabs/services/servicesCard/index.tsx
1:31  Warning: 'JSX' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
1:36  Warning: 'SVGProps' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:10  Warning: 'services' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
143:7  Warning: 'console' is not defined.  no-undef
175:9  Warning: 'console' is not defined.  no-undef
208:11  Warning: 'setTimeout' is not defined.  no-undef
213:11  Warning: 'console' is not defined.  no-undef
217:9  Warning: 'console' is not defined.  no-undef
221:9  Warning: 'setTimeout' is not defined.  no-undef

./src/screens/selectedCategory/events/eventsCardSC.tsx
33:7  Warning: 'console' is not defined.  no-undef
46:16  Warning: 'NodeJS' is not defined.  no-undef
50:15  Warning: 'setTimeout' is not defined.  no-undef
56:18  Warning: 'clearTimeout' is not defined.  no-undef

./src/screens/selectedCategory/events/index.tsx
7:28  Warning: 'HTMLDivElement' is not defined.  no-undef
9:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:27  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/selectedCategory/index.tsx
3:23  Warning: 'TabsList' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
3:33  Warning: 'TabsTrigger' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:10  Warning: 'ChevronDown' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
8:23  Warning: 'ChevronsDown' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:10  Warning: 'Item' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
60:27  Warning: 'HTMLDivElement' is not defined.  no-undef
63:9  Warning: 'handleScroll' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
70:5  Warning: 'console' is not defined.  no-undef
73:5  Warning: 'console' is not defined.  no-undef
75:38  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
82:9  Warning: 'console' is not defined.  no-undef
84:9  Warning: 'console' is not defined.  no-undef
114:20  Warning: 'React' is not defined.  no-undef

./src/screens/selectedCategory/posts/imageCard.tsx
7:10  Warning: 'useRouter' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
18:21  Warning: 'process' is not defined.  no-undef
63:9  Warning: 'console' is not defined.  no-undef

./src/screens/selectedCategory/posts/imageCardMobile.tsx
13:21  Warning: 'process' is not defined.  no-undef
38:7  Warning: 'console' is not defined.  no-undef

./src/screens/selectedCategory/posts/index.tsx
8:28  Warning: 'HTMLDivElement' is not defined.  no-undef
27:21  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/posts/postsCard.tsx
6:10  Warning: 'MediaRenderer' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:3  Warning: 'LimitType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
11:3  Warning: 'PublicationMetadataMainFocusType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
12:3  Warning: 'PublicationType' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
13:3  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
14:3  Warning: 'usePublicationsQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
17:3  Warning: 'getLensProfileDetails' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
42:9  Warning: 'isFetched' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:10  Warning: 'visiblePosts' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
45:24  Warning: 'setVisiblePosts' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:10  Warning: 'loadingMore' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
46:23  Warning: 'setLoadingMore' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:10  Warning: 'allPostsLoaded' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
47:26  Warning: 'setAllPostsLoaded' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
81:20  Warning: 'postCategory' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
132:7  Warning: 'console' is not defined.  no-undef
151:7  Warning: 'console' is not defined.  no-undef
165:7  Warning: 'console' is not defined.  no-undef
181:9  Warning: 'id' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
200:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
201:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
216:16  Warning: 'isLoadingPublications' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
218:12  Warning: 'publicationsError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
302:9  Warning: 'loadNextPage' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/profiles/index.tsx
8:28  Warning: 'HTMLDivElement' is not defined.  no-undef
10:9  Warning: 'postsRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
10:27  Warning: 'HTMLDivElement' is not defined.  no-undef
27:17  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/profiles/profilesCardSC.tsx
1:10  Warning: 'useCallback' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
2:23  Warning: 'getUserById' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
9:10  Warning: 'useProfilesQuery' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
27:9  Warning: 'isAuthLogin' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
37:10  Warning: 'followingList' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
96:7  Warning: 'console' is not defined.  no-undef
156:12  Warning: 'profileError' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
157:16  Warning: 'loadingProfile' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
205:34  Warning: 'setCurrentFollowingCursor' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:9  Warning: 'containerRef' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
209:31  Warning: 'HTMLDivElement' is not defined.  no-undef
211:32  Warning: 'otherUserID' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
218:7  Warning: 'console' is not defined.  no-undef
225:39  Warning: 'following_loading' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
277:15  Warning: 'themeName' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars

./src/screens/selectedCategory/services/index.tsx
7:28  Warning: 'HTMLDivElement' is not defined.  no-undef

./src/screens/selectedCategory/services/servicesCard.tsx
47:9  Warning: 'console' is not defined.  no-undef

./src/services/authBridgeService.ts
10:3  Warning: 'deleteDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
77:5  Warning: 'console' is not defined.  no-undef
96:5  Warning: 'console' is not defined.  no-undef
114:5  Warning: 'console' is not defined.  no-undef
137:7  Warning: 'console' is not defined.  no-undef
147:7  Error: Unreachable code.  no-unreachable
147:7  Warning: 'console' is not defined.  no-undef
150:5  Warning: 'console' is not defined.  no-undef
172:7  Warning: 'console' is not defined.  no-undef
184:5  Warning: 'console' is not defined.  no-undef
202:5  Warning: 'console' is not defined.  no-undef
234:5  Warning: 'console' is not defined.  no-undef
280:9  Warning: 'console' is not defined.  no-undef
285:5  Warning: 'console' is not defined.  no-undef
288:5  Warning: 'console' is not defined.  no-undef
306:5  Warning: 'console' is not defined.  no-undef
310:7  Warning: 'console' is not defined.  no-undef
324:7  Warning: 'console' is not defined.  no-undef
332:7  Warning: 'console' is not defined.  no-undef
341:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
353:5  Warning: 'console' is not defined.  no-undef
403:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
421:7  Warning: 'console' is not defined.  no-undef
428:7  Warning: 'console' is not defined.  no-undef
437:7  Warning: 'console' is not defined.  no-undef
443:5  Warning: 'console' is not defined.  no-undef
452:16  Warning: 'localStorage' is not defined.  no-undef
452:32  Warning: 'window' is not defined.  no-undef
462:5  Warning: 'console' is not defined.  no-undef
480:5  Warning: 'console' is not defined.  no-undef
488:16  Warning: 'localStorage' is not defined.  no-undef
488:32  Warning: 'window' is not defined.  no-undef
490:5  Warning: 'console' is not defined.  no-undef
494:5  Warning: 'console' is not defined.  no-undef
503:5  Warning: 'console' is not defined.  no-undef
536:5  Warning: 'console' is not defined.  no-undef

./src/services/chatService.ts
44:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
62:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
84:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
102:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
117:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/commentServices.ts
74:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
82:5  Warning: 'userId' is defined but never used. Allowed unused args must match /^_/u.  @typescript-eslint/no-unused-vars
112:7  Warning: 'console' is not defined.  no-undef
137:7  Warning: 'console' is not defined.  no-undef
214:7  Warning: 'console' is not defined.  no-undef

./src/services/currencyService.ts
38:28  Warning: 'localStorage' is not defined.  no-undef
48:16  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
60:5  Warning: 'localStorage' is not defined.  no-undef
63:5  Warning: 'console' is not defined.  no-undef
77:5  Warning: 'console' is not defined.  no-undef
91:12  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
111:7  Warning: 'localStorage' is not defined.  no-undef
116:5  Warning: 'console' is not defined.  no-undef
150:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/eventsServices.ts
5:3  Warning: 'addDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
15:3  Warning: 'getFirestore' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
86:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
112:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
220:5  Warning: 'console' is not defined.  no-undef
251:5  Warning: 'console' is not defined.  no-undef
325:5  Warning: 'console' is not defined.  no-undef
399:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
437:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/followServices.ts
47:9  Warning: 'console' is not defined.  no-undef
72:7  Warning: 'console' is not defined.  no-undef
80:9  Warning: 'console' is not defined.  no-undef
106:7  Warning: 'console' is not defined.  no-undef
122:7  Warning: 'console' is not defined.  no-undef
131:9  Warning: 'console' is not defined.  no-undef
148:7  Warning: 'console' is not defined.  no-undef
158:9  Warning: 'console' is not defined.  no-undef
197:7  Warning: 'console' is not defined.  no-undef

./src/services/lensService.ts
4:3  Warning: 'doc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
5:3  Warning: 'getDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
20:7  Warning: 'data' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
62:7  Warning: 'insertLensProfiles' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
78:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
108:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
122:7  Warning: 'console' is not defined.  no-undef
151:5  Warning: 'console' is not defined.  no-undef

./src/services/ordersServices.ts
65:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
89:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
115:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
142:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
161:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/postService.ts
7:3  Warning: 'addDoc' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
16:16  Warning: 'uuidv4' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
66:5  Warning: 'console' is not defined.  no-undef
84:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
153:5  Warning: 'console' is not defined.  no-undef
215:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
238:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
264:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
304:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
346:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
368:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
377:3  Warning: 'console' is not defined.  no-undef
401:3  Warning: 'console' is not defined.  no-undef
450:3  Warning: 'console' is not defined.  no-undef

./src/services/reportServices.ts
100:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
108:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
127:13  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
135:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/serviceService.ts
14:3  Warning: 'Timestamp' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
110:5  Warning: 'console' is not defined.  no-undef
129:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
154:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
204:5  Warning: 'console' is not defined.  no-undef
249:5  Warning: 'console' is not defined.  no-undef
343:5  Warning: 'console' is not defined.  no-undef
350:9  Warning: 'console' is not defined.  no-undef
358:9  Warning: 'console' is not defined.  no-undef
364:7  Warning: 'console' is not defined.  no-undef
376:9  Warning: 'console' is not defined.  no-undef
388:9  Warning: 'console' is not defined.  no-undef
402:13  Warning: 'console' is not defined.  no-undef
411:13  Warning: 'console' is not defined.  no-undef
418:11  Warning: 'console' is not defined.  no-undef
432:11  Warning: 'console' is not defined.  no-undef
453:7  Warning: 'console' is not defined.  no-undef
463:7  Warning: 'console' is not defined.  no-undef
489:7  Warning: 'console' is not defined.  no-undef
500:5  Warning: 'console' is not defined.  no-undef
521:5  Warning: 'console' is not defined.  no-undef
556:5  Warning: 'console' is not defined.  no-undef
572:5  Warning: 'console' is not defined.  no-undef
693:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
733:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
846:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
907:5  Warning: 'console' is not defined.  no-undef

./src/services/usersServices.ts
9:3  Warning: 'getFirestore' is defined but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
26:11  Warning: 'usersCollection' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
28:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
31:5  Warning: 'console' is not defined.  no-undef
53:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
71:5  Warning: 'console' is not defined.  no-undef
89:9  Warning: 'localStorage' is not defined.  no-undef
91:9  Warning: 'localStorage' is not defined.  no-undef
104:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
205:5  Warning: 'console' is not defined.  no-undef
217:11  Warning: 'usersCollection' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
235:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
261:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
290:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
300:7  Warning: 'console' is not defined.  no-undef
309:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
343:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
362:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
383:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
402:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
420:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
432:5  Warning: 'console' is not defined.  no-undef
439:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
444:7  Warning: 'POST_COLLECTION' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
445:7  Warning: 'SERVICES_COLLECTION' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
446:7  Warning: 'EVENT_COLLECTION' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
484:5  Warning: 'console' is not defined.  no-undef
513:11  Warning: 'resp' is assigned a value but never used. Allowed unused vars must match /^_/u.  @typescript-eslint/no-unused-vars
517:5  Warning: 'console' is not defined.  no-undef
521:5  Warning: 'console' is not defined.  no-undef
562:5  Warning: 'console' is not defined.  no-undef
577:5  Warning: 'console' is not defined.  no-undef
611:5  Warning: 'console' is not defined.  no-undef
645:5  Warning: 'console' is not defined.  no-undef
680:5  Warning: 'console' is not defined.  no-undef
707:5  Warning: 'console' is not defined.  no-undef
728:5  Warning: 'console' is not defined.  no-undef
767:5  Warning: 'console' is not defined.  no-undef
780:7  Warning: 'console' is not defined.  no-undef
790:5  Warning: 'console' is not defined.  no-undef
792:5  Warning: 'console' is not defined.  no-undef
813:9  Warning: 'console' is not defined.  no-undef
823:5  Warning: 'console' is not defined.  no-undef
857:5  Warning: 'console' is not defined.  no-undef
952:5  Warning: 'console' is not defined.  no-undef
969:5  Warning: 'console' is not defined.  no-undef
997:5  Warning: 'console' is not defined.  no-undef
1021:5  Warning: 'console' is not defined.  no-undef
1026:7  Warning: 'console' is not defined.  no-undef
1033:5  Warning: 'console' is not defined.  no-undef
1035:5  Warning: 'console' is not defined.  no-undef

./src/types/global.d.ts
10:29  Warning: 'Storage' is not defined.  no-undef
11:31  Warning: 'Storage' is not defined.  no-undef
12:25  Warning: 'Document' is not defined.  no-undef
14:26  Warning: 'Navigator' is not defined.  no-undef
24:18  Warning: 'React' is not defined.  no-undef
24:42  Warning: 'React' is not defined.  no-undef
24:62  Warning: 'SVGElement' is not defined.  no-undef

./src/utils/htmlSanitizer.ts
77:22  Warning: 'DOMParser' is not defined.  no-undef

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/building-your-application/configuring/eslint#disabling-rules
