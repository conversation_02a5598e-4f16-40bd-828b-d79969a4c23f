import { useState, useEffect } from 'react';

export interface CurrentUser {
  uid: string;
  email?: string;
  displayName?: string;
  emailVerified?: boolean;
}

/**
 * Hook to get the current authenticated user from localStorage
 * This works with Firebase Auth data stored by AuthProvider
 */
export const useCurrentUser = () => {
  const [user, setUser] = useState<CurrentUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getUserFromStorage = () => {
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const userData = JSON.parse(userStr);
          if (userData.uid) {
            setUser({
              uid: userData.uid,
              email: userData.email,
              displayName: userData.displayName,
              emailVerified: userData.emailVerified
            });
          } else {
            setUser(null);
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Error parsing user from localStorage:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Get user immediately
    getUserFromStorage();

    // Listen for storage changes (when user logs in/out in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user') {
        getUserFromStorage();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return {
    user,
    loading,
    isAuthenticated: !!user,
    userId: user?.uid || null
  };
};
