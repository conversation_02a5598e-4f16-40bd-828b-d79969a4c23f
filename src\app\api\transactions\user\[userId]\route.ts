import { NextRequest, NextResponse } from 'next/server';
import { getUserTransactions } from '@/services/transactionService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const result = await getUserTransactions(userId, limit);

    if (result.success) {
      return NextResponse.json({
        success: true,
        transactions: result.transactions,
        count: result.transactions?.length || 0,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error fetching user transactions:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch transactions',
    }, { status: 500 });
  }
}
