"use client";
import React from "react";

interface PostSkeletonLoaderProps {
  isScroll?: boolean;
  borderColor?: string;
  count?: number;
}

const PostSkeletonLoader: React.FC<PostSkeletonLoaderProps> = ({
  isScroll = false,
  borderColor = "#000000",
  count = 5,
}) => {
  // Create an array of the specified count
  const skeletonItems = Array.from({ length: count }, (_, index) => index);

  return (
    <div className="w-full">
      {isScroll ? (
        // Skeleton for scroll view with 2 rows
        <div className="mt-0 flex flex-col  overflow-clip">
          {/* First row */}
          <div className="flex flex-row gap-x-[2px]">
            {skeletonItems.map((index) => (
              <div key={`row1-${index}`} className="cursor-pointer">
                <div
                  className="min-h-[85px] min-w-[85px] max-h-[85px] max-w-[85px] border-2 aspect-square relative overflow-hidden"
                  style={{ borderColor }}
                >
                  <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        // Skeleton for regular view
        <div className="mt-0 flex flex-row flex-grow overflow-hidden">
          {skeletonItems.map((index) => (
            <div key={index} className="cursor-pointer row">
              <div className="">
                <div
                  className="min-h-[85px] min-w-[85px] max-h-[85px] max-w-[85px] border-2 aspect-square relative overflow-hidden"
                  style={{ borderColor }}
                >
                  <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent skeleton-animation"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PostSkeletonLoader;
