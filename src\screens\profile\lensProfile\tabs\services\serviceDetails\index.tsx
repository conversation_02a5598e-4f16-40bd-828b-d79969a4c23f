"use client";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { ChevronLeft, Info, Loader } from "react-feather";
import EditService from "./editService";
import { formatDuration, getServiceById } from "@/services/serviceService";
import { getCurrencySymbol, initializeCurrency } from "@/services/currencyService";
import ServiceDescription from "@/globalComponents/formatText";
import RichTextFormatter from "@/components/RichTextFormatter";
const ServiceDetails = ({ id, onSelectServiceDetails }: any) => {
  const [isTrue, setIsTrue] = useState(false);
  const [services, setServices] = useState<any>(null);
  const [currencySymbol, setCurrencySymbol] = useState("£"); // Default to GBP symbol
  const [isLoadingService, setIsLoadingService] = useState(true);
  const [isLoadingCustomizations, setIsLoadingCustomizations] = useState(true);

  const handleSelectService = ({ id }: any) => {
    setIsTrue(false);
  };

  useEffect(() => {
    const fetchAllServices = async () => {
      setIsLoadingService(true);
      setIsLoadingCustomizations(true);

      try {
        const response = await getServiceById(id);
        if (response.success) {
          setServices(response.service);

          // Get the currency symbol from the service data or default
          const servicesCurrency = initializeCurrency();
          setCurrencySymbol(getCurrencySymbol(servicesCurrency));
        }
      } catch (error) {
        console.error("Error fetching service:", error);
      } finally {
        // Add a small delay to ensure loading state is visible
        setTimeout(() => {
          setIsLoadingService(false);
          setIsLoadingCustomizations(false);
        }, 500);
      }
    };

    fetchAllServices();
  }, [id]);

  return (
    <>
      <div
        className={
          isTrue
            ? "grid grid-cols-2 max-md:grid-cols-1 bg-white gap-4"
            : "grid grid-cols-1 bg-white"
        }
      >
        <div className={isTrue ? "max-md:hidden" : ""}>
          {/* header */}
          <div className=" w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div
                onClick={() => !isTrue && onSelectServiceDetails()}
                className=" cursor-pointer row"
                aria-disabled={true}
              >
                <ChevronLeft />
                <p className="pl-[2px]">Back</p>
              </div>
              <p className="text-titleLabel text-lg font-bold">Service Details</p>
              <p className=" opacity-0">save</p>
            </div>
          </div>
          <div
            className={
              isTrue
                ? "flex flex-row w-full overflow-y-scroll gap-3 hide-scroll-custom  bg-white h-[calc(100vh-300px)] pt-4 pb-16"
                : "flex flex-row w-full overflow-scroll gap-3 hide-scroll bg-white h-full pt-4"
            }
          >
            <div className="w-full">
              {isLoadingService ? (
                <div className="flex flex-col items-center justify-center h-[60vh]">
                  <div className="bg-blue-50 rounded-full p-4 mb-4">
                    <Loader size={48} className="text-primary animate-spin" />
                  </div>
                  <h2 className="text-xl font-bold mb-2">Loading Service Details...</h2>
                  <p className="text-gray-500 text-center">
                    Please wait while we load your service information.
                  </p>
                </div>
              ) : (
                <>
                  <p className="text-subtitle">Сategory</p>
                  <p className="text-primary font-[600]">{services?.category}</p>
                  <p className="my-3 text-primary text-lg max-md:text-base font-bold">
                    {services?.title}
                  </p>
                  <div className="row justify-between">
                    <p className="text-titleLabel">
                      {formatDuration(services?.duration, {
                        dayLabel: "day",
                        hourLabel: "hour",
                      })}
                    </p>
                    <p className="text-titleLabel">
                      {currencySymbol}
                      {services?.price ? (services?.price / (1 - 0.16)).toFixed(2) : "0.00"}
                    </p>
                  </div>
                  <div className="mt-3">
                    <RichTextFormatter
                      text={services?.description || ""}
                      className="text-subtitle break-words"
                      preserveWhitespace={true}
                      enableMarkdown={true}
                    />
                  </div>
                  <p className="my-3 text-primary text-xl font-bold">Customization</p>

                  {isLoadingCustomizations ? (
                    <div className="flex flex-col justify-center items-center py-8">
                      <div className="bg-blue-50 rounded-full p-3 mb-2">
                        <Loader size={24} className="text-primary animate-spin" />
                      </div>
                      <p className="text-gray-500 text-sm">Loading customizations...</p>
                    </div>
                  ) : services?.customizations_array &&
                    services?.customizations_array.length > 0 ? (
                    <>
                      <div className="grid grid-cols-2">
                        <div>
                          <p className="text-primary font-bold">Option</p>
                        </div>
                        <div className="justify-end row">
                          <div className="row gap-4">
                            <p className="text-primary font-bold w-16 text-center">Time</p>
                            <p className="text-primary font-bold w-16 text-center">Cost</p>
                            <p className="opacity-0 w-14 text-center">hi</p>
                          </div>
                        </div>
                      </div>
                      {services?.customizations_array.map((item: any, indexs: number) => (
                        <div className="grid grid-cols-2 mt-2" key={indexs}>
                          <div>
                            <p className="text-subtitle text-sm mr-10">{item?.title}</p>
                          </div>
                          <div className="justify-end row">
                            <div className="row gap-4">
                              <p className="text-subtitle w-16 text-center">
                                +
                                {formatDuration(item?.duration, {
                                  dayLabel: "d ",
                                  hourLabel: "h",
                                })}
                              </p>
                              <p className="text-subtitle w-16 text-center">
                                {currencySymbol}
                                {item?.price}
                              </p>
                              <Info className="text-borderColor w-14 text-center" size={21} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </>
                  ) : (
                    <p className="text-gray-500 text-center py-4">
                      No customizations available for this service.
                    </p>
                  )}
                </>
              )}

              <div className="row justify-center mt-5">
                <Badge
                  className=" btn-xs w-60 py-4 border-primary btn"
                  variant="outline"
                  onClick={() => setIsTrue(true)}
                >
                  Edit
                </Badge>
              </div>
              <div className="row justify-center">
                <Badge className=" btn-xs w-60 py-4 border-primary btn mt-3" variant="outline">
                  Delete
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Edit Service */}
        {isTrue && <EditService onSelectService={handleSelectService} />}
      </div>
    </>
  );
};

export default ServiceDetails;
