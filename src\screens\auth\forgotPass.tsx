"use client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

const ForgotPass = () => {
  const [email, setEmail] = useState("");
  const [toggle, setToggle] = useState(false);
  return (
    <>
      {!toggle ? (
        <div>
          <p className="text-center text-primary font-[600] text-2xl mb-3">
            Forgot password?
          </p>
          <p className="text-center text-borderColor">
            To recover your password, enter the email that was used for
            registration
          </p>
          <div className="grid w-full md:max-w-sm items-center gap-1.5 mt-6 max-md:text-start">
            <Label
              htmlFor="email"
              className="text-titleLabel font-bold text-lg"
            >
              Email
            </Label>
            <Input
              type="email"
              id="email"
              placeholder="Email"
              className="text-primary"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <Button
            className=" rounded-full w-full border-primary btn border-[2px]  text-primary py-2 hover:bg-primary hover:text-white mt-4"
            style={{ paddingTop: "10px" }}
            variant="outline"
            onClick={() => setToggle(true)}
            disabled={email.length > 4 ? false : true}
          >
            Continue
          </Button>
        </div>
      ) : (
        <div>
          <p className="text-center text-borderColor">
            A password reset message has been sent to your email
          </p>

          <Button
            className=" rounded-full w-full border-primary btn border-[2px]  text-primary py-2 hover:bg-primary hover:text-white mt-12"
            style={{ paddingTop: "10px" }}
            variant="outline"
          >
            Ok
          </Button>
        </div>
      )}
    </>
  );
};

export default ForgotPass;
