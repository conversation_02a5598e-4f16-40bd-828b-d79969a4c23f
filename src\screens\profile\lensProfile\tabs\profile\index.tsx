import { TabsContent } from "@/components/ui/tabs";
import Followers from "./followers";
import Following from "./following";
import * as Tabs from "@radix-ui/react-tabs";
import { useEffect, useState } from "react";
import ProfileCardSkeleton from "@/components/CardSkeleton/ProfileCardSkeleton";

type ProfileMyProfileProps = {
  activeBorderColor?: string; // New prop for dynamic active border color
  otherUserID?: any;
  isOtherProfileStatus: boolean;
  follower: number;
  following: number;
  selectedProfileTabs: any;
};

export function ProfileMyProfile({
  activeBorderColor = "#333333",
  otherUserID,
  isOtherProfileStatus,
  follower,
  following,
  selectedProfileTabs,
}: ProfileMyProfileProps) {
  const [selectedTabs, setSelectedTabs] = useState(selectedProfileTabs);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setSelectedTabs(selectedProfileTabs ? selectedProfileTabs : "Following");

    // Reset loading state immediately when tab changes
    setLoading(false);

    // Then set it to true for a very short time to trigger the skeleton loader
    setTimeout(() => {
      setLoading(true);

      // And quickly set it back to false
      setTimeout(() => {
        setLoading(false);
      }, 100);
    }, 0);

    return () => {
      // Ensure loading is false when component unmounts
      setLoading(false);
    };
  }, [selectedProfileTabs]);

  // Initial loading effect only on first mount
  useEffect(() => {
    // Set loading to true for a very short time
    setLoading(true);

    // Then quickly set it back to false
    const timer = setTimeout(() => {
      setLoading(false);
    }, 200);

    return () => {
      clearTimeout(timer);
      // Ensure loading is false when component unmounts
      setLoading(false);
    };
  }, []);

  return (
    <div className="mt-3">
      <Tabs.Root defaultValue="Following" value={selectedTabs} className="w-full bg-white ">
        <Tabs.List
          className="TabsList pb-0  max-md:pb-1 sticky top-10 bg-white -mt-5 scroll-pt-2 max-md:-mt-7"
          aria-label="Manage your account"
          style={
            {
              "--active-border-color": activeBorderColor,
            } as React.CSSProperties
          } // Set CSS variable
        >
          <Tabs.Trigger
            className="TabsTrigger min-h-[70px] max-md:min-h-[50px]"
            value="Following"
            onClick={() => {
              setSelectedTabs("Following");
            }}
            style={
              {
                paddingTop: "10px",
              } as React.CSSProperties
            }
          >
            <div className="text-center pb-3 ">
              <p className="small-heading">{following}</p>
              <p className="small-subheading ">Following</p>
            </div>
          </Tabs.Trigger>
          <Tabs.Trigger
            className="TabsTrigger min-h-[70px] max-md:min-h-[50px]"
            value="Followers"
            onClick={() => {
              setSelectedTabs("Followers");
            }}
            style={
              {
                paddingTop: "10px",
              } as React.CSSProperties
            }
          >
            <div className="text-center pb-3 ">
              <p className="small-heading">{follower}</p>
              <p className="small-subheading ">Followers</p>
            </div>
          </Tabs.Trigger>
        </Tabs.List>
        <TabsContent value="Followers" className="bg-red-400">
          <div className="w-full overflow-scroll hide-scroll bg-white h-full max-md:mt-7">
            {loading ? (
              <div className="p-4">
                <ProfileCardSkeleton count={6} columns={2} showGrid={true} />
              </div>
            ) : (
              <Followers otherUserID={otherUserID} />
            )}
          </div>
        </TabsContent>
        <TabsContent value="Following">
          <div className="max-md:mt-7">
            {loading ? (
              <div className="p-4">
                <ProfileCardSkeleton count={6} columns={2} showGrid={true} />
              </div>
            ) : (
              <Following otherUserID={otherUserID} />
            )}
          </div>
        </TabsContent>
      </Tabs.Root>
    </div>
  );
}
