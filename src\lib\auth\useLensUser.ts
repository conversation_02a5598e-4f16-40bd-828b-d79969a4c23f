import { useQuery } from "@tanstack/react-query";
import { readAccessToken } from "./auth-helpers";
import {
  PageSize,
  useAccountsAvailableQuery,
  useAccountStatsQuery,
  useFullAccountQuery,
} from "@/graphql/test/generated";
import { useAccount } from "wagmi";

export default function useLensUser() {
  const { address } = useAccount();

  const localStorageQuery = useQuery(["lens-user", address], () => readAccessToken());

  const { data: availAccountData } = useAccountsAvailableQuery(
    {
      accountsAvailableRequest: {
        managedBy: address,
        includeOwned: true,
        pageSize: PageSize.Fifty,
      },
      lastLoggedInAccountRequest: {
        address,
      },
    },
    {
      enabled: !!address,
      refetchOnWindowFocus: false,
    }
  );

  const profileQuery = useFullAccountQuery(
    {
      accountRequest: {
        address: address,
      },
      accountStatsRequest: {
        account: address,
      },
    },
    {
      enabled: !!address,
      refetchOnWindowFocus: false,
    }
  );

  return {
    isSignedInQuery: localStorageQuery,
    profileQuery: profileQuery,
    availAccountData,
  };
}
