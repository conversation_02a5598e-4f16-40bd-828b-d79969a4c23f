"use client";
import { useEffect, useState } from "react";
import NotionRenderer from "@/components/notion/NotionRenderer";

export default function AboutNotion() {
  const [blocks, setBlocks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlocks = async () => {
      try {
        setLoading(true);
        const response = await fetch("/api/notion/about");
        if (!response.ok) {
          throw new Error("Failed to fetch blocks");
        }
        const data = await response.json();
        setBlocks(data.blocks);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchBlocks();
  }, []);

  if (loading) {
    return (
      <div className="prose mx-auto p-6">
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="prose mx-auto p-6">
        <p className="text-red-500">Error loading content: {error}</p>
      </div>
    );
  }

  return (
    <div className="">
      <NotionRenderer blocks={blocks} />
    </div>
  );
}
