"use client";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  SheetTit<PERSON>,
} from "@/components/ui/sheet";

import { useState } from "react";
import { X, Check, Plus } from "react-feather";
import { useRouter } from "next/navigation";

const Category = [
  "My Feed",
  "Music",
  "Literature",
  "Art",
  "Theatre & Performance",
  "Film & Photography",
  "Multidisciplinary",
  "Groups",
];

const Dateofpublishing = ["Past 24 hours ", "Past week", "Past month"];
const ProfileName = ["Tom Li", "Kim Li 2"];
const Postlocation = ["United Kingdom", "United States", "United States 2"];

export function Filter({ open, onOpenChange }: any) {
  const router = useRouter();
  const [activeSheet, setActiveSheet] = useState(1);

  const showSheet = (sheetNumber: number) => {
    setActiveSheet(sheetNumber);
  };

  //   for Category
  const [SelectedCategory, setSelectedCategory] = useState(Number);

  const handleItemClickCategory = (index: any) => {
    setSelectedCategory(index); // Set the clicked item as the selected one
  };

  //   for Date Of
  const [SelectedProfileName, setSelectedProfileName] = useState(null);

  const handleItemClickProfileName = (index: any) => {
    setSelectedProfileName(index); // Set the clicked item as the selected one
  };

  //   for Date Of
  const [SelectedDate, setSelectedDate] = useState(null);

  const handleItemClickDate = (index: any) => {
    setSelectedDate(index); // Set the clicked item as the selected one
  };
  //   for postLocation
  const [selectedPostLocation, setSelectedPostLocation] = useState(null);

  const handleItemClickPostLocation = (index: any) => {
    setSelectedPostLocation(index); // Set the clicked item as the selected one
  };

  const handlelog = () => {
    onOpenChange(false);
    router.push(`/selectedCategory/${Category[SelectedCategory]}`);
  };
  return (
    <div>
      <Sheet open={open} onOpenChange={onOpenChange} key="right">
        <SheetContent className="w-[22rem] max-md:w-full p-0" style={{}} side="right">
          {activeSheet === 1 && (
            <SheetHeader>
              <SheetTitle>
                <div
                  className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 "
                  style={{ zIndex: 9999999999999999 }}
                >
                  <div className=" cursor-pointer" onClick={() => onOpenChange(false)}>
                    <X />
                  </div>

                  <p className="text-lg font-bold text-titleLabel">Filter Posts</p>
                  <p className="text-lg text-borderColor font-bold">Clear</p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  <div className="filter-list" onClick={() => showSheet(2)}>
                    <p className="text-titleLabel">Category</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">Any</p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                  <div className="filter-list mt-4" onClick={() => showSheet(3)}>
                    <p className="text-titleLabel">Date of publishing</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">Any</p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                  <div className="filter-list mt-4" onClick={() => showSheet(4)}>
                    <p className="text-titleLabel">Post location</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">Any</p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                  <div className="filter-list mt-4" onClick={() => showSheet(5)}>
                    <p className="text-titleLabel">Profile name</p>

                    <div className="row gap-3">
                      <p className="text-borderColor">Any</p>
                      <img src="/assets/right-arrow.svg" alt="" />
                    </div>
                  </div>
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 2 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Category</p>
                  <p
                    className="text-base text-primary font-bold cursor-pointer"
                    onClick={() => handlelog()}
                  >
                    Done
                  </p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  {Category.map((item, index) => {
                    return (
                      <div
                        key={index}
                        className="filter-list mt-4"
                        onClick={() => handleItemClickCategory(index)}
                      >
                        <p className="text-titleLabel">{item}</p>

                        <div className="row gap-3 text-w">
                          {SelectedCategory === index && (
                            <Check color="#25282B" strokeWidth="1.6px" />
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 3 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Date of publishing</p>
                  <p className="text-base text-primary font-bold">Done</p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  {Dateofpublishing.map((item, index) => {
                    return (
                      <div
                        className="filter-list mt-4"
                        key={index}
                        onClick={() => handleItemClickDate(index)}
                      >
                        <p className="text-titleLabel">{item}</p>

                        <div className="row gap-3 text-w">
                          {SelectedDate === index && <Check color="#25282B" strokeWidth="1.6px" />}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 4 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Post location</p>
                  <p className="text-base text-primary font-bold">Done</p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  {Postlocation.map((item, index) => {
                    return (
                      <div
                        className="filter-list mt-4"
                        onClick={() => handleItemClickProfileName(index)}
                      >
                        <p className="text-titleLabel">{item}</p>

                        <div className="row gap-3 text-w">
                          {SelectedProfileName === index && (
                            <Check color="#25282B" strokeWidth="1.6px" />
                          )}
                        </div>
                      </div>
                    );
                  })}
                  <div className="row gap-3 border-b-[1px] pb-4 border-borderColor mt-4 px-4 ">
                    <Plus color="#4F4F4F" strokeWidth="2px" />

                    <p className="text-titleLabel">Add country/region</p>
                  </div>
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
          {activeSheet === 5 && (
            <SheetHeader>
              <SheetTitle>
                <div className="row justify-between mb-6 pt-5 border-b-[1px] z-50 bg-white absolute right-0 w-full px-4 pb-3 ">
                  <div
                    className=" cursor-pointer text-base font-normal row gap-2"
                    onClick={() => showSheet(1)}
                  >
                    <img src="/assets/left-arrow.svg" alt="" />
                    Back
                  </div>

                  <p className="text-base font-bold text-titleLabel">Profile Name</p>
                  <p className="text-base text-primary font-bold">Done</p>
                </div>
              </SheetTitle>
              <SheetDescription>
                <div className="mt-[4.4rem]">
                  {ProfileName.map((item, index) => (
                    <div
                      key={index}
                      className="filter-list mt-4"
                      onClick={() => handleItemClickPostLocation(index)} // Set the selected index on click
                      style={{ cursor: "pointer" }}
                    >
                      <p className="text-titleLabel">{item}</p>
                      <div className="row gap-3 text-w">
                        {/* Only display the check icon if the item is selected */}
                        {selectedPostLocation === index && (
                          <Check color="#25282B" strokeWidth="1.6px" />
                        )}
                      </div>
                    </div>
                  ))}

                  <div className="row gap-3 border-b-[1px] pb-4 border-borderColor mt-4 px-4 ">
                    <Plus color="#4F4F4F" strokeWidth="2px" />

                    <p className="text-titleLabel">Add Profile Name</p>
                  </div>
                </div>
              </SheetDescription>
            </SheetHeader>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}
