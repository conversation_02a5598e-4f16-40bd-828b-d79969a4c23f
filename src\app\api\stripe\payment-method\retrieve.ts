import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripePaymentMethodRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { customerId, isUS }: StripePaymentMethodRequest = req.body;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const paymentMethod = await stripeService.customers.listPaymentMethods(
      customerId, 
      { type: 'card' }
    );

    res.status(200).json({
      payment_method: paymentMethod,
      success: true,
    });

  } catch (error) {
    console.error('Error retrieving payment methods:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
