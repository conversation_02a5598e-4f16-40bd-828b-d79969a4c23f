/* Styles for remote content from Firebase Remote Config */

.remoteContent {
  font-family: 'SF UI Text', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* Headings */
.remoteContent h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem;
  color: #000;
}

.remoteContent h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.2rem 0 0.8rem;
  color: #000;
}

.remoteContent h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 1rem 0 0.6rem;
  color: #000;
}

/* Paragraphs */
.remoteContent p {
  margin-bottom: 1rem;
  font-size: 1rem;
}

/* Lists */
.remoteContent ul, 
.remoteContent ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.remoteContent li {
  margin-bottom: 0.5rem;
}

.remoteContent ul li {
  list-style-type: disc;
}

.remoteContent ol li {
  list-style-type: decimal;
}

/* Links */
.remoteContent a {
  color: #0066cc;
  text-decoration: none;
  transition: color 0.2s ease, text-decoration 0.2s ease;
}

.remoteContent a:hover {
  color: #004499;
  text-decoration: underline;
}

.remoteContent a:visited {
  color: #551A8B;
}

/* Blockquotes */
.remoteContent blockquote {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #555;
  font-style: italic;
}

/* Tables */
.remoteContent table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.remoteContent th, 
.remoteContent td {
  border: 1px solid #e0e0e0;
  padding: 0.5rem;
  text-align: left;
}

.remoteContent th {
  background-color: #f5f5f5;
  font-weight: 600;
}

/* Code blocks */
.remoteContent pre, 
.remoteContent code {
  background-color: #f5f5f5;
  border-radius: 3px;
  font-family: monospace;
}

.remoteContent pre {
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.remoteContent code {
  padding: 0.2rem 0.4rem;
}

/* Images */
.remoteContent img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 4px;
}

/* Horizontal rule */
.remoteContent hr {
  border: 0;
  height: 1px;
  background-color: #e0e0e0;
  margin: 2rem 0;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .remoteContent h1 {
    font-size: 1.6rem;
  }
  
  .remoteContent h2 {
    font-size: 1.4rem;
  }
  
  .remoteContent h3 {
    font-size: 1.2rem;
  }
  
  .remoteContent p,
  .remoteContent li {
    font-size: 0.95rem;
  }
}
