"use client";
import {
  Check,
  DollarSign,
  Edit2,
  Facebook,
  Globe,
  Instagram,
  Loader,
  MoreHorizontal,
  Twitter,
  X,
  Youtube,
} from "react-feather";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>dalHeader } from "@heroui/react";
import { useState, useCallback, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { updateUser } from "@/services/usersServices";
import useAuth from "@/hook";
import { Button } from "@/components/ui/button";

const getSocialLink = (handle: string | undefined, baseUrl: string) => {
  if (!handle) return "#";
  return handle.startsWith("http") ? handle : `${baseUrl}${handle.replace("@", "")}`;
};
const ProfileInfoSocialMedia = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const [facebook, setFacebook] = useState(props?.facebookLink || "");
  const [instagram, setInstagram] = useState(props?.instagramLink || "");
  const [youtube, setYouTube] = useState(props?.youtubeLink || "");
  const [webSite, setWebSite] = useState(props?.websiteLink || "");
  const [twitter, setTwitter] = useState(props?.twitterLink || "");
  const [pendingLink, setPendingLink] = useState("");
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");

  // fetch data from firebase
  const auth = useAuth();

  // No longer using useEffect to handle success state
  // Now handling it directly in the handleSubmit function

  const handleSubmit = async () => {
    if (facebook || instagram || youtube || webSite || twitter) {
      setUploadStatus("uploading");

      try {
        const updatedData = {
          facebookLink: facebook,
          instagramLink: instagram,
          youtubeLink: youtube,
          websiteLink: webSite,
          twitterLink: twitter,
        };
        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setUploadStatus("success");

          // Set a timeout to close the modal and refresh the data
          setTimeout(() => {
            // Trigger parent component to refresh data
            props.onClickAction(true);

            // Force close the modal
            setIsOpen(false);

            // Reset states
            setFacebook("");
            setInstagram("");
            setYouTube("");
            setWebSite("");
            setTwitter("");
            setUploadStatus("idle");
          }, 1500);

          // Force close the modal after a longer delay if it's still open
          setTimeout(() => {
            setIsOpen(false);
          }, 2000);
        } else {
          console.error("Error updating Social Media Links:", response.error);
          setUploadStatus("idle");
        }
      } catch (error) {
        console.error("Error updating Social Media Links:", error);
        setUploadStatus("idle");
      }
    }
  };

  const handleLinkClick = useCallback((url: string, e: React.MouseEvent) => {
    e.preventDefault();
    setPendingLink(url);
    setIsConfirm(true);
  }, []);

  const confirmNavigation = useCallback(() => {
    if (pendingLink) {
      window.open(pendingLink, "_blank");
      setIsConfirm(false);
      setPendingLink("");
    }
  }, [pendingLink]);

  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">
                Links to Social Media Accounts
              </span>
            </div>
            {!props.isOtherProfile && (
              <div
                onClick={() => {
                  setIsOpen(true),
                    setFacebook(props?.facebookLink || ""),
                    setInstagram(props?.instagramLink || ""),
                    setYouTube(props?.youtubeLink || ""),
                    setWebSite(props?.websiteLink || ""),
                    setTwitter(props?.twitterLink || "");
                }}
                className=" cursor-pointer"
              >
                <Edit2 className="max-md:h-[20px]" color={props.bgColor} />
              </div>
            )}
          </div>
          <div className="mt-4 max-md:mt-2 flex flex-row gap-3">
            {props.facebookLink && (
              <Link
                href={getSocialLink(props.facebookLink, "https://www.facebook.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
                onClick={(e) =>
                  handleLinkClick(getSocialLink(props.facebookLink, "https://www.facebook.com/"), e)
                }
              >
                <Facebook size={24} className="fill-primary border-0 outline-0" />
              </Link>
            )}
            {props.instagramLink && (
              <Link
                href={getSocialLink(props.instagramLink, "https://www.instagram.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
                onClick={(e) =>
                  handleLinkClick(
                    getSocialLink(props.instagramLink, "https://www.instagram.com/"),
                    e
                  )
                }
              >
                <Instagram size={28} className="border-0 outline-0" />
              </Link>
            )}
            {props.youtubeLink && (
              <Link
                href={getSocialLink(props.youtubeLink, "https://www.youtube.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
                onClick={(e) =>
                  handleLinkClick(getSocialLink(props.youtubeLink, "https://www.youtube.com/"), e)
                }
              >
                <Youtube size={28} className="border-0 outline-0" />
              </Link>
            )}
            {props.websiteLink && (
              <Link
                href={props.websiteLink}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
                onClick={(e) => handleLinkClick(props.websiteLink, e)}
              >
                <Globe size={28} className="border-0 outline-0" />
              </Link>
            )}
            {props.twitterLink && (
              <Link
                href={getSocialLink(props.twitterLink, "https://twitter.com/")}
                target="_blank"
                className="bg-[#EEEEEE] p-3 rounded-full w-fit"
                onClick={(e) =>
                  handleLinkClick(getSocialLink(props.twitterLink, "https://twitter.com/"), e)
                }
              >
                <Twitter size={28} className="border-0 outline-0 fill-primary" />
              </Link>
            )}
          </div>
        </div>
      </div>
      {/* PersonalMotto Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={(open) => {
            // Always allow closing the modal
            setIsOpen(open);
          }}
          hideCloseButton={true}
          scrollBehavior="inside"
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {uploadStatus === "success" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Social Media Links Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your social media links have been updated and the changes will be visible on
                        your profile.
                      </p>
                    </div>
                  ) : uploadStatus === "uploading" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Updating Your Social Media Links...
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your information. This may take a moment.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div onClick={() => setIsOpen(false)} className="cursor-pointer">
                          <X />
                        </div>
                        <p className="font-bold text-primary">Links To Social Media </p>
                        <div
                          className={`flex items-center ${
                            facebook || instagram || youtube || webSite || twitter
                              ? "text-primary cursor-pointer"
                              : "text-borderColor cursor-not-allowed"
                          }`}
                          onClick={
                            facebook || instagram || youtube || webSite || twitter
                              ? handleSubmit
                              : undefined
                          }
                        >
                          Save
                        </div>
                      </div>

                      <div>
                        <div className="grid w-full items-center gap-1.5 mt-6">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Link to Facebook Account
                          </Label>
                          <Input
                            type="text"
                            placeholder="Add link"
                            className={`h-[40px] ${
                              uploadStatus !== "idle" ? "opacity-70 cursor-not-allowed" : ""
                            }`}
                            value={facebook}
                            onChange={(e) => setFacebook(e.target.value)}
                            disabled={uploadStatus !== "idle"}
                          />
                        </div>
                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Link to Instagram Account
                          </Label>
                          <Input
                            type="text"
                            placeholder="Add link"
                            className={`h-[40px] ${
                              uploadStatus !== "idle" ? "opacity-70 cursor-not-allowed" : ""
                            }`}
                            value={instagram}
                            onChange={(e) => setInstagram(e.target.value)}
                            disabled={uploadStatus !== "idle"}
                          />
                        </div>
                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Link to Twitter Account
                          </Label>
                          <Input
                            type="text"
                            placeholder="Add link"
                            className={`h-[40px] ${
                              uploadStatus !== "idle" ? "opacity-70 cursor-not-allowed" : ""
                            }`}
                            value={twitter}
                            onChange={(e) => setTwitter(e.target.value)}
                            disabled={uploadStatus !== "idle"}
                          />
                        </div>
                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Link to YouTube
                          </Label>
                          <Input
                            type="text"
                            placeholder="Add link"
                            className={`h-[40px] ${
                              uploadStatus !== "idle" ? "opacity-70 cursor-not-allowed" : ""
                            }`}
                            value={youtube}
                            onChange={(e) => setYouTube(e.target.value)}
                            disabled={uploadStatus !== "idle"}
                          />
                        </div>
                        <div className="grid w-full items-center gap-1.5 mt-3">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            Link to Your Website
                          </Label>
                          <Input
                            type="text"
                            placeholder="Add link"
                            className={`h-[40px] ${
                              uploadStatus !== "idle" ? "opacity-70 cursor-not-allowed" : ""
                            }`}
                            value={webSite}
                            onChange={(e) => setWebSite(e.target.value)}
                            disabled={uploadStatus !== "idle"}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      <div>
        <Modal
          isDismissable={false}
          isOpen={isConfirm}
          placement="auto"
          onOpenChange={setIsConfirm}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div>
                    <p className="text-center text-black text-lg">
                      Are you sure you want to visit this external link?
                    </p>
                    <div>
                      <Button
                        variant="outline"
                        className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                        onClick={confirmNavigation}
                      >
                        Yes, continue
                      </Button>
                      <Button
                        variant="outline"
                        className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                        onClick={() => setIsConfirm(false)}
                      >
                        No, cancel
                      </Button>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfoSocialMedia;
