import React from "react";

interface ProfileCardSkeletonProps {
  count?: number;
  columns?: number;
  showGrid?: boolean;
}

const ProfileCardSkeleton: React.FC<ProfileCardSkeletonProps> = ({
  count = 4,
  columns = 2,
  showGrid = true,
}) => {
  // Generate the appropriate grid class based on columns
  const getGridClass = () => {
    if (!showGrid) return "";

    switch (columns) {
      case 1:
        return "grid grid-cols-1 gap-3";
      case 2:
        return "grid grid-cols-2 max-md:grid-cols-1 gap-3";
      case 3:
        return "grid grid-cols-3 max-md:grid-cols-1 gap-3";
      case 4:
        return "grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3";
      default:
        return "grid grid-cols-2 max-md:grid-cols-1 gap-3";
    }
  };

  return (
    <div className={getGridClass()}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`animate-pulse ${!showGrid ? "mb-3" : ""}`}>
          <div className="row justify-between mt-3">
            <div className="row gap-2">
              {/* Avatar skeleton */}
              <div className="w-[40px] h-[40px] rounded-full bg-gray-200"></div>
              <div>
                {/* Profile name skeleton */}
                <div className="h-5 bg-gray-200 rounded-md w-24 mb-1"></div>
                {/* Location skeleton */}
                <div className="h-4 bg-gray-200 rounded-md w-20"></div>
              </div>
            </div>
            {/* Follow button skeleton */}
            <div className="h-8 bg-gray-200 rounded-md w-20"></div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProfileCardSkeleton;
