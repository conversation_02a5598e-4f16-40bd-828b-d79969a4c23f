import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeUpdatePaymentIntentRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      paymentId,
      orderId,
      description,
      receipt_email,
      isUS
    }: StripeUpdatePaymentIntentRequest = req.body;

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const paymentIntent = await stripeService.paymentIntents.update(paymentId, {
      metadata: { order_id: orderId },
      description,
      receipt_email
    });

    res.status(200).json({
      paymentIntent,
      success: true,
    });

  } catch (error) {
    console.error('Error updating payment intent:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
