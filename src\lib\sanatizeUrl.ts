import { IPFS_GATEWAY } from "./constant";

// const sanitizeDStorageUrl = (hash?: string): string => {
//   if (!hash) {
//     return "";
//   }
//   hash = hash?.split("/ipfs/")?.[1];

//   const ipfsGateway = `${IPFS_GATEWAY}/`;

//   let link = hash?.replace(/^Qm[1-9A-Za-z]{44}/gm, `${IPFS_GATEWAY}/${hash}`);
//   link = link?.replace("https://ipfs.io/ipfs/", ipfsGateway);
//   link = link?.replace("ipfs://ipfs/", ipfsGateway);
//   link = link?.replace("ipfs://", ipfsGateway);
// //   link = link.replace("lens://", `${STORAGE_NODE_URL}/`);
//   link = link?.replace("ar://", "https://gateway.arweave.net/");

//   return link;
// };

// export default sanitizeDStorageUrl;

// Handles full URLs or raw IPFS/Arweave hashes
const sanitizeDStorageUrl = (inputUrl?: string): string => {
  if (!inputUrl) return "";

  const ipfsGateway = `${IPFS_GATEWAY}/`;

  // If it's just a raw IPFS hash (Qm... or bafy...)
//   const isRawIpfsHash = /^Qm[1-9A-Za-z]{44}$/.test(inputUrl) || /^bafy[1-9A-Za-z]{50,}$/.test(inputUrl);
//   if (isRawIpfsHash) {
//     return `${ipfsGateway}${inputUrl}`;
//   }

  // If it's a URL, normalize known patterns
  let url = inputUrl;
  url = url.replace("https://ipfs.lens.xyz/ipfs/",ipfsGateway);
  url = url.replace("https://ipfs.io/ipfs/", ipfsGateway);
  url = url.replace("https://gateway.pinata.cloud/ipfs/", ipfsGateway);
  url = url.replace("https://cloudflare-ipfs.com/ipfs/", ipfsGateway);
  url = url.replace("ipfs://ipfs/", ipfsGateway);
  url = url.replace("ipfs://", ipfsGateway);

  url = url.replace("ar://", "https://gateway.arweave.net/");

  return url;
};

export default sanitizeDStorageUrl;
