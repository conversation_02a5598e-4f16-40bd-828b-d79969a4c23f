import { useCallback, useEffect, useRef, useState } from "react";
import { getAllUsers, getUserById } from "@/services/usersServices";
import { themes } from "../../../../theme";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import LoadingOverlay from "@/components/loadingOverlay";
import { getLensProfilesById } from "@/services/lensService";
import { useProfilesQuery } from "@/graphql/generated";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import { sortProfiles } from "@/lib/helper";
import { FollowerManager } from "@/services/followServices";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  PageSize,
  useAccountsBulkQuery,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { getId } from "@/services/authBridgeService";

const ProfileCardSC = (props: any) => {
  const user = useAuth();
  // console.log({ user });

  const isAuthLogin = user.isLogin;
  // console.log(isAuthLogin);

  const { profileData } = useProfile(user?.userId || "");
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [profilesData, setProfilesData] = useState<AccountsBulkQuery["accountsBulk"]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const [followingList, setFollowingList] = useState<any[]>([]);

  const categories = [
    "Music",
    "Literature",
    "Art",
    "Film & Photography",
    "Theatre & Performance",
    "Multidisciplinary",
    "Groups",
    "Storytelling",
  ];

  // Fetch users from API
  const fetchAllUsers = async () => {
    try {
      setLoading(true);
      const response = await getAllUsers();
      if (Array.isArray(response?.users) && response.users.length > 0) {
        const categorizedData: Record<string, any[]> = {};
        let myFeed: any[] = [];

        for (const category of categories) {
          const filteredPosts = response.users.filter((post: any) => {
            const userCategory = post?.categories?.[0];
            // Skip users without a profile name
            if (!post.profile_name) return false;

            return category === "Literature"
              ? userCategory === category || userCategory === "Storytelling"
              : userCategory === category;
          });

          categorizedData[category] = filteredPosts;
          // console.log({ user })
        }

        // console.log(isAuthLogin);
        // console.log(user.userId);
        if (user.isLogin) {
          const resp1 = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);

          setFollowingList(resp1.map((e) => e.id));
          // console.log({ resp1 });

          myFeed = resp1;
        }

        if (user.isLogin) {
          categorizedData["My Feed"] = myFeed;
        }
        // console.log({ categorizedData });

        setCategoryData(categorizedData);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllUsers();
  }, [user.userId]);

  // Fetch Lens profiles by category
  const [profiles, setProfiles] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  const [profilesmy, setProfilesMy] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  // const [profilesData, setProfilesData] = useState<any>(null);
  const category = props.themeProperties.title.toLowerCase();
  const profilesRef = useRef<
    Array<{
      localName: string;
    }>
  >([]);

  useEffect(() => {
    const fetchLensProfilesByCategory = async () => {
      const resp = await getLensProfilesById(category);
      const lensProfiles: Array<{
        localName: string;
      }> = resp?.lens_ids?.map((curr: any) => {
        return {
          localName: curr,
        };
      });

      if (JSON.stringify(profilesRef.current) !== JSON.stringify(lensProfiles)) {
        profilesRef.current = lensProfiles;
        setProfiles(lensProfiles);
      }
    };

    fetchLensProfilesByCategory();
  }, [category]);

  // const { data: profileDataByLensId } = useProfilesQuery({
  //   request: { where: { handles: profiles } },
  // });
  const {
    data: profileDataByLensId,
    error: profileError,
    isLoading: loadingProfile,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames: props.themeProperties.title == "My Feed" ? profilesmy : profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: (props.themeProperties.title == "My Feed" ? profilesmy.length > 0 : profiles.length > 0),
    }
  );

  useEffect(() => {
    if (profileDataByLensId) {
      setProfilesData(profileDataByLensId?.accountsBulk);
    }
  }, [profileDataByLensId]);

  useEffect(() => {
    if (profilesData) {
      // console.log({ profilesData });
    }
  }, [profilesData]);

  const mergedProfiles = sortProfiles(
    [
      ...(categoryData[props.themeProperties.title] || []).map((item) => ({
        ...item,
        profile_name: item.profile_name || "Profile Name*",
      })),
      ...profilesData.map((profile) => ({
        id: profile.username?.localName,
        profile_name: profile.metadata?.name || "Profile Name*",
        avatar: profile?.metadata?.picture || "",
        location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
        isFollow: profile?.operations?.isFollowedByMe,
        lensProfile: true, // Mark lens profiles
      })),
    ],
    "profile_name"
  );

  // my feed

  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [userId, setUserId] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);

  const getLensUserId = async (otherUserID: any) => {
    try {
      const resp = await getId({ id: user.userId });
      if (resp) {
        setUserId(resp?.lens_code);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  useEffect(() => {
    getLensUserId(user.userId);
  }, [user.userId]);
  const { data: following, isLoading: following_loading } = useFollowingQuery(
    {
      request: {
        account: userId, // address
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  interface FollowingItem {
    localName: any;
  }

  const [allFollowing, setAllFollowing] = useState<FollowingItem[]>([]);

  useEffect(() => {
    if (
      following?.following?.items &&
      following?.following?.items.length > 0 &&
      props.themeProperties.title == "My Feed"
    ) {
      const newArray: FollowingItem[] = following.following.items.map((item: any) => ({
        localName: item.following.username?.localName,
      }));

      if (props.themeProperties.title == "My Feed") {
        setAllFollowing((prev) => [...prev, ...newArray]);
      }

      setProfiles((prev) => [...prev, ...newArray]);
    }
  }, [following]);

  useEffect(() => {
    if (allFollowing) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);
  return (
    <>
      {loading ? (
        <LoadingOverlay isLoading={loading} />
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([themeName, themeProperties], indexM) => (
            <div key={indexM}>
              {props.themeProperties.title === themeProperties.title && (
                <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
                  {mergedProfiles.length > 0 ? (
                    mergedProfiles.map((post, index) => (
                      <div key={index} className="mt-0">
                        {themeProperties.title === "My Feed" && !post.lensProfile ? (
                          Object.entries(themes).map(([_, innerThemeProperties], idx) => (
                            <div key={idx}>
                              {innerThemeProperties.title ===
                                (post?.categories[0] === "Storytelling"
                                  ? "Literature"
                                  : post?.categories[0]) && (
                                <GlobalProfileCard
                                  themeProperties={innerThemeProperties}
                                  isFollow={!profileData?.followers.includes(post.id)}
                                  location={post.location}
                                  profile_name={post.profile_name}
                                  avatar={post.avatar}
                                  id={post.id}
                                />
                                // <p>{post.profile_name}hii</p>
                              )}
                            </div>
                          ))
                        ) : themeProperties.title === "My Feed" && post.lensProfile ? (
                          <div>
                            <GlobalProfileCardLens
                              themeProperties={themeProperties}
                              isFollow={post.isFollow}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          </div>
                        ) : post.lensProfile ? (
                          <GlobalProfileCardLens
                            themeProperties={themeProperties}
                            isFollow={post.isFollow}
                            location={post.location}
                            profile_name={post.profile_name}
                            avatar={post.avatar}
                            id={post.id}
                          />
                        ) : (
                          // <p>{post.profile_name} lens</p>
                          <GlobalProfileCard
                            themeProperties={themeProperties}
                            isFollow={!profileData?.followers.includes(post.id)}
                            location={post.location}
                            profile_name={post.profile_name}
                            avatar={post.avatar}
                            id={post.id}
                          />
                          // <p>{post.profile_name}hiii</p>
                        )}
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500">No Profile available in this category.</p>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ProfileCardSC;
