import { Eip712TypedDataDomain } from "@/graphql/generated";
import { ThirdwebSDK } from "@thirdweb-dev/sdk";
// import { EIP712Domain } from "@thirdweb-dev/sdk/dist/declarations/src/evm/common/sign";
import { ethers } from "ethers";
import omitDeep from "omit-deep";

// 1. Sign typed data with omitted __typename values using omit-deep
export function omitTypename(object: any) {
  return omitDeep(object, ["__typename"]);
}

export async function signTypedDataWithOmmittedTypename(
  sdk: ThirdwebSDK,
  domain: Eip712TypedDataDomain,
  types: Record<string, any>,
  value: Record<string, any>
) {
  // Perform the signing using the SDK
  return await sdk.wallet.signTypedData(
    omitTypename(domain) as Eip712TypedDataDomain,
    omitTypename(types) as Record<string, any>,
    omitTypename(value) as Record<string, any>
  );
}

// 2. Split the signature to extract the "v", "r", and "s" values
export function splitSignature(signature: string) {
  return ethers.utils.splitSignature(signature);
}

export const sortProfiles = (profiles: any[], sortKey: string, order: "asc" | "desc" = "asc") => {
  return profiles.sort((a, b) => {
    const valA = a[sortKey] || 0;
    const valB = b[sortKey] || 0;

    // Numeric sorting (for timestamps or other numbers)
    if (typeof valA === "number" && typeof valB === "number") {
      return order === "asc" ? valA - valB : valB - valA;
    }

    // Date string sorting
    if (!isNaN(Date.parse(valA)) && !isNaN(Date.parse(valB))) {
      return order === "asc"
        ? new Date(valA).getTime() - new Date(valB).getTime()
        : new Date(valB).getTime() - new Date(valA).getTime();
    }

    // String sorting
    return order === "asc"
      ? valA.toString().localeCompare(valB.toString())
      : valB.toString().localeCompare(valA.toString());
  });
};
