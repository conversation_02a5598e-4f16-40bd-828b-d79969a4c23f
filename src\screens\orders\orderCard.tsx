"use client";
import * as React from "react";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { OrderCardDetail } from "./orderDetail/orderCardDetail";
import { useState } from "react";

const data = [
  {
    status: "New",
  },
  {
    status: "Completed",
  },
  {
    status: "New",
  },
  {
    status: "Completed",
  },
];

const OrderCard = () => {
  const [isSheetOpen, setSheetOpen] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<number | null>(null); // To store the selected user ID

  const handleUserClick = (id: any) => {
    setSelectedCardId(id);
    setSheetOpen(true);
  };
  return (
    <>
    {isSheetOpen && (
      <OrderCardDetail
        open={isSheetOpen}
        onOpenChange={setSheetOpen}
        cardId={selectedCardId}
      />)}
      <div className=" overflow-y-scroll hide-scroll h-[100vh] pb-32">
        {data.map((item, index) => {
          return (
            <Card
              className={
                selectedCardId == index && isSheetOpen
                  ? "w-full mt-3 bg-[#F2F2F2] rounded-md border-borderColor"
                  : "w-full mt-3 rounded-md border-borderColor"
              }
              onClick={() => handleUserClick(index)}
            >
              <CardHeader>
                <CardTitle className="row justify-between">
                  <div className="font-normal text-subtitle text-base">
                    Status{" "}
                    <span className="text-primary font-semibold uppercase">
                      {item.status}
                    </span>{" "}
                  </div>
                  <div className="text-subtitle font-normal">#4798348</div>
                </CardTitle>
                <CardDescription>
                  <div className="row gap-3 my-2">
                    <img
                      src="https://github.com/shadcn.png"
                      alt=""
                      className="w-10 h-10 rounded-full"
                    />
                    <p className="text-base">John Doe</p>
                  </div>
                  <div>
                    <p className="text-base font-bold text-primary my-2">
                      LIVE MUSIC PERFORMANCE
                    </p>
                  </div>
                  <div className="row justify-between">
                    <p className="text-subtitle">Total cost</p>
                    <p className="text-lg font-bold text-primary">$104.00 </p>
                  </div>
                  <div className="row justify-between">
                    <p className="text-subtitle">Due date</p>
                    <p className="text-lg text-primary">14.07.2020</p>
                  </div>
                </CardDescription>
              </CardHeader>
            </Card>
          );
        })}
      </div>
    </>
  );
};

export default OrderCard;
