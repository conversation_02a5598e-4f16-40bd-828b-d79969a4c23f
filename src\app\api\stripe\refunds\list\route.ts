import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const { chargeId, paymentIntentId, limit = 10, isUS = false } = await request.json();

    if (!chargeId && !paymentIntentId) {
      return NextResponse.json(
        {
          success: false,
          error: "Either charge ID or payment intent ID is required",
        },
        { status: 400 }
      );
    }

    console.log("📋 ===== LISTING STRIPE REFUNDS =====");
    console.log(`🔗 Charge ID: ${chargeId}`);
    console.log(`💳 Payment Intent ID: ${paymentIntentId}`);
    console.log(`📊 Limit: ${limit}`);
    console.log(`🌍 Using US Stripe: ${isUS}`);
    console.log("🕐 Timestamp:", new Date().toISOString());

    let targetChargeId = chargeId;

    // If only payment intent provided, get the charge ID
    if (!chargeId && paymentIntentId) {
      console.log("🔄 Getting charge from payment intent...");
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.latest_charge) {
        targetChargeId = paymentIntent.latest_charge as string;
        console.log(`✅ Found charge: ${targetChargeId}`);
      } else {
        return NextResponse.json(
          {
            success: false,
            error: "No charge found for this payment intent",
            paymentIntentId,
          },
          { status: 400 }
        );
      }
    }

    // Get charge details
    console.log("🔄 Retrieving charge details...");
    const charge = await stripe.charges.retrieve(targetChargeId);

    // List refunds for this charge
    console.log("🔄 Listing refunds...");
    const refunds = await stripe.refunds.list({
      charge: targetChargeId,
      limit: limit,
    });

    console.log(`✅ Found ${refunds.data.length} refunds`);

    // Calculate totals
    const totalRefunded = refunds.data.reduce((sum, refund) => sum + refund.amount, 0);
    const remainingAmount = charge.amount - charge.amount_refunded;

    const responseData = {
      success: true,
      message: `Found ${refunds.data.length} refunds`,
      charge: {
        id: charge.id,
        originalAmount: charge.amount,
        originalAmountFormatted: `$${(charge.amount / 100).toFixed(2)}`,
        totalRefunded: charge.amount_refunded,
        totalRefundedFormatted: `$${(charge.amount_refunded / 100).toFixed(2)}`,
        remainingAmount: remainingAmount,
        remainingAmountFormatted: `$${(remainingAmount / 100).toFixed(2)}`,
        fullyRefunded: charge.refunded,
        status: charge.status,
        currency: charge.currency,
      },
      refunds: {
        count: refunds.data.length,
        hasMore: refunds.has_more,
        data: refunds.data.map((refund) => ({
          id: refund.id,
          amount: refund.amount,
          amountFormatted: `$${(refund.amount / 100).toFixed(2)}`,
          currency: refund.currency,
          status: refund.status,
          reason: refund.reason,
          created: refund.created,
          createdFormatted: new Date(refund.created * 1000).toISOString(),
          metadata: refund.metadata,
          failureReason: refund.failure_reason,
          receiptNumber: refund.receipt_number,
        })),
      },
      summary: {
        totalRefunds: refunds.data.length,
        totalRefundedAmount: totalRefunded,
        totalRefundedAmountFormatted: `$${(totalRefunded / 100).toFixed(2)}`,
        averageRefundAmount: refunds.data.length > 0 ? totalRefunded / refunds.data.length : 0,
        averageRefundAmountFormatted:
          refunds.data.length > 0
            ? `$${(totalRefunded / refunds.data.length / 100).toFixed(2)}`
            : "$0.00",
        refundReasons: refunds.data.reduce((acc: any, refund) => {
          acc[refund.reason || "unknown"] = (acc[refund.reason || "unknown"] || 0) + 1;
          return acc;
        }, {}),
        refundStatuses: refunds.data.reduce((acc: any, refund) => {
          const status = refund.status || "unknown";
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {}),
      },
      paymentIntent: paymentIntentId
        ? {
            id: paymentIntentId,
            linkedToCharge: Boolean(charge.payment_intent),
          }
        : null,
      isUSStripeUsed: Boolean(isUS === "true"),
      timestamp: new Date().toISOString(),
    };

    console.log("✅ ===== REFUND LIST COMPLETED =====");
    console.log(`📊 Total Refunds: ${refunds.data.length}`);
    console.log(`💰 Total Refunded: $${(totalRefunded / 100).toFixed(2)}`);
    console.log(`💵 Remaining: $${(remainingAmount / 100).toFixed(2)}`);
    console.log(`🔗 Charge ID: ${targetChargeId}`);
    console.log("🔚 ===== END REFUND LIST =====");

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("❌ Error listing refunds:", error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        {
          success: false,
          error: "Stripe API error",
          details: error.message,
          type: error.type,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Failed to list refunds",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// GET method for convenience
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chargeId = searchParams.get("charge_id");
    const paymentIntentId = searchParams.get("payment_intent_id");
    const limit = searchParams.get("limit");
    const isUS = searchParams.get("isUS") === "true";

    if (!chargeId && !paymentIntentId) {
      return NextResponse.json(
        {
          success: false,
          error: "Either charge ID or payment intent ID is required",
        },
        { status: 400 }
      );
    }

    // Convert to POST request format
    const postRequest = new NextRequest(request.url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        chargeId,
        paymentIntentId,
        limit: limit ? parseInt(limit) : 10,
        isUS,
      }),
    });

    return POST(postRequest);
  } catch (error) {
    console.error("❌ Error in GET refunds list:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to process refunds list request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
