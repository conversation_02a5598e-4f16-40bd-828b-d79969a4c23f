"use client";
import { getAllPosts } from "@/services/postService";
import { Play } from "lucide-react";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

const PostComp = (props: any) => {
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const isFetched = useRef<boolean>(false);

  useEffect(() => {
    const fetchAllPosts = async () => {
      try {
        setLoading(true);

        // Fetch data from API
        const response = await getAllPosts();

        const categorizedData: Record<string, any[]> = {};
        setCategoryData(categorizedData);
      } catch (error) {
        console.error("Error fetching posts:", error);
      } finally {
        setLoading(false);
      }
    };

    // Fetch data only if not already fetched
    if (!isFetched.current) {
      isFetched.current = true;
      fetchAllPosts();
    }
  }, [isFetched]); // Empty dependency array ensures it runs only once

  return (
    <>
      {loading ? (
        <div className="loader">Loading...</div> // Add your loader component here
      ) : (
        <div className="w-full mt-0">
          <div className="grid grid-cols-6 gap-[1px] mb-[1px] relative">
            <div className="w-full relative col-span-4">
              <Link href="/browse" className="">
                <img
                  src="/assets/img3.svg"
                  alt=""
                  className="  h-full w-full object-cover border-2 max-h-[116px]"
                  style={{
                    borderColor: props.themeProperties.backgroundColor,
                  }}
                />
                <span className="absolute top-1 right-1 text-white row gap-1">
                  <img src="/assets/lens.png" alt="" className="h-5 w-7" />
                  {/* <Play /> */}
                </span>
              </Link>
            </div>
            <div className="w-full relative col-span-2">
              <img
                src="/assets/img3.svg"
                alt=""
                className=" w-full border-2 max-h-[116px] object-cover"
                style={{
                  borderColor: props.themeProperties.backgroundColor,
                }}
              />
              <span className="absolute top-1 right-1 text-white row gap-1">
                {false && (
                  <img src="/assets/lens.png" alt="" className="h-5 w-7" />
                )}

                <Play />
              </span>
            </div>
          </div>
          <div className="grid grid-cols-6 gap-[1px]">
            <div className=" col-span-2 h-full">
              <img
                src="/assets/img2.svg"
                alt=""
                className="  w-full border-2 object-cover h-1/2 mb-[1px]"
                style={{
                  borderColor: props.themeProperties.backgroundColor,
                }}
              />
              <img
                src="/assets/img4.svg"
                alt=""
                className="w-full border-2 object-cover h-1/2"
                style={{
                  borderColor: props.themeProperties.backgroundColor,
                }}
              />
            </div>
            <img
              src="/assets/img3.svg"
              alt=""
              className=" col-span-4  w-full h-full object-cover border-2 "
              style={{
                borderColor: props.themeProperties.backgroundColor,
              }}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default PostComp;
