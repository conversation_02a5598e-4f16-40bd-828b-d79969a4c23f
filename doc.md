# CHAT


## 🛠️ Initialize Chat

```ts
  const initChat = async () => {
        await ChatManager.getInstance().CreateChatIfNotExists({
        fromProfile: auth.userId,  // your user id 
        fromProfileName: auth.userData?.profile_name , // you name
        toProfile:// to profile id , 
        toProfileName:// to_user_name, 
        })
        

        const stop = await ChatManager.getInstance().GetMessagesBetweenUsers({
          userA: // user id of the person you are chatting with,
          userB: auth.userId, // your user_id
          onUpdate: (messages: Messages[]) => {
            console.log("Live messages", messages);

            //✅ here update the messaegs to render in frontedn
            
            if (messages?.length) {
              ChatManager.getInstance().UpdateSeenFlag({
                chatId: messages?.[0]?.chatId,
                currentUserId: auth?.userId,
                msgs: messages,
              });
            }

          },
        });


        // Later when cleaning up:
        stop();
    }
```

## 📤 Send Message

```ts

const resp = await ChatManager.getInstance().SendMessage({
        senderId: auth.userId, // your user id 
        senderName: auth.userData?.profile_name ?? "", // your profile name 

        recipientId: // receiver user id
        recipientName: // receiver user name
        message: "apple mango grapes", // message
});
```



## 📊 Get chat summary

```ts
const resp = await ChatManager.getInstance().GetUserChatSummaries({user_id:auth.userId});

export type ChatSummaryDetails =  { 
  data:ChatSummary[] ;  // here is the chats data , more you can see `ChatSummary` type in `chatsServices.ts`
  totalUnreadCount:number; // this is to show total count
}
```

- right now i only send latest 20 chats per chat (can be modefied later)