'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider
} from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

export default function CustomCheckout({ 
  userId,
  userEmail,
  amount = 2000,
  currency = 'usd',
  productName = 'Demo Product',
  productDescription = '',
  isEscrow = false,
  sellerId = null,
  sellerEmail = null,
  sellerStripeAccountId = null,
  orderId = null,
  onSuccess = null,
  onError = null
}) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [clientSecret, setClientSecret] = useState(null)

  const fetchClientSecret = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Validate required fields
      if (!userId || !userEmail) {
        throw new Error('User ID and email are required')
      }

      if (isEscrow && (!sellerId || !sellerEmail || !orderId)) {
        throw new Error('Escrow payments require seller information and order ID')
      }

      const response = await fetch('/api/checkout/embedded', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userEmail,
          amount,
          currency,
          productName,
          productDescription,
          isEscrow,
          sellerId,
          sellerEmail,
          sellerStripeAccountId,
          orderId
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      setClientSecret(data.clientSecret)
      return data.clientSecret
    } catch (err) {
      console.error('Error creating checkout session:', err)
      setError(err.message)
      if (onError) onError(err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [userId, userEmail, amount, currency, productName, productDescription, isEscrow, sellerId, sellerEmail, sellerStripeAccountId, orderId, onError])

  useEffect(() => {
    fetchClientSecret()
  }, [fetchClientSecret])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Setting up secure checkout...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50 p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="mb-4">
            <svg
              className="mx-auto h-16 w-16 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-red-600 mb-2">Checkout Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => fetchClientSecret()}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (!clientSecret) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to initialize checkout</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-900 text-white">
            <h1 className="text-2xl font-bold">
              {isEscrow ? 'Secure Escrow Payment' : 'Secure Checkout'}
            </h1>
            <p className="text-gray-300 mt-1">
              {isEscrow 
                ? 'Your payment will be held securely until order completion'
                : 'Complete your purchase securely'
              }
            </p>
          </div>
          
          <div className="p-6">
            {/* Order Summary */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Order Summary</h3>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{productName}</p>
                  {productDescription && (
                    <p className="text-sm text-gray-600">{productDescription}</p>
                  )}
                  {isEscrow && (
                    <p className="text-sm text-blue-600 mt-1">
                      ✓ Protected by escrow service
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <p className="font-bold text-lg">
                    ${(amount / 100).toFixed(2)} {currency.toUpperCase()}
                  </p>
                </div>
              </div>
            </div>

            <div id="checkout" className="w-full">
              <EmbeddedCheckoutProvider
                stripe={stripePromise}
                options={{ 
                  clientSecret,
                  onComplete: onSuccess 
                }}
              >
                <EmbeddedCheckout />
              </EmbeddedCheckoutProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
