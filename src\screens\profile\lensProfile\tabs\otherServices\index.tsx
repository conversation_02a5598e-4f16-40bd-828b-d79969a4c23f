"use client";
import OtherServicesCard from "./servicesCard";
import OtherServiceDetails from "./serviceDetails";
import { useState } from "react";
const OtherServicesMProfile = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState(null);

  const handleSelectService = (id: any) => {
    setSelectedServiceId(id);
    setIsOpen(true);
  };

  const handleSelectServiceDetail = (id: any) => {
    setSelectedServiceId(null);
    setIsOpen(false);
  };
  return (
    <>
      <div>
        <div className="">
          <div></div>
          {isOpen ? (
            <OtherServiceDetails
              id={selectedServiceId}
              onSelectServiceDetails={handleSelectServiceDetail}
            />
          ) : (
            <OtherServicesCard
              onSelectService={handleSelectService}
              activeColor={props.activeColor}
              isOtherProfile={props.isOtherProfile}
              otherUserID={props.otherUserID}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default OtherServicesMProfile;
