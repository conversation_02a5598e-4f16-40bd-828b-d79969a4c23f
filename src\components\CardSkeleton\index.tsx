import React from "react";

interface CardSkeletonProps {
  count?: number;
  columns?: number;
  showGrid?: boolean;
}

const CardSkeleton: React.FC<CardSkeletonProps> = ({
  count = 3,
  columns = 1,
  showGrid = true,
}) => {
  // Generate the appropriate grid class based on columns
  const getGridClass = () => {
    if (!showGrid) return "";

    switch (columns) {
      case 1:
        return "grid grid-cols-1 gap-3";
      case 2:
        return "grid grid-cols-2 max-md:grid-cols-1 gap-3";
      case 3:
        return "grid grid-cols-3 max-md:grid-cols-1 gap-3";
      case 4:
        return "grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3";
      default:
        return "grid grid-cols-1 max-md:grid-cols-1 gap-3";
    }
  };

  return (
    <div className={getGridClass()}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`animate-pulse ${!showGrid ? "mb-3" : ""}`}>
          <div
            className="w-full bg-white rounded-md border-l-[10px] border-gray-200 shadow-sm p-4 min-h-[150px]"
            style={{
              borderLeftColor: "#E5E5E5",
              borderTop: "1px solid #E5E5E5",
              borderRight: "1px solid #E5E5E5",
              borderBottom: "1px solid #E5E5E5",
            }}
          >
            {/* Title skeleton */}
            <div className="h-6 bg-gray-200 rounded-md w-3/4 mb-3"></div>

            {/* Description skeleton - multiple lines */}
            <div className="space-y-2 mb-4">
              <div className="h-4 bg-gray-200 rounded-md w-full"></div>
              <div className="h-4 bg-gray-200 rounded-md w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded-md w-4/6"></div>
            </div>

            {/* Price and duration row */}
            <div className="flex justify-between items-center mt-4">
              <div className="h-5 bg-gray-200 rounded-md w-1/4"></div>
              <div className="h-5 bg-gray-200 rounded-md w-1/5"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CardSkeleton;
