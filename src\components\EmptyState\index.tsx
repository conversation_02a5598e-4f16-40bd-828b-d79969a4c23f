import React from "react";
import { Calendar, Image as ImageIcon, ShoppingBag, Plus, Camera, User } from "react-feather";
import { themes } from "../../../theme";
import { Badge } from "@/components/ui/badge";

type EmptyStateProps = {
  type: "posts" | "services" | "events" | "profiles" | "followers" | "following" | "customizations";
  title: string;
  message: string;
  actionLabel?: string;
  onAction?: () => void;
  isOwnProfile?: boolean;
  icon?: React.ReactNode;
  customIcon?: string;
  category?: string;
};

const EmptyState: React.FC<EmptyStateProps> = ({
  type,
  title,
  message,
  actionLabel,
  onAction,
  isOwnProfile = true,
  icon,
  customIcon,
  category,
}) => {
  // Get theme color based on category
  const getThemeColor = () => {
    if (!category) return "#333333"; // Default color

    // Handle special case for "Storytelling"
    const categoryName = category === "Storytelling" ? "Literature" : category;

    // Find the theme that matches the category
    const theme = Object.values(themes).find((t) => t.title === categoryName);
    return theme?.backgroundColor || "#333333";
  };

  const themeColor = getThemeColor();
  // Default icons based on type
  const getDefaultIcon = () => {
    if (icon) return icon;

    if (customIcon) {
      return (
        <div className="w-32 h-32 mb-6 opacity-80 transform transition-transform hover:scale-105 duration-300">
          <img
            src={customIcon}
            alt={`Empty ${type}`}
            className="w-full h-full"
            onError={(e) => {
              e.currentTarget.src =
                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQwIDEwQzMwLjU3MTQgMTAgMjMgMTcuNTcxNCAyMyAyN0MyMyAzNi40Mjg2IDMwLjU3MTQgNDQgNDAgNDRDNDkuNDI4NiA0NCA1NyAzNi40Mjg2IDU3IDI3QzU3IDE3LjU3MTQgNDkuNDI4NiAxMCA0MCAxMFoiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIi8+CjxwYXRoIGQ9Ik02Ni42NjY3IDcwQzY2LjY2NjcgNTcuNDI4NiA1NC43NjE5IDQ3LjE0MjkgNDAgNDcuMTQyOUMyNS4yMzgxIDQ3LjE0MjkgMTMuMzMzMyA1Ny40Mjg2IDEzLjMzMzMgNzAiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTY2LjY2NjcgNzBINzMuMzMzM00xMy4zMzMzIDcwSDYuNjY2NjciIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+Cg==";
            }}
          />
        </div>
      );
    }

    switch (type) {
      case "posts":
        return (
          <div className="relative w-32 h-32 mb-6">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <Camera
                size={48}
                style={{ color: category ? themeColor : "#818cf8" }}
                strokeWidth={1.5}
              />
            </div>
            <div className="absolute -bottom-2 -right-2 bg-gray-100 rounded-full p-2 shadow-sm">
              <Plus size={20} style={{ color: category ? themeColor : "#818cf8" }} />
            </div>
          </div>
        );
      case "services":
        return (
          <div className="relative w-32 h-32 mb-6">
            <div className="absolute inset-0 bg-gradient-to-br from-green-50 to-teal-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <ShoppingBag
                size={48}
                style={{ color: category ? themeColor : "#2dd4bf" }}
                strokeWidth={1.5}
              />
            </div>
            <div className="absolute -bottom-2 -right-2 bg-gray-100 rounded-full p-2 shadow-sm">
              <Plus size={20} style={{ color: category ? themeColor : "#2dd4bf" }} />
            </div>
          </div>
        );
      case "events":
        return (
          <div className="relative w-32 h-32 mb-6">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <Calendar
                size={48}
                style={{ color: category ? themeColor : "#c084fc" }}
                strokeWidth={1.5}
              />
            </div>
            <div className="absolute -bottom-2 -right-2 bg-gray-100 rounded-full p-2 shadow-sm">
              <Plus size={20} style={{ color: category ? themeColor : "#c084fc" }} />
            </div>
          </div>
        );
      case "profiles":
        return (
          <div className="relative w-32 h-32 mb-6">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <User
                size={48}
                style={{ color: category ? themeColor : "#9ca3af" }}
                strokeWidth={1.5}
              />
            </div>
          </div>
        );
      case "followers":
        return (
          <div className="relative w-32 h-32 mb-6 transform transition-transform hover:scale-105 duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-50 to-pink-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <img
                src="/assets/empty-followers.svg"
                alt="No followers"
                className="w-16 h-16 opacity-70"
                style={{
                  filter: category ? `opacity(0.7) drop-shadow(0 0 0 ${themeColor})` : "none",
                }}
                onError={(e) => {
                  e.currentTarget.src =
                    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQwIDEwQzMwLjU3MTQgMTAgMjMgMTcuNTcxNCAyMyAyN0MyMyAzNi40Mjg2IDMwLjU3MTQgNDQgNDAgNDRDNDkuNDI4NiA0NCA1NyAzNi40Mjg2IDU3IDI3QzU3IDE3LjU3MTQgNDkuNDI4NiAxMCA0MCAxMFoiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIi8+CjxwYXRoIGQ9Ik02Ni42NjY3IDcwQzY2LjY2NjcgNTcuNDI4NiA1NC43NjE5IDQ3LjE0MjkgNDAgNDcuMTQyOUMyNS4yMzgxIDQ3LjE0MjkgMTMuMzMzMyA1Ny40Mjg2IDEzLjMzMzMgNzAiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTY2LjY2NjcgNzBINzMuMzMzM00xMy4zMzMzIDcwSDYuNjY2NjciIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+Cg==";
                }}
              />
            </div>
          </div>
        );
      case "following":
        return (
          <div className="relative w-32 h-32 mb-6 transform transition-transform hover:scale-105 duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <img
                src="/assets/empty-following.svg"
                alt="No following"
                className="w-16 h-16 opacity-70"
                style={{
                  filter: category ? `opacity(0.7) drop-shadow(0 0 0 ${themeColor})` : "none",
                }}
                onError={(e) => {
                  e.currentTarget.src =
                    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQwIDEwQzMwLjU3MTQgMTAgMjMgMTcuNTcxNCAyMyAyN0MyMyAzNi40Mjg2IDMwLjU3MTQgNDQgNDAgNDRDNDkuNDI4NiA0NCA1NyAzNi40Mjg2IDU3IDI3QzU3IDE3LjU3MTQgNDkuNDI4NiAxMCA0MCAxMFoiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIi8+CjxwYXRoIGQ9Ik02Ni42NjY3IDcwQzY2LjY2NjcgNTcuNDI4NiA1NC43NjE5IDQ3LjE0MjkgNDAgNDcuMTQyOUMyNS4yMzgxIDQ3LjE0MjkgMTMuMzMzMyA1Ny40Mjg2IDEzLjMzMzMgNzAiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTY2LjY2NjcgNzBINzMuMzMzM00xMy4zMzMzIDcwSDYuNjY2NjciIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+Cg==";
                }}
              />
            </div>
          </div>
        );
      case "customizations":
        return (
          <div className="relative w-32 h-32 mb-6">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <div className="flex flex-col items-center">
                <div
                  className="w-8 h-8 bg-amber-100 rounded-md mb-2"
                  style={{ backgroundColor: category ? `${themeColor}20` : "" }}
                ></div>
                <div
                  className="w-12 h-2 bg-amber-200 rounded-md"
                  style={{ backgroundColor: category ? `${themeColor}30` : "" }}
                ></div>
                <div
                  className="w-10 h-2 bg-amber-100 rounded-md mt-2"
                  style={{ backgroundColor: category ? `${themeColor}20` : "" }}
                ></div>
              </div>
            </div>
            <div className="absolute -bottom-2 -right-2 bg-gray-100 rounded-full p-2 shadow-sm">
              <Plus size={20} style={{ color: category ? themeColor : "#fbbf24" }} />
            </div>
          </div>
        );
      default:
        return (
          <div className="relative w-32 h-32 mb-6">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg transform rotate-6"></div>
            <div className="absolute inset-0 bg-white rounded-lg border border-gray-200 shadow-sm flex items-center justify-center">
              <ImageIcon
                size={48}
                style={{ color: category ? themeColor : "#9ca3af" }}
                strokeWidth={1.5}
              />
            </div>
          </div>
        );
    }
  };

  return (
    <div
      className="flex flex-col justify-center items-center h-[60vh] w-full p-6 text-center rounded-lg transition-all duration-300 ease-in-out"
      style={{
        backgroundColor: category ? `${themeColor}20` : "", // 20 is hex for 12% opacity
      }}
    >
      <div className="animate-fadeIn w-full max-w-md mx-auto flex flex-col items-center">
        <div className="mb-8 flex justify-center items-center">{getDefaultIcon()}</div>
        <h3 className="text-xl font-semibold text-gray-700 mb-3">{title}</h3>
        <p className="text-gray-500 max-w-md mb-6">{message}</p>

        {isOwnProfile && actionLabel && onAction && (
          <Badge
            className="btn-sm text-center font-light justify-center rounded-full w-full py-3 border-primary btn cursor-pointer mt-5"
            variant="outline"
            onClick={onAction}
            style={{
              borderColor: themeColor,
              color: themeColor,
            }}
          >
            {actionLabel}
          </Badge>
        )}
      </div>
    </div>
  );
};

// Add animation keyframes to global styles
if (typeof document !== "undefined") {
  const style = document.createElement("style");
  style.innerHTML = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .animate-fadeIn {
      animation: fadeIn 0.5s ease-out forwards;
    }
  `;
  document.head.appendChild(style);
}

export default EmptyState;
