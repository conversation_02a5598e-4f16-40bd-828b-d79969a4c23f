import { useState, useEffect, useRef } from "react";
import PostCardSkeleton from "@/components/CardSkeleton/PostCardSkeleton";
import EmptyState from "@/components/EmptyState";
import {
  Bookmark,
  ChevronLeft,
  MessageSquare,
  MoreHorizontal,
  Play,
  Star,
  X,
  ArrowLeft,
} from "react-feather";
import {
  MainContentFocus,
  PageSize,
  PostReactionType,
  PostReferenceType,
  PostsQuery,
  PostType,
  PostVisibilityFilter,
  ReferenceRelevancyFilter,
  useFullAccountQuery,
  usePostReferencesQuery,
  usePostsQuery,
  useTransactionStatusQuery,
} from "@/graphql/test/generated";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Label } from "@radix-ui/react-label";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { getFormattedTime, getLocation } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import Link from "next/link";
import { useReaction } from "@/lib/useReaction";
import { useBookMark } from "@/lib/useBookMark";
import SignInButton from "@/components/SignInButton";
import { useComment } from "@/lib/useCommen";
import { useQueryClient } from "@tanstack/react-query";
import { useAccount } from "wagmi";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ServiceDescription from "@/globalComponents/formatText";
import RichTextFormatter from "@/components/RichTextFormatter";
import { useDeletePost } from "@/lib/useDeletePost";
import { useSignInStore } from "@/components/GlobalSignInButton";

const PostsMyProfile = (props: any) => {
  // Helper function to split array into chunks
  const chunkArray = (array: any[], chunkSize: number) => {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  };

  const { address } = useAccount();

  const queryClient = useQueryClient();
  const commentResp = useComment();
  const addReaction = useReaction();
  const bookmarkResp = useBookMark();

  const [profileData, setProfileData] = useState<any>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [publicationsData, setPublicationsData] = useState<PostsQuery>();
  const [loadingPublications, setLoadingPublications] = useState(true);
  const [localLoading, setLocalLoading] = useState(true); // Add local loading state for UI
  const [isInitialLoad, setIsInitialLoad] = useState(true); // Track if this is the first load
  const [isPaginating, setIsPaginating] = useState(false); // Track pagination loading
  const [publicationsError, setPublicationsError] = useState<any | null>(null);
  const [currentFollowersCursor, setCurrentFollowersCursor] = useState<string | null>(null);
  const [allPost, setAllPost] = useState<any[]>([]);
  const [visibleChunks, setVisibleChunks] = useState(4);
  const [isOpen, setIsOpen] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0); // Store scroll position
  const scrollContainerRef = useRef<HTMLDivElement>(null); // Ref for scroll container
  // Store the currently selected post for the modal
  const [selectedPost, setSelectedPost] = useState<any | null>(null);
  // State for comment input
  const [message, setMessage] = useState("");
  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isDisable, setIsDisable] = useState(false);
  const [temp, setTemp] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [showMoreOptionsmodl, setShowMoreOptionsmodl] = useState(false);
  const [isOpenSheet, setIsOpenSheet] = useState(false);
  const [isDeletePost, setIsDeletePost] = useState(false);
  const [showComments, setShowComments] = useState(false);

  // For transaction handling
  const [Txn_id, setTxn_id] = useState<string | null>(null);

  // Comments state
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);
  const [isResonPost, setIsResonPost] = useState(false);

  const {
    data: profileQueryData,
    isLoading: isLoadingProfile,
    error: profileQueryError,
  } = useFullAccountQuery(
    {
      accountRequest: {
        address: props.userId,
      },
      accountStatsRequest: {
        account: props?.userId,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    setLoadingProfile(isLoadingProfile);
    if (profileQueryData) setProfileData(profileQueryData);
  }, [isLoadingProfile, profileQueryData, selectedPost]);

  const {
    isLoading: isLoadingPublications,
    data: publicationsQueryData,
    error: publicationsQueryError,
  } = usePostsQuery(
    {
      request: {
        filter: {
          authors: props.otherUserID, // joanakawaharalino
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        cursor: currentFollowersCursor,
        pageSize: PageSize.Fifty,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!props.otherUserID, // Only execute query when otherUserID exists
    }
  );

  useEffect(() => {
    // Set pagination loading state when loading starts
    if (isLoadingPublications) {
      setLoadingPublications(true);

      // If we have existing posts, this is pagination
      if (allPost.length > 0) {
        setIsPaginating(true);
        // Store current scroll position before pagination
        if (scrollContainerRef.current) {
          setScrollPosition(scrollContainerRef.current.scrollTop);
        }
      } else {
        // This is initial load
        setIsInitialLoad(true);
        setAllPost([]);
      }
    }

    // Process data when it's available
    if (publicationsQueryData) {
      setPublicationsData(publicationsQueryData);

      // Update posts list with new items
      setAllPost((prev) => [
        ...prev,
        ...publicationsQueryData?.posts?.items.filter(
          (item) => !prev.some((post) => post.id === item.id)
        ),
      ]);

      // Add a small delay before setting loading to false
      setTimeout(() => {
        setLoadingPublications(false);
        setIsPaginating(false);
        setIsInitialLoad(false);

        // Restore scroll position after pagination (with a small delay)
        if (scrollContainerRef.current && scrollPosition > 0) {
          setTimeout(() => {
            if (scrollContainerRef.current) {
              scrollContainerRef.current.scrollTop = scrollPosition;
            }
          }, 100);
        }
      }, 300);
    }

    // Handle errors
    if (publicationsQueryError) {
      setPublicationsError(publicationsQueryError);
      setLoadingPublications(false);
      setIsPaginating(false);
      setIsInitialLoad(false);
    }

    // If not loading and no data yet, ensure loading is false
    if (!isLoadingPublications && !publicationsQueryData && !publicationsQueryError) {
      setLoadingPublications(false);
      setIsPaginating(false);
      setIsInitialLoad(false);
    }
  }, [
    isLoadingPublications,
    publicationsQueryData,
    publicationsQueryError,
    allPost.length,
    scrollPosition,
  ]);

  // Effect to manage local loading state - only show skeleton on initial load
  useEffect(() => {
    // Only show skeleton loader on initial load, not during pagination
    if (isInitialLoad && loadingPublications) {
      setLocalLoading(true);
    }

    // Only transition to non-loading state when loading is complete
    if (!loadingPublications) {
      // Use a single timeout to prevent state flickering
      const timer = setTimeout(() => {
        setLocalLoading(false);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [loadingPublications, isInitialLoad]);

  // Transaction status monitoring
  const { data: transactionData, refetch: refetchTransactionStatus } = useTransactionStatusQuery(
    {
      request: {
        txHash: Txn_id,
      },
    },
    {
      enabled: !!Txn_id,
      refetchOnWindowFocus: false,
    }
  );

  // Monitor transaction status
  useEffect(() => {
    if (!Txn_id) return;
    console.log({ Txn_id });

    const POLL_INTERVAL = 1000;
    const MAX_CHECK_COUNT = 15;
    let checkCnt = 0;

    const intervalId = setInterval(async () => {
      if (checkCnt > MAX_CHECK_COUNT) {
        alert(` timeout `);
        clearInterval(intervalId);
      }
      if (
        transactionData?.transactionStatus?.__typename === "FinishedTransactionStatus" ||
        transactionData?.transactionStatus?.__typename === "FailedTransactionStatus"
      ) {
        // alert(`comment added successfully ✅`);
        setIsLoading(false);
        await refetchComments();
        setMessage("");
        setIsDisable(false);
        setTemp(true);

        clearInterval(intervalId);
      } else {
        checkCnt++;
        await refetchTransactionStatus();
      }
    }, POLL_INTERVAL);

    return () => clearInterval(intervalId); // Cleanup on unmount
  }, [Txn_id, transactionData, refetchTransactionStatus]);

  // Load next page on scroll
  const loadNextPage = () => {
    const nextCursor = publicationsData?.posts.pageInfo.next;
    if (nextCursor) {
      setCurrentFollowersCursor(nextCursor);
    }
  };

  // Detect scroll bottom
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight + 20) {
      setVisibleChunks((prev) => prev + 1);
      loadNextPage();
    }
  };

  // Comments functionality
  const {
    isLoading: commentsLoading,
    data: comments,
    error: commentsError,
    refetch: refetchComments,
  } = usePostReferencesQuery(
    {
      request: {
        referencedPost: selectedPost?.id || "", // post id
        referenceTypes: [PostReferenceType.CommentOn],
        cursor: currentCursor,
        pageSize: PageSize.Fifty,
        relevancyFilter: ReferenceRelevancyFilter.All,
        visibilityFilter: PostVisibilityFilter.Visible,
      },
    },
    {
      enabled: !!selectedPost?.id,
      refetchOnWindowFocus: false,
    }
  );

  // Function to handle opening the modal with selected post
  const handlePostClick = (post: any) => {
    if (!post) return;
    setSelectedPost(post);
    setIsOpen(true);
  };

  // Function to handle posting a comment
  const handleComment = async () => {
    if (!address) {
      useSignInStore.getState().setIsOpen(true);
      return;
    }

    setIsLoading(true);
    if (!message.trim() || !selectedPost?.id) return;

    if (!address) {
      sessionStorage.setItem("input", message);
      sessionStorage.setItem("openPost", props.cardData.id);
      useSignInStore.getState().setIsOpen(true);
      return;
    }

    try {
      console.log("Click Comment");
      setIsDisable(true);
      const Check = false;
      // Check if it's a momoka post, currently set to false
      const resp = await commentResp(selectedPost.id, message, Check);
      if (resp?.post?.__typename === "PostResponse") {
        setIsLoading(false);
        setTxn_id(resp?.post?.hash);
      }
    } catch (error: any) {
      setIsLoading(false);
      alert(error?.message ?? "something went wrong");
      setIsDisable(false);
    }
  };

  // Load more comments if available
  const loadMoreComments = () => {
    if (comments?.postReferences?.pageInfo?.next) {
      setCurrentCursor(comments.postReferences.pageInfo.next);
    }
  };

  // Handle like/dislike functionality
  const handleLikeDislike = async () => {
    if (!selectedPost) return;

    if (!address) {
      useSignInStore.getState().setIsOpen(true);
      return;
    }

    try {
      if (selectedPost?.__typename !== "Post") {
        console.log("not a post");
        return;
      }

      if (!selectedPost?.operations?.hasReacted) {
        const resplike = await addReaction(selectedPost.id, PostReactionType.Upvote, "like");
        if (selectedPost.operations) {
          selectedPost.operations.hasReacted = true;
        }

        if (selectedPost?.stats?.reactions) {
          selectedPost.stats.reactions++;
        }
      } else {
        const respdislike = await addReaction(
          selectedPost.id,
          PostReactionType.Downvote,
          "dislike"
        );
        if (selectedPost.operations) {
          selectedPost.operations.hasReacted = false;
        }

        if (selectedPost?.stats?.reactions) {
          selectedPost.stats.reactions--;
        }
      }
      // Force a refresh of the component
      setTemp(!temp);
    } catch (error: any) {
      alert(error?.message ?? "something went wrong");
    }
  };

  // Handle bookmark functionality
  const handleBookmark = async () => {
    if (!selectedPost) return;

    if (!address) {
      useSignInStore.getState().setIsOpen(true);
      return;
    }

    try {
      if (selectedPost?.__typename !== "Post") {
        console.log("not a post");
        return;
      }

      if (selectedPost?.operations?.hasBookmarked) {
        const resp = await bookmarkResp(selectedPost.id, false);
        selectedPost.operations.hasBookmarked = false;
      } else {
        const resp = await bookmarkResp(selectedPost.id, true);
        if (selectedPost.operations) {
          selectedPost.operations.hasBookmarked = true;
        }
      }
      // Force a refresh of the component
      setTemp(!temp);
    } catch (error) {
      alert(error ?? "something went wrong");
    }
  };

  // Update UI when actions change
  useEffect(() => {
    // This will re-render when these dependencies change
  }, [
    temp,
    selectedPost?.__typename === "Post" && selectedPost?.operations?.hasBookmarked,
    selectedPost?.__typename === "Post" && selectedPost?.operations?.hasReacted,
  ]);

  // Split posts into chunks of 5
  const postChunks = chunkArray(allPost, 5);

  if (publicationsError) return <div>Could not find this profile.</div>;

  const hexToRgb = (hex: string) => {
    if (!hex) return "0, 0, 0"; // Default to black if no color provided
    hex = hex.replace(/^#/, "");
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `${r}, ${g}, ${b}`;
  };

  // Helper function to render media (image or video)
  const renderMedia = (post: any, className: string) => {
    if (!post) return null;

    if (post?.metadata?.image?.item) {
      return (
        <img
          src={sanitizeDStorageUrl(post.metadata.image.item || "")}
          alt={post.metadata.title || "Post image"}
          className={className}
          style={{
            backgroundColor: `rgba(${hexToRgb(props.borderColor)}, 0.3)`,
            borderColor: props.borderColor,
            aspectRatio: "auto",
            width: "full",
          }}
        />
      );
    } else if (post?.metadata?.video?.item) {
      return (
        <div key={sanitizeDStorageUrl(post.metadata.video.item)} className="relative">
          <video
            key={sanitizeDStorageUrl(post.metadata.video.item)}
            controls={false}
            autoPlay={false}
            muted
            className={className}
            poster={sanitizeDStorageUrl(post.metadata.video.cover)}
            style={{
              backgroundColor: `rgba(${hexToRgb(props.borderColor)}, 0.3)`,
              borderColor: props.borderColor,
            }}
          >
            <source src={sanitizeDStorageUrl(post.metadata.video.item)} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className="absolute top-0 right-0">
            <Play className="text-white" />
          </div>
        </div>
      );
    }

    return null;
  };

  // Helper function to render media in the modal (image or video)
  const renderMediaModal = (post: any, className: string) => {
    if (!post) return null;

    if (post?.metadata?.image?.item) {
      return (
        <img
          src={sanitizeDStorageUrl(post?.metadata?.image?.item || "")}
          alt={post.metadata.image.item}
          className={className}
          style={{
            backgroundColor: `rgba(${hexToRgb(props.border)}, 0.3)`,
            borderColor: props.border,
            aspectRatio: "auto",
            width: "auto",
          }}
        />
      );
    } else if (post?.metadata?.video?.item) {
      return (
        <div key={sanitizeDStorageUrl(post.metadata.video.item)} className="relative">
          <video
            key={sanitizeDStorageUrl(post.metadata.video.item)}
            controls={true}
            autoPlay={true}
            className={className}
            poster={sanitizeDStorageUrl(post.metadata.video.cover)}
            style={{
              backgroundColor: `rgba(${hexToRgb(props.borderColor)}, 0.3)`,
              borderColor: props.borderColor,
            }}
          >
            <source src={sanitizeDStorageUrl(post.metadata.video.item)} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      );
    }

    return null;
  };

  // shere post
  function copyProfileLink(id: string) {
    const url = `https://www.amuzn.app/profile/${id}`;
    navigator.clipboard
      .writeText(url)
      .then(() => {
        alert("Profile link copied to clipboard!");
      })
      .catch((err) => {
        console.error("Failed to copy link: ", err);
      });
  }

  const deletePostResp = useDeletePost();

  return (
    <div>
      <div
        ref={scrollContainerRef}
        className="flex flex-row w-full overflow-y-auto overflow-x-hidden gap-3 hide-scroll-custom py-4 h-[calc(100vh-280px)] max-md:h-[100vh]"
        onScroll={handleScroll}
      >
        {localLoading ? (
          <div className=" w-full">
            <PostCardSkeleton count={2} />
          </div>
        ) : allPost.length > 0 ? (
          <div className="grid grid-cols-1 gap-[1px] w-full">
            {postChunks.slice(0, visibleChunks).map((chunk, chunkIndex) => (
              <div key={chunkIndex} className="w-full mt-0">
                <div className="w-full mb-[1px] gap-[1px]">
                  <div className="grid grid-cols-6 gap-[1px] mb-[1px]">
                    <div
                      className="col-span-4 cursor-pointer w-full aspect-[2/1]"
                      onClick={() => handlePostClick(chunk[0])}
                    >
                      {renderMedia(chunk[0], "h-full w-full object-cover border-2 aspect-[2/1]")}
                    </div>

                    <div
                      className="col-span-2 cursor-pointer w-full aspect-square"
                      onClick={() => handlePostClick(chunk[1])}
                    >
                      {renderMedia(chunk[1], "h-full w-full object-cover border-2 aspect-square")}
                    </div>
                  </div>
                  <div className="grid grid-cols-6 gap-[1px] min-h-[250px] ">
                    <div className="col-span-2 h-full">
                      <div
                        className="cursor-pointer w-full h-1/2 aspect-square"
                        onClick={() => handlePostClick(chunk[2])}
                      >
                        {renderMedia(
                          chunk[2],
                          "w-full h-full object-cover border-2 min-h-[124px] aspect-square mb-[1px]"
                        )}
                      </div>

                      <div
                        className="cursor-pointer w-full h-1/2 aspect-square"
                        onClick={() => handlePostClick(chunk[3])}
                      >
                        {renderMedia(
                          chunk[3],
                          "w-full h-full object-cover border-2 min-h-[124px] aspect-square"
                        )}
                      </div>
                    </div>

                    <div
                      className="col-span-4 cursor-pointer w-full aspect-square "
                      onClick={() => handlePostClick(chunk[4])}
                    >
                      {renderMedia(
                        chunk[4],
                        "w-full h-full  object-cover border-2 min-h-[250px] aspect-square"
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Pagination loader - show at bottom when paginating */}
            {isPaginating && (
              <div className=" w-full">
                <PostCardSkeleton count={1} />
              </div>
            )}
          </div>
        ) : (
          <EmptyState
            type="posts"
            title="No Posts Yet"
            message={
              props.isOtherProfile
                ? "This user hasn't created any posts yet."
                : "Posts you create will appear here"
            }
            isOwnProfile={!props.isOtherProfile}
          />
        )}
      </div>

      {/* Post Detail Modal */}

      <div>
        <Modal
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          placement="auto"
          size="5xl"
          isDismissable={false}
          hideCloseButton={false}
        >
          <ModalContent className="px-5 py-5 max-md:py-0 max-md:px-0 rounded-3xl h-[80vh] max-md:rounded-none max-md:h-full max-md:max-h-screen mx-0 max-md:overflow-hidden">
            {(onClose) => (
              <>
                <ModalBody className="p-0 max-md:h-full max-md:flex max-md:flex-col">
                  <div className="max-md:min-w-full pb-4 max-md:pb-0 rounded-3xl max-md:rounded-none max-md:h-full max-md:flex max-md:flex-col">
                    <div className="grid grid-cols-2 gap-4 max-md:grid-cols-1 max-md:h-full max-md:flex max-md:flex-col">
                      <div
                        className={`flex flex-col justify-between ${showComments ? "max-md:hidden" : ""}`}
                      >
                        {/* Mobile Header for first grid */}
                        <div className="hidden max-md:block px-3 py-2 border-b bg-white sticky top-0 z-10">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-2">
                              <X className="cursor-pointer" onClick={() => setIsOpen(false)} />
                              <img
                                src={
                                  sanitizeDStorageUrl(selectedPost?.author?.metadata?.picture) ||
                                  "/assets/profileAvatar.svg"
                                }
                                alt=""
                                className="w-[40px] h-[40px] rounded-full object-cover"
                              />
                              <div>
                                <p className="font-bold text-sm">
                                  {selectedPost?.author?.metadata?.name ||
                                    selectedPost?.author?.username?.localName ||
                                    "Profile Name"}
                                </p>
                                <p className="text-[#616770] text-xs">
                                  {getLocation(selectedPost?.author?.metadata?.attributes) ||
                                    selectedPost?.author?.username?.localName}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="max-md:overflow-y-auto max-md:h-[calc(100vh-60px)]">
                          <div className="mb-2 border-b pb-2 py-2 hidden max-md:block pl-2">
                            <RichTextFormatter
                              text={selectedPost?.metadata?.content || ""}
                              className="text-primary break-words"
                              preserveWhitespace={true}
                              enableMarkdown={true}
                            />
                          </div>

                          {/* Post Media */}
                          <div className="flex flex-fill justify-center items-center min-h-[65vh] max-h-[65vh] max-md:min-h-[40vh] max-md:max-h-[40vh]">
                            {
                              // @ts-ignore
                              selectedPost?.metadata?.video?.item && (
                                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                  <Play className="text-white h-12 w-12 opacity-80" />
                                </div>
                              )
                            }
                            {renderMediaModal(
                              selectedPost,
                              "w-full max-h-[60vh]  max-md:max-h-[40vh]"
                            )}
                          </div>
                          {/* Post Info & Actions */}
                          <div className="row justify-between mt-2 p-3">
                            <p className="text-subtitle text-[14px]">
                              {getFormattedTime(selectedPost?.timestamp || Date.now())}
                            </p>
                            <div className="row gap-3">
                              <p className="text-[14px] text-titleLabel">
                                {props.location || selectedPost?.metadata?.location || ""}
                              </p>
                              <div className="row gap-3">
                                <p
                                  className={
                                    selectedPost?.__typename === "Post" &&
                                    selectedPost?.operations?.hasReacted
                                      ? "text-sm -mr-1"
                                      : "text-iconColor text-sm -mr-1"
                                  }
                                  style={
                                    selectedPost?.__typename === "Post" &&
                                    selectedPost?.operations?.hasReacted
                                      ? {
                                          color: props.borderColor,
                                          fill: props.borderColor,
                                        }
                                      : undefined
                                  }
                                >
                                  {selectedPost?.__typename === "Post"
                                    ? selectedPost?.stats?.reactions || 0
                                    : 0}
                                </p>
                                <Star
                                  className={
                                    selectedPost?.__typename === "Post" &&
                                    selectedPost?.operations?.hasReacted
                                      ? "cursor-pointer"
                                      : "text-iconColor cursor-pointer"
                                  }
                                  style={
                                    selectedPost?.__typename === "Post" &&
                                    selectedPost?.operations?.hasReacted
                                      ? {
                                          color: props.borderColor,
                                          fill: props.borderColor,
                                        }
                                      : undefined
                                  }
                                  onClick={handleLikeDislike}
                                />
                                <MessageSquare
                                  className="text-iconColor cursor-pointer"
                                  onClick={() => {
                                    if (window.innerWidth <= 768) {
                                      setShowComments(true);
                                    }
                                  }}
                                />
                                <Bookmark
                                  className={
                                    selectedPost?.__typename === "Post" &&
                                    selectedPost?.operations?.hasBookmarked
                                      ? "cursor-pointer"
                                      : "text-iconColor cursor-pointer"
                                  }
                                  style={
                                    selectedPost?.__typename === "Post" &&
                                    selectedPost?.operations?.hasBookmarked
                                      ? {
                                          color: props.borderColor,
                                          fill: props.borderColor,
                                        }
                                      : undefined
                                  }
                                  onClick={handleBookmark}
                                />
                                <DropdownMenu
                                  open={showMoreOptionsmodl}
                                  onOpenChange={(open) => {
                                    // Only allow closing via the trigger button
                                    if (!open) setShowMoreOptionsmodl(false);
                                  }}
                                >
                                  <DropdownMenuTrigger asChild>
                                    <MoreHorizontal
                                      className="text-iconColor cursor-pointer max-md:hidden"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setShowMoreOptionsmodl(!showMoreOptionsmodl);
                                      }}
                                    />
                                  </DropdownMenuTrigger>
                                  {!props.isOtherProfile ? (
                                    <DropdownMenuContent
                                      className="w-60 rounded-3xl p-0 mr-52"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <DropdownMenuLabel
                                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                        onClick={() => copyProfileLink(props.userID)}
                                      >
                                        Share post
                                      </DropdownMenuLabel>
                                      <DropdownMenuLabel
                                        className="text-center font-normal text-base  pt-3 cursor-pointer"
                                        onClick={() => {
                                          address
                                            ? setIsResonPost(true)
                                            : useSignInStore.getState().setIsOpen(true);
                                        }}
                                      >
                                        Report post
                                      </DropdownMenuLabel>
                                      <DropdownMenuSeparator />
                                    </DropdownMenuContent>
                                  ) : (
                                    <DropdownMenuContent className="w-60 rounded-3xl p-0 mr-52">
                                      <DropdownMenuLabel className="text-center font-normal text-base border-b-2 py-3 cursor-pointer">
                                        Edit post
                                      </DropdownMenuLabel>
                                      <DropdownMenuLabel className="text-center font-normal text-base border-b-2 py-3 cursor-pointer">
                                        Share post
                                      </DropdownMenuLabel>
                                      <DropdownMenuLabel
                                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                                        onClick={() => {
                                          setIsDeletePost(true), setIsOpen(false);
                                        }}
                                      >
                                        Delete post
                                      </DropdownMenuLabel>
                                      <DropdownMenuLabel className="text-center font-normal text-base cursor-pointer">
                                        Cancel
                                      </DropdownMenuLabel>
                                      <DropdownMenuSeparator />
                                    </DropdownMenuContent>
                                  )}
                                </DropdownMenu>
                                <MoreHorizontal
                                  className="text-iconColor cursor-pointer md:hidden"
                                  onClick={() => setIsOpenSheet(true)}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`flex flex-col justify-between h-full max-md:h-full max-md:flex-1 max-md:min-h-0 relative ${!showComments ? "max-md:hidden" : ""}`}
                      >
                        {/* Mobile Header - Show profile info at top on mobile */}
                        <div className="md:hidden px-3 py-2 border-b bg-white sticky top-0 z-10">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-2">
                              <ArrowLeft
                                className="cursor-pointer md:hidden"
                                onClick={() => setShowComments(false)}
                              />
                              <img
                                src={
                                  sanitizeDStorageUrl(selectedPost?.author?.metadata?.picture) ||
                                  "/assets/profileAvatar.svg"
                                }
                                alt=""
                                className="w-[40px] h-[40px] rounded-full object-cover"
                              />
                              <div>
                                <p className="font-bold text-sm">
                                  {selectedPost?.author?.metadata?.name ||
                                    selectedPost?.author?.username?.localName ||
                                    "Profile Name"}
                                </p>
                                <p className="text-[#616770] text-xs">
                                  {getLocation(selectedPost?.author?.metadata?.attributes) ||
                                    selectedPost?.author?.username?.localName}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Content area with scrollbar */}
                        <div className="px-3 max-md:px-4 overflow-y-auto flex-1 max-md:min-h-0 max-h-[calc(65vh-120px)] max-md:max-h-none">
                          <div className="md:sticky md:top-0 bg-white max-md:hidden">
                            <div className="row gap-2 items-start max-md:items-center">
                              <div>
                                <img
                                  src={
                                    sanitizeDStorageUrl(selectedPost?.author?.metadata?.picture) ||
                                    "/assets/profileAvatar.svg"
                                  }
                                  alt=""
                                  className="w-[40px] h-[40px] rounded-full object-cover"
                                />
                              </div>

                              <div className="flex flex-row gap-2 max-md:flex-col">
                                <div>
                                  <p className="font-bold text-nowrap">
                                    {selectedPost?.author?.metadata?.name ||
                                      selectedPost?.author?.username?.localName ||
                                      "Profile Name"}
                                  </p>
                                </div>
                                <p className="text-[#616770] max-md:-mt-1">
                                  {" "}
                                  {getLocation(selectedPost?.author?.metadata?.attributes) ||
                                    selectedPost?.author?.username?.localName}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="max-md:pb-2 ">
                            {/* Content with text wrapping */}
                            <div className="mb-2 border-b pb-2 md:pl-[40px] max-md:pl-0">
                              <RichTextFormatter
                                text={selectedPost?.metadata?.content || ""}
                                className="text-primary break-words"
                                preserveWhitespace={true}
                                enableMarkdown={true}
                              />
                            </div>

                            {/* Comments Section */}
                            <div className="mt-4 mb-4 max-md:mb-4">
                              <h3 className="font-bold mb-2 text-sm">Comments</h3>
                              {commentsLoading ? (
                                <div className="text-center py-4">Loading comments...</div>
                              ) : comments?.postReferences?.items &&
                                comments.postReferences.items.length > 0 ? (
                                <div className="space-y-3">
                                  {comments.postReferences.items.map((comment, index) => (
                                    <div key={index} className="flex gap-2 mb-2 border-b pb-2">
                                      <div className="flex-grow">
                                        <Link
                                          href={`/profile/lens/${comment?.author?.username?.localName}`}
                                        >
                                          <p className="font-semibold text-sm">
                                            {
                                              // @ts-ignore
                                              comment?.author?.metadata?.name || "Anonymous"
                                            }{" "}
                                          </p>
                                        </Link>
                                        <div className="break-words whitespace-pre-wrap text-sm">
                                          {
                                            // @ts-ignore
                                            comment.metadata?.content || ""
                                          }
                                        </div>
                                        <p className="text-xs text-subtitle mt-1">
                                          {getFormattedTime(comment.timestamp || Date.now())}
                                        </p>
                                      </div>
                                    </div>
                                  ))}

                                  {comments.postReferences.pageInfo?.next && (
                                    <div className="text-center pt-2 pb-4">
                                      <Badge
                                        className="cursor-pointer"
                                        variant="outline"
                                        onClick={loadMoreComments}
                                      >
                                        Load more comments
                                      </Badge>
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="text-center py-4 text-subtitle">
                                  No comments yet. Be the first to comment!
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Comment Input - Fixed positioning for mobile */}
                        <div className="grid w-full gap-1.5 max-md:gap-0  px-3 pt-2 bg-background border-t max-md:sticky max-md:bottom-0 max-md:bg-white max-md:z-20 max-md:border-t-2 max-md:shadow-lg">
                          <Label
                            htmlFor="comment"
                            className="text-primary text-sm font-bold max-md:text-start"
                          >
                            Add a comment
                          </Label>
                          <Textarea
                            placeholder="Write a comment..."
                            id="comment"
                            className="text-primary text-base max-md:min-h-[80px] resize-none"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            onFocus={() => {
                              // Ensure modal stays in proper position on iOS
                              if (typeof window !== "undefined" && window.scrollTo) {
                                setTimeout(() => window.scrollTo(0, 0), 100);
                              }
                            }}
                          />
                          <Badge
                            className="btn-xs btn text-center mt-2 rounded-full w-[100px] min-h-[30px] cursor-pointer max-md:mb-2"
                            variant="outline"
                            onClick={() => !isDisable && !isLoading && handleComment()}
                          >
                            {isLoading ? (
                              <span className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></span>
                            ) : (
                              "Post"
                            )}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Bottom Icon  */}
      <Sheet open={isOpenSheet} onOpenChange={setIsOpenSheet}>
        <SheetContent side="bottom" className="bg-transparent border-none">
          <div className="bg-white rounded-[14px]">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-tr-[14px]  rounded-tl-[14px] border-b"
              onClick={() => copyProfileLink(props.userID)}
            >
              Share
            </button>
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px]"
              onClick={() => {
                address ? setIsResonPost(true) : useSignInStore.getState().setIsOpen(true);
              }}
            >
              Report
            </button>
          </div>
          <div className="mt-4">
            <button
              className="bg-white text-primary w-full h-[50px] rounded-[14px] "
              onClick={() => setIsOpenSheet(false)}
            >
              Cancel
            </button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Signout Modal */}
      <div>
        <AlertDialog open={isDeletePost} onOpenChange={setIsDeletePost}>
          <AlertDialogTrigger asChild>
            {/* Empty or hidden trigger since we're controlling externally */}
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent className="w-80 p-12 rounded-3xl" style={{ borderRadius: "20px" }}>
            <AlertDialogHeader>
              <AlertDialogDescription>
                <p className="text-center text-black text-lg">
                  Are you sure you would like to delete the post?
                </p>
                <div>
                  <Button
                    variant="outline"
                    className=" rounded-full w-full mt-5 border-black text-black border-2 py-5  text-base"
                    onClick={async () => {
                      try {
                        const resp = await deletePostResp(selectedPost.id);
                        // Close the modal after successful deletion
                        setIsDeletePost(false);
                      } catch (error) {
                        console.error("Error deleting post:", error);
                      }
                    }}
                  >
                    Yes, delete
                  </Button>
                  <Button
                    variant="outline"
                    className=" rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base "
                    onClick={() => setIsDeletePost(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* SignIn Modal */}
      <div className="max-md:h-full px-5">
        <AlertDialog open={isSigninOpen} onOpenChange={setIsSigninOpen}>
          <AlertDialogTrigger asChild>
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className="max-md: overflow-scroll h-full hide-scroll">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => {
                    sessionStorage.removeItem("input");
                    sessionStorage.removeItem("openPost");
                    setIsSigninOpen(false);
                  }}
                >
                  <X />
                </div>
                <SignInButton />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default PostsMyProfile;
