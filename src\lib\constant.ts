import { chains } from "@lens-chain/sdk/viem";
import type { FallbackTransport } from "viem";
import { http, fallback } from "viem";

export const LENS_API_URL = "https://api.lens.xyz/graphql"
export const IPFS_GATEWAY = "https://gw.ipfs-lens.dev/ipfs";
export const STORAGE_NODE_URL = "https://api.grove.storage";
export const CHAIN = chains.mainnet;

// "https://api-v2.lens.dev/";
export const LENS_MAINNET_RPCS = [
    "https://rpc.lens.xyz",
    "https://api.lens.matterhosted.dev",
    "https://lens-mainnet.g.alchemy.com/v2/N_HuqeYE3mr_enxw-BGFI2rOm1U7bhGy"
  ];
  
  export const LENS_TESTNET_RPCS = [
    "https://rpc.testnet.lens.dev",
    "https://lens-sepolia.g.alchemy.com/v2/N_HuqeYE3mr_enxw-BGFI2rOm1U7bhGy"
  ];
  

const getRpc = ({ mainnet }: { mainnet: boolean }): FallbackTransport => {
    if (mainnet) {
      return fallback(
        LENS_MAINNET_RPCS.map((rpc) => http(rpc, { batch: { batchSize: 10 } }))
      );
    }
  
    return fallback(
      LENS_TESTNET_RPCS.map((rpc) => http(rpc, { batch: { batchSize: 10 } }))
    );
  };
  
  export default getRpc;
  
  export const NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID = "515406640a4bc4a1099bb8092d154507"




  export enum OrderActivityType {
    orderInfo = 'Order info' , 
    orderStatusUpdate = 'Order status update',
  }
  
  
  export enum OrderStatus {
    basket = "BASKET",
    newOrder = 'newOrder',
    incomplete = 'incomplete',
    declined = 'declined',
    autoDeclined = 'autoDeclined',
    accepted = 'accepted',
    delivered = 'delivered',
    completed = 'completed',
    autoCompleted = 'autoCompleted',
    revisionRequest = 'revisionRequest',
    refundRequest = 'refundRequest',
    refunded = 'refunded',
    cancellationRequest = 'cancellationRequest',
    cancelled = 'cancelled',
  }

export const OrderStatusValue: Record<string, OrderStatus> = {
  BASKET: OrderStatus.basket,
  NEW: OrderStatus.newOrder,
  INCOMPLETE: OrderStatus.incomplete,
  DECLINED: OrderStatus.declined,
  "AUTO-DECLINED": OrderStatus.autoDeclined,
  ACCEPTED: OrderStatus.accepted,
  DELIVERED: OrderStatus.delivered,
  COMPLETED: OrderStatus.completed,
  "AUTO-COMPLETED": OrderStatus.autoCompleted,
  "REVISION REQUEST": OrderStatus.revisionRequest,
  "REFUND REQUEST": OrderStatus.refundRequest,
  REFUNDED: OrderStatus.refunded,
  "CANCELLATION REQUEST": OrderStatus.cancellationRequest,
  CANCELLED: OrderStatus.cancelled,
};

export const OrderStatusName: Record<OrderStatus, string> = {
  [OrderStatus.basket]: "BASKET",
  [OrderStatus.newOrder]: "NEW",
  [OrderStatus.incomplete]: "INCOMPLETE",
  [OrderStatus.declined]: "DECLINED",
  [OrderStatus.autoDeclined]: "AUTO-DECLINED",
  [OrderStatus.accepted]: "ACCEPTED",
  [OrderStatus.delivered]: "DELIVERED",
  [OrderStatus.completed]: "COMPLETED",
  [OrderStatus.autoCompleted]: "AUTO-COMPLETED",
  [OrderStatus.revisionRequest]: "REVISION REQUEST",
  [OrderStatus.refundRequest]: "REFUND REQUEST",
  [OrderStatus.refunded]: "REFUNDED",
  [OrderStatus.cancellationRequest]: "CANCELLATION REQUEST",
  [OrderStatus.cancelled]: "CANCELLED",
};

export enum OrderStatusType {
  BASKET = "BASKET",
  NEW = "NEW",
  INCOMPLETE = "INCOMPLETE",
  DECLINED = "DECLINED",
  AUTO_DECLINED = "AUTO-DECLINED",
  ACCEPTED = "ACCEPTED",
  DELIVERED = "DELIVERED",
  COMPLETED = "COMPLETED",
  AUTO_COMPLETED = "AUTO-COMPLETED",
  REVISION_REQUEST = "REVISION REQUEST",
  REFUND_REQUEST = "REFUND REQUEST",
  REFUNDED = "REFUNDED",
  CANCELLATION_REQUEST = "CANCELLATION REQUEST",
  CANCELLED = "CANCELLED"
}



export enum OrderInfo {
  full = "full",
  ten = "ten",
  eighty = "eighty",
  refund = "refund",
  cancellationDeclined = "cancellationDeclined",
  dueDateRequested = "dueDateRequested",
  dueDateAccepted = "dueDateAccepted",
  dueDateDeclined = "dueDateDeclined",
}

export const OrderInfoName: Record<OrderInfo, string> = {
  [OrderInfo.full]: "Payment pre-authorization by Stripe",
  [OrderInfo.ten]: "Payment received",
  [OrderInfo.refund]: "Funds returned by Stripe",
  [OrderInfo.eighty]: "Payment received",

  [OrderInfo.cancellationDeclined]: "Cancellation request declined",
  [OrderInfo.dueDateRequested]: "New due date request",
  [OrderInfo.dueDateAccepted]: "New due date accepted",
  [OrderInfo.dueDateDeclined]: "New due date declined",
};

export interface OrderNewDueDateModel {
  orderId: string;
  formattedDate: string;
  reason: string;
  comment: string;
}



export function getOrderInfoDesc({
  status,
  sellerName,
  loggedInUser ,
  newDateModel,
}: {
  status: OrderInfo;
  sellerName: string;
  loggedInUser: string;
  newDateModel?: OrderNewDueDateModel;
}): string | null {

  switch (status) {
    case OrderInfo.full:
      return 'Full order amount pre-authorized by Stripe';
    case OrderInfo.ten:
      return `${sellerName} received 10% of order service cost.`;
    case OrderInfo.eighty:
      return `${sellerName} received 80% of order service cost.`;
    case OrderInfo.refund:
      return 'Order refund processed.';
    case OrderInfo.cancellationDeclined:
      return `${loggedInUser} declined order cancellation.`;
    case OrderInfo.dueDateRequested:
      return `${loggedInUser} requested the new due date for the order #${newDateModel?.orderId}.\nNew due date: ${newDateModel?.formattedDate}.\nReason: ${newDateModel?.reason}.\nComment: “${newDateModel?.comment}”`;
    case OrderInfo.dueDateAccepted:
      return `${loggedInUser} accepted the new due date for the order #${newDateModel?.orderId}.\nNew due date: ${newDateModel?.formattedDate}.`;
    case OrderInfo.dueDateDeclined:
      return `${loggedInUser} declined the new due date for the order #${newDateModel?.orderId}.`;
    default:
      return null;
  }
}

export function getOrderStatusDesc({
  status,
  comment,
  reason,
  userName,
  sellerName,
  profileType, // either "user" or "creator"
}: {
  status: OrderStatusType;
  comment?: string;
  reason?: string;
  userName: string;
  sellerName: string;
  profileType: "user" | "creator";
}): string | null {
  const formattedComment = comment?.trim()
    ? `\n" ${comment.trim()} "`
    : "";

  const formattedReason = reason?.trim()
    ? `\nReason: "${reason.trim()}"`
    : "";

  const cancelPerson = profileType === "user" ? userName : sellerName;

  switch (status) {
    case OrderStatusType.NEW:
      return `${userName} placed the order.\n${sellerName} have 48 hours to accept it.${formattedComment}`;
    case OrderStatusType.INCOMPLETE: //
      return `${sellerName} requested more information about the order.${formattedComment}`;
    case OrderStatusType.DECLINED: //
      return `${sellerName} declined the order.${formattedReason}${formattedComment}`;
    case OrderStatusType.AUTO_DECLINED: // 
      return `Order was automatically declined because ${sellerName} did not respond to it in 48 hours`;
    case OrderStatusType.ACCEPTED:
      return `${sellerName} accepted the order and will start working on it.${formattedComment}`;
    case OrderStatusType.DELIVERED:
      return `${sellerName} delivered the order.${formattedComment}`;
    case OrderStatusType.COMPLETED:
      return `${userName} accepted the order.${formattedComment}`;
    case OrderStatusType.AUTO_COMPLETED:
      return `The order has Auto-Completed.\n\nNote that in accordance with the Terms of Service, if no action is taken by the Buyer to change the status of an order after it has been marked as “Delivered” by the Seller, the order will automatically complete and marked as “Auto-Completed” seven (7) days after the order Due Date. \n\nPrior to this time, the Buyer has the opportunity to action a Delivered order by either accepting, revising it, or requesting a refund by changing the order status to “Completed”, “Revision Request”, or “Refund Request”, respectively.\n\nYour AMUZN Team\<EMAIL>\nwww.amuzn.com`;

    case OrderStatusType.REVISION_REQUEST: // 
      return `${userName} requested revision.${formattedComment}`;
      
    case OrderStatusType.REFUND_REQUEST: // 
      return `${userName} requested refund.${formattedReason}${formattedComment}`;
    case OrderStatusType.REFUNDED:
      return `${cancelPerson} accepted order refund.`;
      
    case OrderStatusType.CANCELLATION_REQUEST:
      return `${cancelPerson} requested order cancellation.${formattedComment}`;
    case OrderStatusType.CANCELLED:
      return `${cancelPerson} accepted cancellation request.`;
    default:
      return null;
  }
}
