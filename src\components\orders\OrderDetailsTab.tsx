import React, { useState } from "react";
import { Info } from "react-feather";
import CustomizationDetailDrawer from "./CustomizationDetailDrawer";

interface ServiceCustomization {
  duration: string;
  id: string;
  title: string;
  time?: string;
  price?: string | number;
  // add other fields as needed
}

interface ServiceDetails {
  id: string;
  title?: string;
  duration?: any;
  price?: number;
  description?: string;
  customizations?: ServiceCustomization[];
  // add other fields as needed
}

interface serviceModel {
  // id: string;
  // title?: string;
  // duration?: number | string;
  price?: number;
  // description?: string;
  // customizations?: ServiceCustomization[];
  // add other fields as needed
}

interface Order {
  id: string | number;
  status: string;
  orderNumber: string;
  image: string;
  userName: string;
  title: string;
  totalCost: string;
  selectedCustomizations?: string[];
  serviceDetails?: ServiceDetails;
  comment?: string;
  deliveryDetails?: string;
  serviceModel: serviceModel;
}

interface OrderDetailsTabProps {
  order: Order;
  currencySymbol?: string;
  currencyCode?: string;
}

const OrderDetailsTab: React.FC<OrderDetailsTabProps> = ({
  order,
  currencySymbol = "$",
  currencyCode = "USD",
}) => {
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<any>(null);

  const handleInfoClick = (option: any) => {
    setSelectedOption(option);
    setIsDetailOpen(true);
  };

  // Calculate total price of selected customizations
  const selectedCustomizations =
    order.serviceDetails?.customizations?.filter((option) =>
      order.selectedCustomizations?.includes(option.id)
    ) || [];

  const customizationsTotal = selectedCustomizations.reduce((sum, option) => {
    // Use the same price calculation as in the UI (+16% logic)
    const price = option?.price !== undefined ? Number(option?.price) : 0;
    return sum + price;
  }, 0);

  // Base service price
  const basePrice =
    order?.serviceModel?.price !== undefined ? Number(order?.serviceModel?.price) : 0;

  // Subtotal = base + customizations
  const subtotal = basePrice + customizationsTotal;

  // Transaction fee (4%)
  const transactionFee = subtotal * 0.04;

  // Order total
  const orderTotal = subtotal + transactionFee;

  console.log(order);

  return (
    <div className="space-y-6">
      {order?.deliveryDetails && (
        <section>
          <h3 className="font-semibold mb-2">Order delivery details</h3>
          <p className="text-gray-600 text-sm">{order?.deliveryDetails}</p>
        </section>
      )}

      <section>
        <p className="font-bold text-primary mb-2 text-sm">{order.serviceDetails?.title}</p>
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>{order.serviceDetails?.duration} day</span>
          <span>
            {currencySymbol}
            {Number(order?.serviceModel?.price)?.toFixed(2)}
          </span>
        </div>
        <p className="text-gray-600 text-sm">{order.serviceDetails?.description}</p>
      </section>

      {/* Only render comment section if order.comment exists */}
      {order?.comment && (
        <section>
          <p className="font-semibold mb-2 text-sm">Client's comment</p>
          <p className="text-gray-600 text-sm">{order?.comment}</p>
        </section>
      )}

      <section>
        <div className="">
          {(order.selectedCustomizations?.length ?? 0) > 0 && (
            <div className="">
              <p className="font-semibold mb-4 text-sm">Customization</p>

              <div className="border-b pb-2">
                <h4 className="font-medium mb-4 text-sm">Customization Options</h4>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 text-sm font-medium text-gray-600 -mb-2">
                    <div className="row gap-4">
                      {/* <p className=" opacity-0  w-8 text-center">hi</p> */}
                      <div className="text-primary font-semibold w-14 text-center">Option</div>
                    </div>
                    <div className="justify-end row">
                      <div className="row gap-4">
                        <div className="text-primary font-semibold w-14 text-end">Time</div>
                        <div className="text-primary font-semibold w-14 text-center">Cost</div>
                        <p className=" opacity-0  w-8 text-center">hi</p>
                      </div>
                    </div>
                  </div>

                  {order.serviceDetails?.customizations
                    ?.filter((option) => order.selectedCustomizations?.includes(option.id))
                    .map((option, index) => (
                      <div key={option.id} className="grid grid-cols-2 ">
                        <div className="row  gap-3">
                          <div>
                            {/* Display actual customization title with fallback */}
                            {option.title || `Option ${index + 1}`}
                          </div>
                        </div>

                        <div className="justify-end row">
                          <div className="row text-center gap-4">
                            <div>+{parseFloat(option.duration || "0").toFixed(0)}d</div>
                            <div>
                              +{currencySymbol}
                              {option?.price !== undefined
                                ? Number(option?.price).toFixed(2)
                                : "0.00"}
                            </div>
                            <div className="flex justify-end">
                              <button
                                className="p-1 rounded-full"
                                onClick={() => handleInfoClick(option)}
                              >
                                <Info size={16} className="text-gray-400" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          )}
          <div className="py-2 space-y-3 ">
            <div className="flex justify-between text-sm">
              <span>Subtotal</span>
              <span className="font-medium">
                {currencySymbol}
                {subtotal.toFixed(2)}
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span>Transaction fee (4%)</span>
              <span className="font-medium">
                {currencySymbol}
                {transactionFee.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between font-semibold pt-2 border-t">
              <span className="text-sm">Order total</span>
              <span className="font-medium">
                {currencySymbol}
                {orderTotal.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </section>

      <CustomizationDetailDrawer
        isOpen={isDetailOpen}
        onOpenChange={setIsDetailOpen}
        selectedOption={selectedOption}
        currencySymbol={currencySymbol}
        currencyCode={currencyCode}
      />
    </div>
  );
};

export default OrderDetailsTab;
