import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../firebaseConfig';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sellerId = searchParams.get('sellerId');

    // Initialize Firebase and get Firestore instance
    const { db } = await initFirebase();

    if (sellerId) {
      // Get specific seller
      const sellerRef = doc(db, 'sellers', sellerId);
      const sellerDoc = await getDoc(sellerRef);
      
      if (!sellerDoc.exists()) {
        return NextResponse.json({
          error: 'Seller not found'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        seller: {
          id: sellerDoc.id,
          ...sellerDoc.data()
        }
      });
    } else {
      // Get all sellers
      const sellersRef = collection(db, 'sellers');
      const sellersSnapshot = await getDocs(sellersRef);
      
      const sellers = sellersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return NextResponse.json({
        success: true,
        sellers,
        count: sellers.length
      });
    }

  } catch (error) {
    console.error('Error fetching sellers:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to fetch sellers',
      details: errorMessage
    }, { status: 500 });
  }
}
