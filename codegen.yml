# schema: "https://api-v2.lens.dev"
# schema: "https://api.lens.xyz/graphql"
# documents: "src/graphql/*.graphql"

# 
schema: "https://api.testnet.lens.xyz/graphql"
# schema: "https://api.testnet.lens.dev/graphql"
documents: "src/graphql/test/documents/**/*.graphql"
generates:
  # src/graphql/generated.ts:
  src/graphql/test/generated.ts:
    plugins:
      - typescript
      - typescript-operations
      - typescript-react-query
      - fragment-matcher
    config:
      dedupeFragments: true
      fetcher:
        func: "./auth-fetcher#fetcher"
        isReactHook: false
overwrite: true
      