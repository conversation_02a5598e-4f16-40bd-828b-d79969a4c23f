"use client";
import { TabsContent } from "@/components/ui/tabs";
import PostsHome from "./posts";
import ProfileHome from "./profiles";
import EventsHome from "./events";
import ServicesHome from "./services";
import * as Tabs from "@radix-ui/react-tabs";

type WebHomeProps = {
  activeBgColor?: string;
};

export function WebHome({ activeBgColor = "#454545" }: WebHomeProps) {
  return (
    <Tabs.Root
      defaultValue="Posts"
      className="overflow-hidden h-full hide-scroll max-md:overflow-x-hidden max-md:hidden"
    >
      <div className="sticky top-0 bg-white z-50 w-full px-2">
        <Tabs.List
          className="TabsListBg w-[450px] max-md:w-full"
          aria-label="Manage your account"
          style={
            {
              "--active-bg-color": activeBgColor,
            } as React.CSSProperties
          }
        >
          <Tabs.Trigger
            className="TabsTriggerBg text-xl"
            value="Posts"
            style={{}}
          >
            Posts
          </Tabs.Trigger>
          <Tabs.Trigger className="TabsTriggerBg text-xl" value="Services">
            Services
          </Tabs.Trigger>
          <Tabs.Trigger className="TabsTriggerBg text-xl" value="Events">
            Events
          </Tabs.Trigger>
          <Tabs.Trigger className="TabsTriggerBg text-xl" value="Profiles">
            Profiles
          </Tabs.Trigger>
        </Tabs.List>
      </div>

      <div className="overflow-scroll h-full hide-scroll">
        <TabsContent
          value="Posts"
          className="w-full"
          style={{ background: "white", width: "100%" }}
        >
          <PostsHome />
        </TabsContent>
        <TabsContent value="Services">
          <ServicesHome />
        </TabsContent>
        <TabsContent value="Events">
          <EventsHome />
        </TabsContent>
        <TabsContent value="Profiles">
          <ProfileHome />
        </TabsContent>
      </div>
    </Tabs.Root>
  );
}

export default WebHome;
