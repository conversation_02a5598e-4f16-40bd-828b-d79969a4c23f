'use client'

import { useSearchParams, useRouter } from 'next/navigation'
import { Suspense } from 'react'
import CustomPaymentForm from '@/components/CustomPaymentForm'

function CheckoutContent() {
  const searchParams = useSearchParams()
  const router = useRouter()

  // Get checkout data from URL parameters or localStorage
  const getCheckoutData = () => {
    const urlData = {
      amount: searchParams.get('amount'),
      currency: searchParams.get('currency'),
      productName: searchParams.get('productName'),
      productImage: searchParams.get('productImage'),
    }

    // Try localStorage as fallback
    const storedData = typeof window !== 'undefined' 
      ? JSON.parse(localStorage.getItem('checkoutData') || '{}')
      : {}

    return {
      amount: parseInt(urlData.amount || storedData.amount || '6500'),
      currency: urlData.currency || storedData.currency || 'usd',
      productName: urlData.productName || storedData.productName || 'Pure set',
      productImage: urlData.productImage || storedData.productImage || '/api/placeholder/300/300',
    }
  }

  const checkoutData = getCheckoutData()

  const handleSuccess = (paymentIntent) => {
    console.log('Payment successful:', paymentIntent)
    
    // Redirect to success page
    router.push(`/checkout/return?payment_intent=${paymentIntent.id}&payment_intent_client_secret=${paymentIntent.client_secret}`)
  }

  const handleError = (error) => {
    console.error('Payment error:', error)
    // You could show an error message here
  }

  return (
    <CustomPaymentForm
      amount={checkoutData.amount}
      currency={checkoutData.currency}
      productName={checkoutData.productName}
      productImage={checkoutData.productImage}
      onSuccess={handleSuccess}
      onError={handleError}
    />
  )
}

export default function CustomCheckoutPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout...</p>
        </div>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  )
}
