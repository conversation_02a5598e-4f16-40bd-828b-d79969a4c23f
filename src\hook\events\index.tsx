import { useEffect, useState, useCallback } from "react";
import { getEventById } from "@/services/eventsServices";
import useProfile from "../profileData";

const useEvent = (eventId: string, refreshTrigger = 0) => {
  const [eventData, setEventData]: any = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true); // Set loading to true initially
  const [error, setError] = useState<string | null>(null);

  const { profileData } = useProfile(eventId);

  // Define fetchAllServices as a useCallback to allow it to be called from outside the effect
  const fetchAllServices = useCallback(async () => {
    try {
      setLoading(true); // Start loading

      // Check if profileData exists and has events
      if (profileData?.events && profileData.events.length > 0) {
        const fetchedServices = await Promise.all(
          profileData.events.map(async (serviceId: string) => {
            const response = await getEventById(serviceId);
            return response?.success ? response.event : null;
          })
        );

        // Filter out invalid services
        const validServices = fetchedServices.filter(
          (service) => service !== null
        );
        setEventData(validServices);
      } else {
        // If no events, set empty array
        setEventData([]);
      }
    } catch (err) {
      setError("Failed to fetch events");
      // Set empty array on error
      setEventData([]);
    } finally {
      // Add a small delay before setting loading to false to ensure UI transitions properly
      setTimeout(() => {
        setLoading(false); // Stop loading
      }, 300);
    }
  }, [profileData?.events]);

  // Effect to fetch events when dependencies change
  useEffect(() => {
    // Always set loading to true when dependencies change
    setLoading(true);

    // Only fetch if we have a valid eventId
    if (eventId) {
      fetchAllServices();
    } else {
      // If no eventId, set empty data and stop loading
      setEventData([]);
      setLoading(false);
    }
  }, [fetchAllServices, refreshTrigger, eventId]); // Added refreshTrigger and eventId to dependencies

  return { eventData, loading, error, refetch: fetchAllServices };
};

export default useEvent;
