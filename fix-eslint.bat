@echo off
echo Starting ESLint fix process...

cd /d d:\Work\amuzn-webapp

echo Fixing ESLint issues in src/app directory...
npx eslint src/app --fix

echo Fixing ESLint issues in src/components directory...
npx eslint src/components --fix

echo Fixing ESLint issues in src/globalComponents directory...
npx eslint src/globalComponents --fix

echo Fixing ESLint issues in src/hook directory...
npx eslint src/hook --fix

echo Fixing ESLint issues in src/lib directory...
npx eslint src/lib --fix

echo Fixing ESLint issues in src/screens directory...
npx eslint src/screens --fix

echo Fixing ESLint issues in src/services directory...
npx eslint src/services --fix

echo Fixing ESLint issues in src/utils directory...
npx eslint src/utils --fix

echo ESLint fix process completed!
