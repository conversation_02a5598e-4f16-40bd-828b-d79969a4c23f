"use client";
import { Edit2, <PERSON>, X } from "react-feather";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Modal, ModalBody, ModalContent, Tooltip } from "@heroui/react";

const ProfileInfoAbout = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isToggle, setIsToggle] = useState(false);
  const [about, setAbout]: any = useState(props.about);

  // Update local state when props change
  useEffect(() => {
    if (isOpen) {
      setAbout(props.about || "");
    }
  }, [isOpen, props.about]);

  const handleSubmit = async () => {
    if (about) {
      // First update parent's feed state
      props.setFeed(about);

      // Then trigger the parent's submit function
      props.onClickAction();

      // Close the dialog
      // setIsOpen(false);
    }
  };

  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <p
                className={props.isToggle ? "text-primary line-clamp-2" : "text-primary"}
                onClick={(e) => {
                  // Only execute the toggle function if on mobile view (screen width <= 768px)
                  // Don't toggle when inside a modal or when an input is focused
                  if (
                    window.innerWidth <= 768 &&
                    !isOpen &&
                    !document.activeElement?.tagName.match(/input|textarea/i)
                  ) {
                    props.setIsToggle(!props.isToggle);
                  }
                }}
              >
                {props?.about ? (
                  <div
                    className={
                      props.isToggle
                        ? "text-primary line-clamp-2 whitespace-pre-line cursor-pointer"
                        : "whitespace-pre-line cursor-pointer"
                    }
                  >
                    <span className="font-bold max-md:text-sm text-nowrap">About me:</span>{" "}
                    {props?.about}
                  </div>
                ) : (
                  <span className="font-bold">About me*</span>
                )}
              </p>
            </div>
            <div className="row gap-3 ">
              {props.isOtherProfile && (
                <div onClick={() => setIsOpen(true)} className=" cursor-pointer">
                  <Edit2 color={props.bgColor} />
                </div>
              )}
              <Triangle
                size={18}
                className={
                  props.isToggle
                    ? "fill-primary rotate-180 cursor-pointer md:hidden"
                    : "fill-primary cursor-pointer md:hidden"
                }
                onClick={() => {
                  // Only toggle if no input is focused
                  if (!document.activeElement?.tagName.match(/input|textarea/i)) {
                    props.setIsToggle(!props.isToggle);
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* About Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={setIsOpen}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-between">
                    <div onClick={() => setIsOpen(false)} className=" cursor-pointer">
                      <X />
                    </div>
                    <p className="font-bold text-primary">Edit About Me </p>
                    <p
                      className={
                        about
                          ? "font-bold text-primary cursor-pointer"
                          : "font-bold text-borderColor cursor-not-allowed"
                      }
                      onClick={about && handleSubmit}
                    >
                      Save
                    </p>
                  </div>

                  <div>
                    <div className="grid w-full items-center gap-1.5 mt-6">
                      <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                        About me*
                      </Label>
                      <Textarea
                        placeholder="Tell the world about you and your services"
                        className="resize-none h-40 outline-none text-lg text-primary"
                        //   {...field}
                        value={about}
                        onChange={(e) => setAbout(e.target.value)}
                      />
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfoAbout;
