import { useState, useEffect } from "react";
import { getLensProfilesById } from "@/services/lensService";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  MainContentFocus,
  PageSize,
  PostType,
  useAccountsBulkQuery,
  useFollowingQuery,
  usePostsQuery,
} from "@/graphql/test/generated";
import { getId } from "@/services/authBridgeService";
import useAuth from "@/hook";

const useLensData = (category: string) => {
  const user = useAuth();
  const [profiles, setProfiles] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  const [profilesmy, setProfilesMy] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  const [profilesIds, setProfilesIds] = useState<string[]>([]);
  const [currentCursor, setCurrentCursor] = useState<string | null>(null);

  // Fetch profiles by category
  useEffect(() => {
    const fetchLensProfilesByCategory = async () => {
      const resp = await getLensProfilesById(category);
      const lensProfiles: Array<{
        localName: string;
      }> = resp?.lens_ids?.map((curr: any) => {
        return {
          localName: curr,
        };
      });
      setProfiles(lensProfiles);
    };

    fetchLensProfilesByCategory();
  }, [category]);

  // Fetch profile data
  // const {
  //   data: profileData,
  //   isLoading: loadingProfile,
  //   error: profileError,
  // } =
  // useProfilesQuery({
  //   request: {
  //     where: {
  //       handles: profiles,
  //     },
  //   },
  // });

  const {
    data: profileData,
    isLoading: loadingProfile,
    error: profileError,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames: category == "my feed" ? profilesmy : profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: (category == "my feed" ? profilesmy.length > 0 : profiles.length > 0), // Only execute when usernames array has at least one element
    }
  );

  // Update profile IDs when profile data is available
  useEffect(() => {
    if (profileData) {
      const lensIds = profileData?.accountsBulk?.map(
        (curr: AccountsBulkQuery["accountsBulk"][0]) => curr.address
      );
      setProfilesIds(lensIds);
    }
  }, [profileData]);

  // Fetch publications based on profile IDs
  const { data: publicationsData, isLoading: loadingPublications, error: publicationsError } =
    // v3
    usePostsQuery({
      request: {
        pageSize: PageSize.Fifty,
        cursor: currentCursor,
        filter: {
          authors: profilesIds,
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
      },
    }, {
      refetchOnWindowFocus: false,
      enabled: profilesIds.length > 0, // Only execute query when profilesIds has at least one element
    });

  // Load next page function
  const loadNextPage = () => {
    const nextCursor = publicationsData?.posts.pageInfo.next;
    if (nextCursor) {
      setCurrentCursor(nextCursor);
    }
  };

  // my feed
  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [userId, setUserId] = useState("");
  // const containerRef = useRef<HTMLDivElement>(null);

  const getLensUserId = async (otherUserID: any) => {
    try {
      const resp = await getId({ id: user.userId });
      if (resp) {
        setUserId(resp?.lens_code);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  useEffect(() => {
    getLensUserId(user.userId);
  }, [user.userId]);

  const { data: following, isLoading: following_loading } = useFollowingQuery(
    {
      request: {
        account: userId, // address
        pageSize: PageSize.Ten, // Changed to smaller page size for better pagination
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!userId,
    }
  );

  interface FollowingItem {
    localName: any;
  }

  const [allFollowing, setAllFollowing] = useState<FollowingItem[]>([]);

  useEffect(() => {
    if (
      following?.following?.items &&
      following?.following?.items.length > 0 &&
      category == "my feed"
    ) {
      const newArray: FollowingItem[] = following.following.items.map((item) => ({
        localName: item.following.username?.localName,
      }));

      // Add new items, avoiding duplicates
      if (category == "my feed") {
        setAllFollowing((prev) => {
          const updatedFollowing = [...prev];

          newArray.forEach((newItem) => {
            // Check if this item already exists in the array
            if (
              !updatedFollowing.some((existingItem) => existingItem.localName === newItem.localName)
            ) {
              updatedFollowing.push(newItem);
            }
          });

          return updatedFollowing;
        });
      }

      // Add new profiles, avoiding duplicates
      setProfiles((prev) => {
        const updatedProfiles = [...prev];

        newArray.forEach((newItem) => {
          // Check if this item already exists in the array
          if (
            !updatedProfiles.some((existingItem) => existingItem.localName === newItem.localName)
          ) {
            updatedProfiles.push(newItem);
          }
        });

        return updatedProfiles;
      });
    }
  }, [following, category]);

  useEffect(() => {
    if (allFollowing.length > 0) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);

  return {
    publicationsData,
    profileData,
    loadingProfile,
    loadingPublications,
    profileError,
    publicationsError,
    loadNextPage,
  };
};

export default useLensData;
