"use client";
import { Edit2, X } from "react-feather";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { updateUser } from "@/services/usersServices";
const ProfileInfoPersonalMotto = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [personalMotto, setPersonalMotto]: any = useState();

  // fetch data from firebase
  const auth = useAuth();
  const profile = useProfile(auth?.userData?.uid);

  const handleSubmit = async () => {
    if (personalMotto) {
      const updatedData = {
        personal_moto: personalMotto,
      };
      const response = await updateUser(auth?.userData?.uid, updatedData);
      if (response.success) {
        // console.log(response);
        setPersonalMotto("");
        setIsOpen(false);
      } else {
        // console.error(response.error);
      }
    }
  };
  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              <span className="font-bold text-primary max-md:text-sm">Personal Motto </span>
              {/* <p className="text-primary">
                Most of these photos are developed and scanned at home by hand.
                Most of photos most of. labore et dolore magna aliqua.
              </p>
              <p className="text-primary">
                Ut enim ad minim veniam, quis nostrud exercitation ullamco
                laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
                dolor in reprehenderit in voluptate velit esse cillum dolore eu
                fugiat nulla pariatur.{" "}
              </p> */}
              <p className="text-primary max-md:text-sm">{props?.personalMotto}</p>
            </div>
          </div>
        </div>
      </div>
      {/* PersonalMotto Modal */}
      <div>
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogTrigger asChild>
            {/* Empty or hidden trigger since we're controlling externally */}
            <span style={{ display: "none" }}></span>
          </AlertDialogTrigger>
          <AlertDialogContent
            className="min-w-96 max-w-96 min-h-60 p-6 rounded-[30px]"
            style={{ borderRadius: "20px" }}
          >
            <AlertDialogHeader>
              <AlertDialogDescription>
                <div className="row justify-between">
                  <div onClick={() => setIsOpen(false)} className=" cursor-pointer">
                    <X />
                  </div>
                  <p className="font-bold text-primary">Edit Personal Motto</p>
                  <p
                    className={
                      personalMotto
                        ? "font-bold text-primary cursor-pointer"
                        : "font-bold text-borderColor cursor-not-allowed"
                    }
                    onClick={personalMotto && handleSubmit}
                  >
                    Save
                  </p>
                </div>

                <div>
                  <div className="grid w-full items-center gap-1.5 mt-6">
                    <Label htmlFor="email" className="text-base font-[600] text-titleLabel">
                      Personal Motto
                    </Label>
                    <Textarea
                      placeholder="Tell the world about you and your services"
                      className="resize-none h-40 outline-none text-lg text-primary"
                      //   {...field}
                      value={personalMotto}
                      onChange={(e) => setPersonalMotto(e.target.value)}
                    />
                  </div>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};

export default ProfileInfoPersonalMotto;
