"use client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@radix-ui/react-dropdown-menu";
import { useState } from "react";
import { ChevronLeft, DollarSign, Info } from "react-feather";
import EditCustomizationService from "./editCustomizations";

const EditService = ({ onSelectService }: any) => {
  const [isTrue, setIsTrue] = useState(true);

  const handleSelectService = ({ id }: any) => {
    setIsTrue(!isTrue);
  };

  return (
    <>
      {isTrue ? (
        <div>
          <div className="">
            {/* header */}
            <div className=" w-full bg-white sticky top-[6.2rem] pt-3">
              <div className="row gap-3 justify-between">
                <div
                  onClick={() => isTrue && onSelectService(1)}
                  className=" cursor-pointer row"
                >
                  <ChevronLeft />
                  <p className="pl-[2px]">Back</p>
                </div>
                <p className="text-titleLabel text-lg font-bold">
                  Edit Service
                </p>
                <p>Save</p>
              </div>
            </div>
            <div className="flex flex-row w-full overflow-scroll gap-3 hide-scroll-custom  bg-white h-[calc(100vh-300px)] max-md:h-[calc(100vh-150px)] pt-4 pb-16 px-3">
              <div>
                <p className="text-primary font-[600]">Choose category</p>
                <div className="row gap-3">
                  <Button className="text-white">Music</Button>
                  <Button className=" bg-[#F2F2F2] text-primary shadow-none hover:text-white">
                    Storytelling
                  </Button>
                </div>
                <div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Title
                    </Label>
                    <Input
                      placeholder="Title"
                      className="resize-none h-[40px] outline-none text-lg text-primary"
                    />
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Descriptions
                    </Label>
                    <Textarea
                      placeholder="Descriptions"
                      className="resize-none h-40 outline-none text-lg text-primary"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-x-4 ">
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Service Cost
                    </Label>
                    <p>(you will receive)</p>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>$</p>
                    </div>
                    <p>
                      By Seller. VAT inclusive.
                      {"                                   "}
                    </p>
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      List Price
                    </Label>
                    <p>(you will receive)</p>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>$</p>
                    </div>
                    <p className="line-clamp-2">
                      **Includes AMUZ App Fee (16% of List Price)
                    </p>
                  </div>

                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Time
                    </Label>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>hours</p>
                    </div>
                  </div>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label
                      //   htmlFor="email"
                      className="text-base font-[600] text-titleLabel"
                    >
                      Time
                    </Label>
                    <div className="row gap-2">
                      <Input
                        placeholder="Service Cost "
                        className="resize-none h-[40px] outline-none text-lg text-primary"
                      />
                      <p>days</p>
                    </div>
                  </div>
                </div>

                <p className="mt-3">8 hours = 1 day</p>
                <p className="mt-3">
                  The service proposed deivery date shall not exceed 2 weeks
                  (112hours).
                </p>
                <p className="mt-3">
                  The service delivery due date, based on above hour, will
                  appear in ‘My Order’ page detailsafter order obtaining
                  ACCEPTED status.
                </p>
                <p className="my-3 text-primary text-xl font-bold">
                  Customization
                </p>

                <div className="grid grid-cols-2">
                  <div>
                    <p className="text-primary font-bold">Option</p>
                  </div>
                  <div className="justify-end row">
                    <div className="row gap-4">
                      <p className="text-primary font-bold w-12 text-center">
                        Time
                      </p>
                      <p className="text-primary font-bold  w-16 text-center">
                        Cost
                      </p>
                      <p className=" opacity-0  w-8 text-center">hi</p>
                    </div>
                  </div>
                </div>
                {Array.from({ length: 4 }).map((_, indexs) => (
                  <div className="grid grid-cols-2 mt-2">
                    <div>
                      <p className="text-subtitle">
                        Create a track for 5 minutes.
                      </p>
                    </div>
                    <div className=" justify-end row">
                      <div className="row gap-4">
                        <p className="text-subtitle w-12 text-center ">1d</p>
                        <p className="text-subtitle  w-16 text-center">
                          $10.00
                        </p>
                        <Info
                          className="text-borderColor  w-8 text-center"
                          size={21}
                        />
                      </div>
                    </div>
                  </div>
                ))}

                <div className="row justify-center mt-5">
                  <Badge
                    className=" btn-xs w-full py-4 border-primary btn text-white"
                    // variant="outline"
                    onClick={() => handleSelectService(1)}
                  >
                    Edit Customizations (3)
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <EditCustomizationService
          onSelectService={() => handleSelectService(1)}
        />
      )}
    </>
  );
};

export default EditService;
