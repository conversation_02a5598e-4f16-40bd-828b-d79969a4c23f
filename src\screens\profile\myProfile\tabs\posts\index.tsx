import { useState, useEffect } from "react";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getPostsByUserSorted, Post } from "@/services/postService";
import ImageGrid from "./imageGrid";
import PostCardSkeleton from "@/components/CardSkeleton/PostCardSkeleton";
import EmptyState from "@/components/EmptyState";

const PostsMyProfile = (props: any) => {
  // Fetch data from firebase
  const auth = useAuth();
  const profile = useProfile(props.isOtherProfile ? props.otherUserID : auth?.userData?.uid);

  // State to store fetched post images
  const [postImages, setPostImages] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch posts when profile data changes
  useEffect(() => {
    // Flag to track if component is mounted
    let isMounted = true;

    // Always start with loading state
    setIsLoading(true);

    // Don't attempt to fetch until we have a user ID
    const userId = props.isOtherProfile ? props.otherUserID : auth?.userData?.uid;
    if (!userId) {
      return () => {
        isMounted = false;
      };
    }

    const fetchAllPosts = async () => {
      try {
        // Fetch all posts using the new service
        const posts = await getPostsByUserSorted({
          userId: userId,
        });

        // Only update state if component is still mounted
        if (!isMounted) return;

        if (posts && posts.length > 0) {
          // Apply category filtering if a category is selected
          if (props.activePostCategory) {
            setPostImages(posts.filter((post) => post?.category === props.activePostCategory));
          } else {
            setPostImages(posts);
          }

          // Ensure we have a minimum loading time for better UX
          setTimeout(() => {
            if (isMounted) setIsLoading(false);
          }, 1000);
        } else {
          // If no posts, keep loading for a bit longer then update
          setTimeout(() => {
            if (isMounted) {
              setPostImages([]);
              setIsLoading(false);
            }
          }, 1000);
        }
      } catch (error) {
        console.error("Error fetching posts:", error);
        // On error, wait a bit then show no posts
        setTimeout(() => {
          if (isMounted) {
            setPostImages([]);
            setIsLoading(false);
          }
        }, 1000);
      }
    };

    // Start fetching data
    fetchAllPosts();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [
    auth?.userData?.uid,
    props.otherUserID,
    props.isOtherProfile,
    props.activePostCategory,
    props.isRefatch,
  ]);

  // Helper function to split images into chunks of 5
  const chunkArray = (array: Post[], chunkSize: number) => {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  };

  const imageChunks = chunkArray(postImages, 5);

  return (
    <div className="w-full overflow-y-scroll gap-3 hide-scroll-custom py-4">
      {isLoading ? (
        <div className="">
          <PostCardSkeleton count={2} />
        </div>
      ) : imageChunks.length > 0 ? (
        <div className="grid grid-cols-1 gap-[0px] max-lg:grid-cols-1">
          {imageChunks.map((chunk: any, chunkIndex: any) => (
            <div key={chunkIndex} className=" ">
              <ImageGrid chunk={chunk} borderColor="#000" chunkIndex={chunkIndex} category="Art" />
            </div>
          ))}
        </div>
      ) : (
        <EmptyState
          type="posts"
          title="No Posts Yet"
          message={
            props.isOtherProfile
              ? "This user hasn't created any posts yet."
              : "Posts you create will appear here"
          }
          isOwnProfile={!props.isOtherProfile}
          actionLabel={!props.isOtherProfile ? "Create Post" : undefined}
          onAction={
            !props.isOtherProfile ? () => props.setIsOpen && props.setIsOpen(true) : undefined
          }
        />
      )}
    </div>
  );
};

export default PostsMyProfile;
