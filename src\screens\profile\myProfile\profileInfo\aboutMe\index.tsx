"use client";
import { <PERSON>, Edit2, <PERSON><PERSON>, <PERSON>, <PERSON> } from "react-feather";
import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { updateUser } from "@/services/usersServices";
import useAuth from "@/hook";
import { <PERSON>dal, ModalBody, ModalContent } from "@heroui/react";

const ProfileInfoAbout = (props: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [about, setAbout]: any = useState(props?.about);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success">("idle");

  // fetch data from firebase
  const auth = useAuth();

  // No longer using useEffect to handle success state
  // Now handling it directly in the handleSubmit function

  const handleSubmit = async () => {
    if (about) {
      setUploadStatus("uploading");

      try {
        const updatedData = {
          about_me: about,
        };
        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          setUploadStatus("success");

          // Set a timeout to close the modal and refresh the data
          setTimeout(() => {
            // Trigger parent component to refresh data
            props.onClickAction(true);

            // Close the modal
            setIsOpen(false);

            // Reset states
            setAbout("");
            setUploadStatus("idle");
          }, 1500);

          // Force close the modal after a longer delay if it's still open
          setTimeout(() => {
            setIsOpen(false);
          }, 2000);
        } else {
          console.error("Error updating About Me:", response.error);
          setUploadStatus("idle");
        }
      } catch (error) {
        console.error("Error updating About Me:", error);
        setUploadStatus("idle");
      }
    }
  };
  return (
    <>
      <div>
        <div className="mt-4">
          <div className="flex flex-row gap-4 items-start justify-between">
            <div>
              {props?.about ? (
                <div
                  className="max-md:text-sm flex"
                  onClick={(e) => {
                    // Only execute the toggle function if on mobile view (screen width <= 768px)
                    // Don't toggle when inside a modal or when an input is focused
                    if (
                      window.innerWidth <= 768 &&
                      !isOpen &&
                      !document.activeElement?.tagName.match(/input|textarea/i)
                    ) {
                      props.setIsToggle(!props.isToggle);
                    }
                  }}
                >
                  <div
                    className={
                      props.isToggle
                        ? "text-primary line-clamp-2 whitespace-pre-line cursor-pointer"
                        : "whitespace-pre-line cursor-pointer"
                    }
                  >
                    <span className="font-bold max-md:text-sm text-nowrap">About me:</span>{" "}
                    {props?.about}
                  </div>
                </div>
              ) : (
                <span className="font-bold max-md:text-sm">About me*</span>
              )}
            </div>
            <div className="row gap-3 ">
              {!props.isOtherProfile && (
                <div
                  onClick={() => {
                    setIsOpen(true), setAbout(props?.about);
                  }}
                  className=" cursor-pointer"
                >
                  <Edit2 color={props.bgColor} className="max-md:h-[20px]" />
                </div>
              )}
              <Triangle
                size={18}
                className={
                  props.isToggle
                    ? "fill-primary rotate-180 cursor-pointer md:hidden"
                    : "fill-primary cursor-pointer md:hidden"
                }
                onClick={() => {
                  // Only toggle if no input is focused
                  if (!document.activeElement?.tagName.match(/input|textarea/i)) {
                    props.setIsToggle(!props.isToggle);
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* About Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={(open) => {
            // Always allow closing the modal
            setIsOpen(open);
          }}
          hideCloseButton={true}
          size="2xl"
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  {uploadStatus === "success" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-green-100 rounded-full p-4 mb-4">
                        <Check size={48} className="text-green-500" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        About Me Updated Successfully!
                      </h2>
                      <p className="text-gray-500 text-center">
                        Your profile information has been updated and the changes will be visible on
                        your profile.
                      </p>
                    </div>
                  ) : uploadStatus === "uploading" ? (
                    <div className="flex flex-col items-center justify-center py-10">
                      <div className="bg-blue-50 rounded-full p-4 mb-4">
                        <Loader size={48} className="text-primary animate-spin" />
                      </div>
                      <h2 className="text-xl font-bold mb-2 text-center">
                        Updating Your Profile...
                      </h2>
                      <p className="text-gray-500 text-center">
                        Please wait while we update your information. This may take a moment.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div className="row justify-between">
                        <div onClick={() => setIsOpen(false)} className="cursor-pointer">
                          <X />
                        </div>
                        <p className="font-bold text-primary">Edit About Me</p>
                        <div
                          className={`flex items-center ${
                            about
                              ? "text-primary cursor-pointer"
                              : "text-borderColor cursor-not-allowed"
                          }`}
                          onClick={about ? handleSubmit : undefined}
                        >
                          Save
                        </div>
                      </div>

                      <div>
                        <div className="grid w-full items-center gap-1.5 mt-6">
                          <Label
                            htmlFor="email"
                            className="text-base font-[600] text-titleLabel max-md:text-start"
                          >
                            About me*
                          </Label>
                          <Textarea
                            placeholder="Tell the world about you and your services"
                            className="resize-none h-60 outline-none text-lg text-primary"
                            value={about}
                            onChange={(e) => setAbout(e.target.value)}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default ProfileInfoAbout;
