"use client";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import useAuth from "@/hook";
import { createService, Service } from "@/services/serviceService";
import { updateUser } from "@/services/usersServices";
import { useState } from "react";
import { arrayUnion, Timestamp } from "firebase/firestore";

const CreateService = () => {
  const auth = useAuth();
  const [title, setTitle]: any = useState<string | null>(null);
  const [category, setCategory]: any = useState<string | null>(null);
  const [about, setAbout] = useState("");
  const [serviceCost, setServiceCost]: any = useState<number | null>(null);
  const [listPrice, setListPrice] = useState<number | null>(null);
  const [time, setTime]: any = useState<string | null>(null);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [geoTags, setGeoTags] = useState<string[]>([]);
  const [media, setMedia] = useState<File | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData: Service = {
      id: auth?.userData?.uid,
      title,
      category,
      // about,
      price: serviceCost,
      // listPrice,
      duration: time,
      description: about,
    };
    // console.log(formData);
    // Add your API call or processing logic here
    //  console.log("New Post Created:", post);

    try {
      // Call createPost for the single post
      const response = await createService(formData);

      // Check if the post was created successfully
      if (response.success) {
        // setPosts((prevPosts: any) => [...prevPosts, { ...response, ...post }]);
        // console.log("Service created successfully:", response);
        const postId = response.id; // Assuming `response.id` contains the created post's ID
        const userId = auth?.userData?.uid; // Assuming `auth.userData.uid` contains the current user's ID

        if (userId && postId) {
          await updateUser(userId, { services: arrayUnion(postId) });
          console.log("User's posts array updated successfully.");
        }
      } else {
        // console.error("Error creating post:", response);
      }
    } catch (error) {
      // console.error("Error creating post:", error);
    }
  };

  return (
    <>
      <p
        onClick={handleSubmit}
        className={
          false
            ? "font-bold text-primary "
            : "font-bold text-borderColor absolute top-6 right-6"
        }
      >
        Save
      </p>
      <div className=" bg-gray-50">
        <div className="max-w-md mx-auto bg-white  rounded-md p-2 py-4">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Choose Category */}
            <div>
              <p className="text-primary mb-2 font-[600] text-start">
                Choose category
              </p>
              <div className="flex space-x-2">
                {["Music", "Story-Telling"].map((cat) => (
                  <button
                    key={cat}
                    type="button"
                    onClick={() => setCategory(cat)}
                    className={`px-4 py-2 rounded-md ${
                      category === cat
                        ? "bg-primary text-white"
                        : "bg-[#EEEEEE]  text-primary"
                    }`}
                  >
                    {cat}
                  </button>
                ))}
              </div>
            </div>

            {/* About Project */}
            <div className="grid w-full md:max-full items-center gap-1.5 mt-6 max-md:text-start">
              <Label
                htmlFor="email"
                className="text-primary mb-2 font-[600] block text-start"
              >
                Title
              </Label>
              <Input
                type="text"
                id="email"
                placeholder="Service name"
                className="text-primary h-10"
                value={title || ""}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>

            <div>
              <label
                htmlFor="about"
                className="text-primary mb-2 font-[600] block text-start"
              >
                Description
              </label>
              <textarea
                id="about"
                value={about}
                onChange={(e) => setAbout(e.target.value)}
                placeholder="Tell the world about your service"
                className="w-full p-2 border border-gray-300 rounded-md"
              ></textarea>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid w-full md:max-full items-center gap-1.5 mt-3 max-md:text-start">
                <Label
                  htmlFor="serviceCost"
                  className="text-primary mb-2 font-[600] block text-start"
                >
                  Service Cost{" "}
                  <span className="font-thin">(you will receive)</span>
                </Label>
                <div className="row gap-3">
                  <Input
                    type="number"
                    id="serviceCost"
                    className="text-primary h-10 max-w-20"
                    value={serviceCost || ""}
                    onChange={(e) => setServiceCost(Number(e.target.value))}
                  />
                  <p>$</p>
                </div>
                <p className="text-sm text-gray-500 opacity-0">.</p>
              </div>{" "}
              <div className="grid w-full md:max-full items-center gap-1.5 mt-3 max-md:text-start">
                <Label
                  htmlFor="listPrice"
                  className="text-primary mb-2 font-[600] block text-start"
                >
                  List Price <span className="font-thin">(user will pay)</span>
                </Label>
                <div className="row gap-3">
                  <Input
                    type="number"
                    id="listPrice"
                    className="text-primary h-10 max-w-20"
                    value={listPrice || ""}
                    onChange={(e) => setListPrice(Number(e.target.value))}
                  />
                  <p>$</p>
                </div>
                <p className="text-sm text-gray-500">Inc. 16% service fee</p>
              </div>
            </div>

            <div className="text-black text-base text-start mt-4">
              Pricing for individual products and services should start at US
              $25 and not exceed US $2,500
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid w-full md:max-full items-center gap-1.5 mt-3 max-md:text-start">
                <Label
                  htmlFor="time"
                  className="text-primary mb-2 font-[600] block text-start"
                >
                  Time
                </Label>
                <div className="row gap-3">
                  <Input
                    type="text"
                    id="time"
                    className="text-primary h-10 max-w-20"
                    value={time || ""}
                    onChange={(e) => setTime(e.target.value)}
                  />
                  <p>hours</p>
                </div>
                <p className="text-sm text-gray-500">8 hours = 1 day</p>
              </div>
            </div>

            <div className="text-black text-base text-start mt-2">
              The service needs to be delivered by the date specified, not to
              exceed 2 weeks from Order placement
            </div>

            <div>
              <p className="text-xl text-primary font-bold">Customizations</p>
              <div className="row justify-center mt-5">
                <Badge className=" btn-xs w-full py-4 border-primary btn text-white">
                  Set Customization
                </Badge>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default CreateService;
