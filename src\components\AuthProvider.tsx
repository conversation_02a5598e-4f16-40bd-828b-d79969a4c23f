"use client";

import { useEffect } from "react";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { useRouter } from "next/navigation";
import { initFirebase } from "../../firebaseConfig";

export default function AuthWatcher({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  let auth: any = null;
  initFirebase().then((resp) => {
    auth = getAuth(resp.app);
    // console.log({ auth });
  });

  useEffect(() => {
    if (!auth) return;

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        // Session is invalid, clear cached data
        localStorage.removeItem("user");
        sessionStorage.removeItem("user");

        // Optionally redirect
        router.push("/");
      } else {
        // Save user to localStorage if needed
        localStorage.setItem("user", JSON.stringify(user));
      }
    });

    return () => unsubscribe();
  }, []);
  return <>{children}</>;
}
