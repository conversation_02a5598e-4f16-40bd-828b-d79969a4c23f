import {
  PublicationReportingFraudSubreason,
  PublicationReportingIllegalSubreason,
  PublicationReportingReason,
  PublicationReportingSensitiveSubreason,
  PublicationReportingSpamSubreason,
  useReportPublicationMutation,
} from "@/graphql/generated";
import useLogin from "./auth/useLogin";
import { readAccessToken } from "./auth/auth-helpers";
import {
  PostReportReason,
  useReportPostMutation,
} from "@/graphql/test/generated";
import { useAccount } from "wagmi";

export function useReport() {
  const { mutateAsync: requestTypedData } = useReportPostMutation();

  const { address , isConnected , } = useAccount();

  const { mutateAsync: loginUser }: any = useLogin();

  async function report(
    postId: string,
    additionalComments: string,
    // _reason: PostReportReason,
    _reason: PostReportReason,
    _subreason:
      | PublicationReportingSpamSubreason
      | PublicationReportingIllegalSubreason
      | PublicationReportingFraudSubreason
      | PublicationReportingSensitiveSubreason
  ) {
    try {
      if (!address && !isConnected) {
        throw new Error("Wallet not connected");
      }

      // check if token exisst then only call this
      if (readAccessToken() === null || (!address && !isConnected)) {
        await loginUser(JSON.parse(localStorage.getItem("lens-user") || "{}")?.address);
      }

      let typedData;
      typedData = await requestTypedData({
        request: {
          post: postId,
          additionalComment: additionalComments,
          reason: _reason,
        },
      });
      return typedData;
    } catch (error) {
      // console.error("Follow error:", error);
      throw error;
    }
  }

  return report;
}
