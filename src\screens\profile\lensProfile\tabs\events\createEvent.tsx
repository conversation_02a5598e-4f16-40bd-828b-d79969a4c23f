"use client";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRef, useState } from "react";
import { FilePlus, Plus, Trash, X } from "react-feather";
import FileUploader from "../services/serviceDetails/fileUploader";
import { updateUser } from "@/services/usersServices";
import { arrayUnion, Timestamp } from "firebase/firestore";
import { createEvent, Event } from "@/services/eventsServices";
import useAuth from "@/hook";
const CreateEvent = (props: any) => {
  const auth = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const postsRef = useRef<HTMLDivElement>(null);
  const Category = "Music";

  const [title, setTitle]: any = useState<string | null>(null);
  const [category, setCategory] = useState<string | null>(null);
  const [about, setAbout] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData: Event = {
      id: auth?.userData?.uid,
      name: title,
      description: about,
      date: dateTime,
    };

    // handleLog();

    try {
      // Call createPost for the single post
      const response = await createEvent(formData);

      // Check if the post was created successfully
      if (response.success) {
        // setPosts((prevPosts: any) => [...prevPosts, { ...response, ...post }]);
        // console.log("Service created successfully:", response);
        const postId = response.id; // Assuming `response.id` contains the created post's ID
        const userId = auth?.userData?.uid; // Assuming `auth.userData.uid` contains the current user's ID

        if (userId && postId) {
          await updateUser(userId, { events: arrayUnion(postId) });
          // console.log("User's posts array updated successfully.");
          props.setIsOpen(false);
          props.setToggleMain((e: boolean) => !e);
        }
      } else {
        // console.error("Error creating post:", response);
      }
    } catch (error) {}
  };

  // for date and time picker

  const [date, setDate] = useState<string>("");
  const [time, setTime] = useState<string>("");
  const [dateTime, setDateTime] = useState<string>("");

  // Function to update date and time in the required format
  const updateDateTime = (selectedDate: string, selectedTime: string) => {
    if (selectedDate && selectedTime) {
      const [year, month, day] = selectedDate.split("-");
      const [hours, minutes] = selectedTime.split(":");

      // Convert month number to full month name
      const monthName = new Date(parseInt(year), parseInt(month) - 1).toLocaleString("default", {
        month: "long",
      });

      // Format date as "11 June 2023 10:00"
      const formattedDateTime = `${parseInt(day)} ${monthName} ${year} ${hours}:${minutes}`;

      setDateTime(formattedDateTime);
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDate(e.target.value);
    updateDateTime(e.target.value, time);
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTime(e.target.value);
    updateDateTime(date, e.target.value);
  };

  const handleLog = () => {
    if (dateTime) {
      console.log("Selected Date and Time:", dateTime);
    } else {
      console.log("No date and time selected!");
    }
  };
  return (
    <>
      <p
        onClick={handleSubmit}
        className={
          title && about && dateTime
            ? "font-bold text-primary absolute top-6 right-6 cursor-pointer"
            : "font-bold text-borderColor absolute top-6 right-6"
        }
      >
        Save
      </p>
      <div className="flex flex-row justify-between items-center mb-4">
        <div onClick={() => props.setIsOpen(false)} className="cursor-pointer">
          <X />
        </div>
        <h2 className="text-xl font-bold">Create Service</h2>
        <p
          onClick={handleSubmit}
          className={
            title && about && dateTime
              ? "font-bold text-primary cursor-pointer"
              : "font-bold text-borderColor cursor-not-allowed"
          }
        >
          Save
        </p>
      </div>
      <div className=" bg-gray-50">
        <div className="max-w-md mx-auto bg-white  rounded-md p-3 py-4">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Choose Category */}
            <div>
              <p className="text-primary mb-2 font-[600] text-start">Choose category</p>
              <div className="flex space-x-2">
                {props.category.map((cat: any, index: any) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => setCategory(cat)}
                    className={`px-4 py-2 rounded-md ${
                      category === cat ? "bg-primary text-white" : "bg-[#EEEEEE]  text-primary"
                    }`}
                  >
                    {cat}
                  </button>
                ))}
              </div>
            </div>

            {/* About Project */}

            <div className="grid w-full md:max-full items-center gap-1.5 mt-6 max-md:text-start">
              <Label htmlFor="email" className="text-primary mb-2 font-[600] block text-start">
                Event name
              </Label>
              <Input
                type="text"
                id="email"
                placeholder="Service name"
                className="text-primary h-10"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>

            <div>
              <label htmlFor="about" className="text-primary mb-2 font-[600] block text-start">
                Description
              </label>
              <textarea
                id="about"
                value={about}
                onChange={(e) => setAbout(e.target.value)}
                placeholder="Tell the world about your service"
                className="w-full p-2 border border-gray-300 rounded-md"
              ></textarea>
              {/* <p className="text-sm text-gray-500"></p> */}
            </div>

            <div>
              <label htmlFor="about" className="text-primary mb-2 font-[600] block text-start">
                date and time
              </label>
              <div className="flex flex-row gap-2">
                <input
                  type="date"
                  value={date}
                  onChange={handleDateChange}
                  className="border p-2 rounded"
                />
                <input
                  type="time"
                  value={time}
                  onChange={handleTimeChange}
                  className="border p-2 rounded"
                />
              </div>
            </div>

            <div></div>
          </form>
        </div>
      </div>
    </>
  );
};

export default CreateEvent;
