// import {
//   RefreshDocument,
//   RefreshMutation,
//   RefreshMutationVariables,
// } from "@/graphql/generated";
import { readAccessToken, setAccessToken } from "./auth-helpers";
import { LENS_API_URL } from "../constant";
import {
  RefreshDocument,
  RefreshMutation,
  RefreshMutationVariables,
} from "@/graphql/test/generated";

export default async function refreshAccessToken() {
  // 1. Get our current refresh token from local storage
  const currentRefreshToken = readAccessToken()?.refreshToken;
  // console.log({currentRefreshToken});

  if (!currentRefreshToken) return null;

  async function fetchData<TData, TVariables>(
    query: string,
    variables?: TVariables,
    options?: RequestInit["headers"]
  ): Promise<TData> {
    const res = await fetch(LENS_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...options,
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const json = await res.json();

    if (json.errors) {
      const { message } = json.errors[0] || {};
      throw new Error(message || "Error…");
    }

    return json.data;
  }

  // 3. set the new access token in local storage
  const result: RefreshMutation = await fetchData<
    RefreshMutation,
    RefreshMutationVariables
  >(RefreshDocument, {
    request: {
      refreshToken: currentRefreshToken,
    },
  });
  // console.log({ result });

  if (result.refresh.__typename === "AuthenticationTokens") {
    const {
      accessToken,
      idToken: identityToken,
      refreshToken: newRefreshToken,
    } = result.refresh;
    setAccessToken(accessToken, identityToken, newRefreshToken);
    return accessToken as string;
  }

  return null;
}
