import type { NextApiRequest, NextApiResponse } from 'next';
import { getStripeInstance, type StripeCreateAccountRequest } from '@/lib/stripe';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, isUS }: StripeCreateAccountRequest = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    const stripeService = getStripeInstance(isUS === 'true');

    const account = await stripeService.accounts.create({
      type: 'express',
      email,
    });

    res.status(200).json({
      account,
      success: true,
    });

  } catch (error) {
    console.error('Error creating account:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
}
